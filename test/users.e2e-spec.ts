import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import { UsersModule } from 'src/configuration/module/users.module';
import * as request from 'supertest';
import { User } from 'src/core/domain/User';
import { FindUserService } from 'src/core/application/services/user/findUser.service';
import { LoginDto } from 'src/entrypoint/dto/auth/login.dto';
import { AuthModule } from 'src/configuration/module/auth.module';

describe('UserController (e2e)', () => {
	let app: INestApplication;
	const findUserService = {
		findUserById: async (): Promise<User> =>
			new User(1, '<PERSON>', '<EMAIL>', '3'),
		findOneOrFail: async (): Promise<User> =>
			new User(1, '<PERSON>', '<EMAIL>', '3'),
	};
	let token: string;

	beforeEach(async () => {
		const moduleFixture: TestingModule = await Test.createTestingModule({
			imports: [UsersModule, AuthModule],
		})
			.overrideProvider(FindUserService.FindUserService)
			.useValue(findUserService)
			.compile();

		app = moduleFixture.createNestApplication();
		await app.init();

		const loginDto: LoginDto = {
			email: '<EMAIL>',
			password: 'teste123',
		};

		const response = await request(app.getHttpServer())
			.post('/v1/auth/login')
			.send(loginDto);

		token = response.body.token;
	});

	it('v1/user/{id} (GET) - should return a user', () => {
		return request(app.getHttpServer())
			.get('/v1/users/1')
			.set('Authorization', `Bearer ${token}`)
			.expect(200)
			.expect({ id: 1, name: 'John Doe', email: '<EMAIL>' });
	});
});
