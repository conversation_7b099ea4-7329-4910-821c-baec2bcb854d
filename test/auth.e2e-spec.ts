import { INestApplication } from '@nestjs/common';
import { Test, TestingModule } from '@nestjs/testing';
import * as request from 'supertest';
import { JwtService } from '@nestjs/jwt';
import { Auth } from 'src/core/domain/Auth';
import { AppModule } from 'src/configuration/module/app.module';
import { LoginDto } from 'src/entrypoint/dto/auth/login.dto';

describe('AuthController (e2e)', () => {
	let app: INestApplication;
	let jwtService: JwtService;

	beforeAll(async () => {
		const moduleFixture: TestingModule = await Test.createTestingModule({
			imports: [AppModule],
		}).compile();

		app = moduleFixture.createNestApplication();
		await app.init();

		jwtService = moduleFixture.get<JwtService>(JwtService);
	});

	afterAll(async () => {
		if (app) {
			await app.close();
		}
	});

	describe('/v1/auth/login (POST)', () => {
		it('deve autenticar o usuário e retornar um token válido', async () => {
			const loginDto: LoginDto = {
				email: '<EMAIL>',
				password: 'teste123',
			};

			const response = await request(app.getHttpServer())
				.post('/v1/auth/login')
				.send(loginDto)
				.expect(201);

			const authData: Auth = response.body;

			expect(authData).toHaveProperty('token');
			const decodedToken = jwtService.verify(authData.token);

			expect(decodedToken.email).toEqual(loginDto.email);
		});

		it('deve falhar ao autenticar com credenciais inválidas', async () => {
			const invalidLoginDto: LoginDto = {
				email: '<EMAIL>',
				password: 'wrongpassword',
			};

			const response = await request(app.getHttpServer())
				.post('/v1/auth/login')
				.send(invalidLoginDto)
				.expect(401);

			expect(response.body.message).toEqual('E-mail e/ou senha são inválidos');
		});
	});
});
