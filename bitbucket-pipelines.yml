definitions:
  caches:
    pnpm: $BITBUCKET_CLONE_DIR/.pnpm-store
  services:
    docker:
      memory: 3072

pipelines:
  pull-requests:
    "**":
      - step:
          name: Build and test
          image: node:20.17
          script:
            - corepack enable
            - corepack prepare pnpm@10.0.0 --activate
            - pnpm install
            - pnpm run build
            - pnpm run test
          caches:
            - pnpm
  branches:
    master:
      - step:
          name: test
          image: node:20.17
          script:
            - corepack enable
            - corepack prepare pnpm@10.0.0 --activate
            - pnpm install
            - pnpm run test
          caches:
            - pnpm
      - step:
          name: Build, push, and apply to production
          deployment: production
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_PRODUCTION_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-back"
            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-back.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-back:latest .
            - docker tag carefy-censo-back:latest $IMAGE:$TAG
            - docker tag carefy-censo-back:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-back-$ENV.yml > deploy/deployment-carefy-censo-back-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-back-$ENV-$TAG.yml
    develop:
      - step:
          name: test
          image: node:20.17
          script:
            - corepack enable
            - corepack prepare pnpm@10.0.0 --activate
            - pnpm install
            - pnpm run test
          caches:
            - pnpm
      - step:
          name: Build, push, and apply api to dev
          deployment: dev
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_DEV_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-back"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-back.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-back:latest .
            - docker tag carefy-censo-back:latest $IMAGE:$TAG
            - docker tag carefy-censo-back:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-back-$ENV.yml > deploy/deployment-carefy-censo-back-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-back-$ENV-$TAG.yml
  custom:
    gcp-carefy-censo-back-dev:
      - step:
          name: Build, push and apply to dev
          deployment: dev
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_DEV_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-back"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-back.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-back:latest .
            - docker tag carefy-censo-back:latest $IMAGE:$TAG
            - docker tag carefy-censo-back:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-back-$ENV.yml > deploy/deployment-carefy-censo-back-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-back-$ENV-$TAG.yml
    gcp-carefy-censo-back-staging:
      - step:
          name: Build, push and apply to staging
          deployment: staging
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_DEV_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-back"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-back.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-back:latest .
            - docker tag carefy-censo-back:latest $IMAGE:$TAG
            - docker tag carefy-censo-back:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-back-$ENV.yml > deploy/deployment-carefy-censo-back-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-back-$ENV-$TAG.yml
    gcp-carefy-censo-back-prod:
      - step:
          name: Build, push and apply to production
          deployment: production
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_PRODUCTION_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-back"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-back.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-back:latest .
            - docker tag carefy-censo-back:latest $IMAGE:$TAG
            - docker tag carefy-censo-back:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-back-$ENV.yml > deploy/deployment-carefy-censo-back-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-back-$ENV-$TAG.yml
    gcp-carefy-censo-consumer-dev:
      - step:
          name: Build, push and apply to dev
          deployment: dev
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_DEV_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-consumer"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-consumer.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-consumer:latest .
            - docker tag carefy-censo-consumer:latest $IMAGE:$TAG
            - docker tag carefy-censo-consumer:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-consumer-$ENV.yml > deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml
    gcp-carefy-censo-consumer-staging:
      - step:
          name: Build, push and apply to staging
          deployment: staging
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_DEV_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-consumer"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-consumer.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-consumer:latest .
            - docker tag carefy-censo-consumer:latest $IMAGE:$TAG
            - docker tag carefy-censo-consumer:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-consumer-$ENV.yml > deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml
    gcp-carefy-censo-consumer-prod:
      - step:
          name: Build, push and apply to production
          deployment: production
          image: google/cloud-sdk:latest
          services:
            - docker
          script:
            - export VERSION=$CAREFY_PRODUCTION_VERSION
            - export HASH=`git log -1 --pretty=format:%h`
            - export DATA=`env TZ=America/Sao_Paulo date +%Y%m%d-%H%M%S`
            - export TAG="$VERSION-$DATA-$HASH"
            - export IMAGE="$CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-consumer"

            - export DOCKER_BUILDKIT=0

            - echo $CAREFY_GCP_KEYFILE > ~/.gcloud-api-key.json
            - gcloud auth activate-service-account --key-file ~/.gcloud-api-key.json
            - gcloud config set project $CAREFY_GCP_PROJECT
            - gcloud container clusters get-credentials $CAREFY_GCP_CLUSTER --zone=$CAREFY_GCP_ZONE --project $CAREFY_GCP_PROJECT
            - gcloud auth configure-docker $CAREFY_GCP_REGION-docker.pkg.dev --quiet

            - rm -f /etc/apt/sources.list.d/google-cloud*
            - apt-get update
            - apt-get install gettext-base

            - envsubst < deploy/cloud/gcp/Dockerfile-censo-consumer.pipeline > deploy/Dockerfile

            - docker build -f deploy/Dockerfile -t carefy-censo-consumer:latest .
            - docker tag carefy-censo-consumer:latest $IMAGE:$TAG
            - docker tag carefy-censo-consumer:latest $IMAGE:$VERSION

            - docker push $IMAGE:$TAG
            - docker push $IMAGE:$VERSION

            - envsubst < deploy/cloud/gcp/gke/carefy/deployment/template/deployment-carefy-censo-consumer-$ENV.yml > deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml

            - kubectl apply -f deploy/deployment-carefy-censo-consumer-$ENV-$TAG.yml