import { BedHospital } from 'src/core/domain/BedHospital';
import { HospitalMapper } from '../hospital/HospitalMapper';
import { BedHospitalEntity } from 'src/gateway/entities/bedHospital.entity';

export class BedHospitalMapper {
	public static toBedHospitalDomain(
		bedHospitalEntity: BedHospitalEntity,
	): BedHospital {
		return new BedHospital(
			bedHospitalEntity.id ?? null,
			bedHospitalEntity.created,
			bedHospitalEntity.admissionIn,
			bedHospitalEntity.accommodation,
			bedHospitalEntity.isCenso,
			bedHospitalEntity.isTransfer,
			Number(bedHospitalEntity.price),
			bedHospitalEntity.enabled,
			bedHospitalEntity.hospital
				? HospitalMapper.toHospitalDomain(bedHospitalEntity.hospital)
				: undefined,
			bedHospitalEntity.updated,
			bedHospitalEntity.changed,
			bedHospitalEntity.admissionOut,
			bedHospitalEntity.patientWayId,
			bedHospitalEntity.bedNumber,
			bedHospitalEntity.speciality,
			bedHospitalEntity.accommodationCustom,
			bedHospitalEntity.userId,
			bedHospitalEntity.accommodationIsolation,
			bedHospitalEntity.codIntegration,
		);
	}
}
