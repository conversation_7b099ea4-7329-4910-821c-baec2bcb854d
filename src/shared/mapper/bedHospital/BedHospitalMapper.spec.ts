import { BedHospitalMapper } from 'src/shared/mapper/bedHospital/BedHospitalMapper';
import { BedHospital } from 'src/core/domain/BedHospital';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { Hospital } from 'src/core/domain/Hospital';
import { BedHospitalEntity } from 'src/gateway/entities/bedHospital.entity';

describe('BedHospitalMapper.toBedHospitalDomain', () => {
	it('deve mapear corretamente um objeto BedHospitalEntity para o domínio BedHospital', () => {
		const dummyHospitalEntity = { id: 999 } as HospitalEntity;
		const dummyEntity = {
			id: 10,
			created: new Date('2023-01-01T00:00:00Z'),
			admissionIn: new Date('2023-01-02T00:00:00Z'),
			accommodation: 'Room A',
			isCenso: true,
			isTransfer: true,
			price: 200.5, // simulando que vem como string
			enabled: true,
			hospital: dummyHospitalEntity,
			updated: new Date('2023-01-03T00:00:00Z'),
			changed: new Date('2023-01-04T00:00:00Z'),
			admissionOut: new Date('2023-01-05T00:00:00Z'),
			patientWayId: 20,
			bedNumber: 'B12',
			speciality: 'Cardiology',
			accommodationCustom: 'VIP Room',
			userId: 101,
			accommodationIsolation: 'Isolation A',
			codIntegration: 1,
		} as BedHospitalEntity;

		const dummyHospitalDomain = {
			id: 999,
			name: 'Dummy Hospital',
		} as unknown as Hospital;
		jest
			.spyOn(HospitalMapper, 'toHospitalDomain')
			.mockReturnValue(dummyHospitalDomain);

		const result = BedHospitalMapper.toBedHospitalDomain(dummyEntity);

		const expectedDomain = new BedHospital(
			dummyEntity.id,
			dummyEntity.created,
			dummyEntity.admissionIn,
			dummyEntity.accommodation,
			dummyEntity.isCenso,
			dummyEntity.isTransfer,
			dummyEntity.price,
			dummyEntity.enabled,
			dummyHospitalDomain,
			dummyEntity.updated,
			dummyEntity.changed,
			dummyEntity.admissionOut,
			dummyEntity.patientWayId,
			dummyEntity.bedNumber,
			dummyEntity.speciality,
			dummyEntity.accommodationCustom,
			dummyEntity.userId,
			dummyEntity.accommodationIsolation,
			dummyEntity.codIntegration,
		);

		expect(result).toEqual(expectedDomain);
	});

	it('deve mapear corretamente um objeto BedHospitalEntity para o domínio BedHospital (id e hospital deve ser nulo)', () => {
		const dummyEntity = {
			id: null,
			created: new Date('2023-01-01T00:00:00Z'),
			admissionIn: new Date('2023-01-02T00:00:00Z'),
			accommodation: 'Room A',
			isCenso: true,
			isTransfer: true,
			price: 200.5, // simulando que vem como string
			enabled: true,
			hospital: null,
			updated: new Date('2023-01-03T00:00:00Z'),
			changed: new Date('2023-01-04T00:00:00Z'),
			admissionOut: new Date('2023-01-05T00:00:00Z'),
			patientWayId: 20,
			bedNumber: 'B12',
			speciality: 'Cardiology',
			accommodationCustom: 'VIP Room',
			userId: 101,
			accommodationIsolation: 'Isolation A',
			codIntegration: 1,
		} as BedHospitalEntity;

		const dummyHospitalDomain = undefined as unknown as Hospital;
		jest
			.spyOn(HospitalMapper, 'toHospitalDomain')
			.mockReturnValue(dummyHospitalDomain);

		const result = BedHospitalMapper.toBedHospitalDomain(dummyEntity);

		const expectedDomain = new BedHospital(
			dummyEntity.id,
			dummyEntity.created,
			dummyEntity.admissionIn,
			dummyEntity.accommodation,
			dummyEntity.isCenso,
			dummyEntity.isTransfer,
			dummyEntity.price,
			dummyEntity.enabled,
			dummyHospitalDomain,
			dummyEntity.updated,
			dummyEntity.changed,
			dummyEntity.admissionOut,
			dummyEntity.patientWayId,
			dummyEntity.bedNumber,
			dummyEntity.speciality,
			dummyEntity.accommodationCustom,
			dummyEntity.userId,
			dummyEntity.accommodationIsolation,
			dummyEntity.codIntegration,
		);

		expect(result).toEqual(expectedDomain);
	});
});
