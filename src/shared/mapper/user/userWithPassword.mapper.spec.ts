import { User } from 'src/core/domain/User';
import { UserWithPasswordMapper } from './userWithPassword.mapper';
import { UserEntity } from 'src/gateway/entities/user.entity';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';

describe('UserWithPasswordMapper', () => {
	it('should map UserEntity to User domain object correctly', () => {
		const mockUserEntity: UserEntity = {
			id: 1,
			name: '<PERSON>',
			email: '<EMAIL>',
			companyId: '3',
			password: 'hashed_password',
			enabled: 1,
			role: UserRoleEnum.ADMIN,
		};

		const result = UserWithPasswordMapper.toUserDomain(mockUserEntity);

		// Verify that the result is an instance of User
		expect(result).toBeInstanceOf(User);

		// Verify that each property matches the UserEntity
		expect(result.id).toEqual(mockUserEntity.id);
		expect(result.name).toEqual(mockUserEntity.name);
		expect(result.email).toEqual(mockUserEntity.email);
		expect(result.password).toEqual(mockUserEntity.password);
	});
});
