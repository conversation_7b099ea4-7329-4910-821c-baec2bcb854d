import { User } from 'src/core/domain/User';
import { UserMapper } from 'src/shared/mapper/user/user.mapper';
import { GetUserDto } from 'src/entrypoint/dto/user/get-user.dto';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';

describe('UserMapper', () => {
	it('should map UserEntity to User correctly', () => {
		const mockUserEntity: GetUserDto = {
			id: 1,
			name: 'Test',
			email: '<EMAIL>',
			companyId: '3',
			role: UserRoleEnum.ADMIN,
		};

		const user: User = UserMapper.toUserDomain(mockUserEntity);
		expect(user).toBeInstanceOf(User);
		expect(user.id).toBe(mockUserEntity.id);
		expect(user.name).toBe(mockUserEntity.name);
		expect(user.email).toBe(mockUserEntity.email);
	});
});
