import { Autorizacoes } from 'src/core/domain/Autorizacoes';
import { AutorizacoesAuditStatus } from 'src/core/domain/AutorizacoesAuditStatus';
import { MotivoNegativa } from 'src/core/domain/MotivoNegativa';
import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';
import Operadoras from 'src/shared/operadoras.enum';
import { HospitalMapper } from '../hospital/HospitalMapper';
import { AutorizacoesRawDto } from 'src/gateway/autorizacoes/dto/AutorizacoesRaw.dto';
import { AutorizacoesViewModelDto } from 'src/core/application/dto/AutorizacoesViewModel';

export class AutorizacoesMapper {
	static toAutorizacoesDomain(
		autorizacoesEntity: AutorizacoesEntity,
	): Autorizacoes {
		return new Autorizacoes(
			autorizacoesEntity.id,
			autorizacoesEntity.created,
			autorizacoesEntity.caraterInternacao,
			autorizacoesEntity.dataSolicitacao,
			autorizacoesEntity.limitDate,
			autorizacoesEntity.pac,
			autorizacoesEntity.nomePrestador,
			autorizacoesEntity.regimeInternacao,
			autorizacoesEntity.authorizationStatusIa,
			autorizacoesEntity.authorizationMotivoIa,
			autorizacoesEntity.authorizationDescriptionIa,
			autorizacoesEntity.patient,
			autorizacoesEntity.company?.id as Operadoras,
			autorizacoesEntity.numeroGuia,
			autorizacoesEntity.cidPrincipal,
			autorizacoesEntity.tipoGuia,
			autorizacoesEntity.numeroReanalises,
			autorizacoesEntity.transactionNumber,
			autorizacoesEntity.dataVencimento,
			autorizacoesEntity.isFavorito,
			autorizacoesEntity.sugestao,
			autorizacoesEntity.hospitalCompany
				? HospitalMapper.toHospitalCompanyDomain(
						autorizacoesEntity.hospitalCompany,
					)
				: undefined,
			autorizacoesEntity.motivoNegativa
				? new MotivoNegativa(
						autorizacoesEntity.motivoNegativa.id,
						autorizacoesEntity.motivoNegativa.codeTas,
						autorizacoesEntity.motivoNegativa.category,
						autorizacoesEntity.motivoNegativa.description,
						autorizacoesEntity.motivoNegativa.enabled,
						autorizacoesEntity.motivoNegativa.created,
						autorizacoesEntity.motivoNegativa.updated,
					)
				: undefined,
			autorizacoesEntity.enabled,
			autorizacoesEntity.autorizacaoStatus
				? new AutorizacoesAuditStatus(
						autorizacoesEntity.autorizacaoStatus.id,
						autorizacoesEntity.autorizacaoStatus.descricao,
						autorizacoesEntity.autorizacaoStatus.cor,
					)
				: undefined,
		);
	}

	static toAutorizacoesViewModel(
		autorizacao: AutorizacoesRawDto,
	): AutorizacoesViewModelDto {
		return new AutorizacoesViewModelDto(
			autorizacao.ag_id,
			autorizacao.patient_name,
			autorizacao.cod_beneficiario,
			autorizacao.carater,
			autorizacao.data_pedido,
			autorizacao.limit_date,
			autorizacao.ag_pac,
			autorizacao.prestador,
			autorizacao.ag_regimeInternacao,
			autorizacao.plan,
			autorizacao.plan_status,
			autorizacao.plan_regulamentado,
			autorizacao.status_ia,
			autorizacao.motivo_ia,
			autorizacao.description_ia,
			autorizacao.authorization_id,
			autorizacao.patient_id,
			autorizacao.company_id,
			autorizacao.numero_guia,
			autorizacao.ag_cidPrincipal,
			autorizacao.ag_tipoGuia,
			autorizacao.audit_status,
			autorizacao.numero_reanalises,
			autorizacao.transaction_number,
			autorizacao.ag_tipoGuia2,
			autorizacao.tags,
			autorizacao.ag_dataVencimento,
			autorizacao.empresa,
			autorizacao.status,
			autorizacao.status_descricao,
			autorizacao.status_cor,
			autorizacao.ag_created,
			autorizacao.unimed_name,
			autorizacao.pendency,
			autorizacao.observacoes,
			autorizacao.motivo_negativa,
			autorizacao.ag_isFavorito,
			autorizacao.isFavorito2,
			autorizacao.ag_sugestao,
			autorizacao.ultimo_auditou,
			autorizacao.data_ultimo_auditou,
		);
	}
}
