import { Autorizacoes } from 'src/core/domain/Autorizacoes';
import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';
import { AutorizacoesMapper } from './autorizacoes.mapper';
import { AutorizacoesRawDto } from 'src/gateway/autorizacoes/dto/AutorizacoesRaw.dto';
import { AutorizacoesViewModelDto } from 'src/core/application/dto/AutorizacoesViewModel';
import { OperadoraEntity } from 'src/gateway/entities/operadora.entity';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';
import { AutorizacoesAuditStatus } from 'src/core/domain/AutorizacoesAuditStatus';
import { MotivoNegativaEntity } from 'src/gateway/entities/motivoNegativa.entity';

describe('AutorizacoesMapper', () => {
	describe('toAutorizacoesDomain', () => {
		it('deve mapear AutorizacoesEntity para Autorizacoes corretamente', () => {
			// Arrange
			const mockDate = new Date('2024-01-01T10:00:00Z');

			const mockPatient = new PatientEntity();

			const mockEntity = new AutorizacoesEntity();
			mockEntity.id = 1;
			mockEntity.created = mockDate;
			mockEntity.caraterInternacao = 'Eletivo';
			mockEntity.dataSolicitacao = mockDate;
			mockEntity.limitDate = mockDate;
			mockEntity.pac = 'PAC123';
			mockEntity.nomePrestador = 'Hospital Teste';
			mockEntity.regimeInternacao = 'Ambulatorial';
			mockEntity.authorizationStatusIa = 'APROVADO';
			mockEntity.authorizationMotivoIa = 'Dentro dos critérios';
			mockEntity.authorizationDescriptionIa = 'Aprovação automática';
			mockEntity.patient = mockPatient;
			mockEntity.company = new OperadoraEntity();
			mockEntity.numeroGuia = '12345';
			mockEntity.cidPrincipal = 'J45';
			mockEntity.tipoGuia = 'SADT';
			mockEntity.numeroReanalises = 0;
			mockEntity.autorizacaoStatus = new AutorizacoesAuditStatus(
				'em_analise',
				'Em analise',
				'#**********',
			);

			mockEntity.hospitalCompany = { id: 124124 } as HospitalsCompanyEntity;
			mockEntity.motivoNegativa = new MotivoNegativaEntity();
			// Act
			const result = AutorizacoesMapper.toAutorizacoesDomain(mockEntity);

			// Assert
			expect(result).toBeInstanceOf(Autorizacoes);
			expect(result.id).toBe(mockEntity.id);
			expect(result.caraterInternacao).toBe(mockEntity.caraterInternacao);
			expect(result.dataSolicitacao).toEqual(mockEntity.dataSolicitacao);
			expect(result.limitDate).toEqual(mockEntity.limitDate);
			expect(result.pac).toBe(mockEntity.pac);
			expect(result.nomePrestador).toBe(mockEntity.nomePrestador);
			expect(result.regimeInternacao).toBe(mockEntity.regimeInternacao);
			expect(result.authorizationStatusIa).toBe(
				mockEntity.authorizationStatusIa,
			);
			expect(result.authorizationMotivoIa).toBe(
				mockEntity.authorizationMotivoIa,
			);
			expect(result.authorizationDescriptionIa).toBe(
				mockEntity.authorizationDescriptionIa,
			);
			expect(result.patient).toEqual(mockEntity.patient);
			expect(result.numeroGuia).toBe(mockEntity.numeroGuia);
			expect(result.cidPrincipal).toBe(mockEntity.cidPrincipal);
			expect(result.tipoGuia).toBe(mockEntity.tipoGuia);
			expect(result.numeroReanalises).toBe(mockEntity.numeroReanalises);
		});

		it('deve mapear AutorizacoesEntity com valores nulos', () => {
			// Arrange
			const mockEntity = new AutorizacoesEntity();
			mockEntity.id = 1;
			mockEntity.created = new Date();

			// Act
			const result = AutorizacoesMapper.toAutorizacoesDomain(mockEntity);

			// Assert
			expect(result).toBeInstanceOf(Autorizacoes);
			expect(result.id).toBe(mockEntity.id);
			expect(result.patient).toBeUndefined();
		});

		it('deve mapear AutorizacoesRawDto para AutorizacoesViewModelDto', () => {
			// Arrange
			const mockRawDto: AutorizacoesRawDto = {
				ag_id: 1,
				ag_created: new Date('2024-01-01'),
				ag_pac: '789',
				ag_regimeInternacao: 'AMBULATORIAL',
				ag_cidPrincipal: 'A123',
				ag_tipoGuia: 'SADT',
				ag_tipoGuia2: 'SADT',
				ag_dataVencimento: new Date('2024-02-01'),
				ag_isFavorito: false,
				ag_sugestao: 'APROVAR',
				patient_name: 'João Silva',
				cod_beneficiario: '123456',
				carater: 'ELETIVO',
				data_pedido: '01/01/2024',
				limit_date: new Date('2024-02-01'),
				prestador: 'Hospital ABC',
				plan: 'Plano Premium',
				plan_status: 'ATIVO',
				plan_regulamentado: true,
				status_ia: 'APROVADO',
				motivo_ia: null,
				description_ia: null,
				authorization_id: 1,
				patient_id: 123,
				company_id: 1,
				numero_guia: 'G123456',
				audit_status: 'PENDENTE',
				numero_reanalises: 0,
				transaction_number: 'T789',
				tags: 'urgente, prioritário',
				empresa: 'HOSPITAL SÃO LUCAS LTDA',
				status: 'PENDENTE',
				status_descricao: 'Aguardando análise médica',
				status_cor: 'amarelo',
				unimed_name: 'UNIMED VITORIA',
				pendency: 1,
				observacoes: 'Paciente com histórico de asma',
				motivo_negativa: null,
				isFavorito2: 0,
				ultimo_auditou: 'Dra. Maria Santos',
				data_ultimo_auditou: new Date('2024-01-15'),
			};

			// Act
			const result = AutorizacoesMapper.toAutorizacoesViewModel(mockRawDto);

			// Assert
			expect(result).toBeInstanceOf(AutorizacoesViewModelDto);
			expect(result.id).toBe(mockRawDto.ag_id);
			expect(result.nomePaciente).toBe(mockRawDto.patient_name);
			expect(result.codBeneficiario).toBe(mockRawDto.cod_beneficiario);
			expect(result.carater).toBe(mockRawDto.carater);
			expect(result.dataPedido).toBe(mockRawDto.data_pedido);
			expect(result.dataLimite).toEqual(mockRawDto.limit_date);
			expect(result.pac).toBe(mockRawDto.ag_pac);
			expect(result.prestador).toBe(mockRawDto.prestador);
			expect(result.regimeInternacao).toBe(mockRawDto.ag_regimeInternacao);
			expect(result.plano).toBe(mockRawDto.plan);
			expect(result.statusPlano).toBe(mockRawDto.plan_status);
			expect(result.planoRegulamentado).toBe(mockRawDto.plan_regulamentado);
			expect(result.statusIa).toBe(mockRawDto.status_ia);
			expect(result.motivoIa).toBe(mockRawDto.motivo_ia);
			expect(result.descricaoIa).toBe(mockRawDto.description_ia);
		});

		it('deve lidar com erros de mapeamento', () => {
			// Arrange
			const invalidEntity: null = null;

			// Act & Assert
			expect(() => {
				AutorizacoesMapper.toAutorizacoesDomain(
					invalidEntity as unknown as AutorizacoesEntity,
				);
			}).toThrow();
		});
	});
});
