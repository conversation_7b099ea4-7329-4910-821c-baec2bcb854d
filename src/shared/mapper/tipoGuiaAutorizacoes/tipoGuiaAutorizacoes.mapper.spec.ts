import { TipoGuiaMapper } from 'src/shared/mapper/tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.mapper';
import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoesEntity } from 'src/gateway/entities/tipoGuiaAutorizacoes.entity';

describe('TipoGuiaMapper', () => {
	describe('toTipoGuiaAutorizacoesDomain', () => {
		it('deve mapear um TipoGuiaAutorizacoesEntity para um TipoGuiaAutorizacoes corretamente', () => {
			// Arrange
			const dummyTipoGuiaAutorizacoesEntity: TipoGuiaAutorizacoesEntity = {
				id: 1,
				tipo: 'Tipo Teste',
				companyId: 100,
				enabled: true,
			} as TipoGuiaAutorizacoesEntity;

			// Act
			const result: TipoGuiaAutorizacoes =
				TipoGuiaMapper.toTipoGuiaAutorizacoesDomain(
					dummyTipoGuiaAutorizacoesEntity,
				);

			// Assert
			expect(result).toBeInstanceOf(TipoGuiaAutorizacoes);
			expect(result.id).toBe(dummyTipoGuiaAutorizacoesEntity.id);
			expect(result.tipo).toBe(dummyTipoGuiaAutorizacoesEntity.tipo);
			expect(result.companyId).toBe(dummyTipoGuiaAutorizacoesEntity.companyId);
			expect(result.enabled).toBe(dummyTipoGuiaAutorizacoesEntity.enabled);
		});
	});
});
