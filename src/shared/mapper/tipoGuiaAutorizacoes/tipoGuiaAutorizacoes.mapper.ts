import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';

import { TipoGuiaAutorizacoesEntity } from 'src/gateway/entities/tipoGuiaAutorizacoes.entity';

export class TipoGuiaMapper {
	static toTipoGuiaAutorizacoesDomain(
		tipoGuiaAutorizacoesEntity: TipoGuiaAutorizacoesEntity,
	): TipoGuiaAutorizacoes {
		return new TipoGuiaAutorizacoes(
			tipoGuiaAutorizacoesEntity.id,
			tipoGuiaAutorizacoesEntity.tipo,
			tipoGuiaAutorizacoesEntity.companyId,
			tipoGuiaAutorizacoesEntity.enabled,
		);
	}
}
