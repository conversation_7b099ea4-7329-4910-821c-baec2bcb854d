import { FormCidsMapper } from 'src/shared/mapper/formCids/FormCidsMapper';
import { FormCids } from 'src/core/domain/FormCids';
import { FormCidsEntity } from 'src/gateway/entities/formCids.entity';
import { HospitalizationMapper } from 'src/shared/mapper/hospitalization/HospitalizationMapper';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';

describe('FormCidsMapper.toDomain', () => {
	it('deve mapear corretamente um FormCidsEntity para o domínio FormCids', () => {
		const dummyHospitalizationDomain = { id: 5 } as Hospitalization;

		jest
			.spyOn(HospitalizationMapper, 'toHospitalizationDomain')
			.mockReturnValue(dummyHospitalizationDomain);

		const dummyEntity: FormCidsEntity = {
			id: 1,
			created: new Date('2023-01-01T00:00:00Z'),
			type: 2,
			status: 3,
			userId: 10,
			enabled: true,
			isCenso: false,
			patientWay: { id: 5 } as HospitalizationEntity,
			updated: new Date('2023-01-02T00:00:00Z'),
			cidSubCategoriaId: '100',
			cidCategoriaId: '200',
			codIntegration: '1',
		};

		const result = FormCidsMapper.toDomain(dummyEntity);

		const expected = new FormCids(
			1,
			new Date('2023-01-01T00:00:00Z'),
			2,
			3,
			10,
			true,
			false,
			dummyHospitalizationDomain,
			new Date('2023-01-02T00:00:00Z'),
			'100',
			'200',
			'1',
		);

		expect(result).toEqual(expected);
	});
});
