import { FormCids } from 'src/core/domain/FormCids';
import { FormCidsEntity } from 'src/gateway/entities/formCids.entity';
import { HospitalizationMapper } from '../hospitalization/HospitalizationMapper';

export class FormCidsMapper {
	public static toDomain(formCidsEntity: FormCidsEntity): FormCids {
		return new FormCids(
			formCidsEntity.id,
			formCidsEntity.created,
			formCidsEntity.type,
			formCidsEntity.status,
			formCidsEntity.userId,
			formCidsEntity.enabled,
			formCidsEntity.isCenso,
			HospitalizationMapper.toHospitalizationDomain(formCidsEntity.patientWay),
			formCidsEntity.updated,
			formCidsEntity.cidSubCategoriaId,
			formCidsEntity.cidCategoriaId,
			formCidsEntity.codIntegration,
		);
	}
}
