import { GuideDailyMapper } from 'src/shared/mapper/guideDaily/GuideDailyMapper';
import { GuideDaily } from 'src/core/domain/GuideDaily';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { Guide } from 'src/core/domain/Guide';

describe('GuideDailyMapper.toDomain', () => {
	it('deve mapear corretamente um GuideDailyEntity para o domínio GuideDaily', () => {
		const dummyGuideDomain = { id: 10 } as Guide;
		jest.spyOn(GuideMapper, 'toDomain').mockReturnValue(dummyGuideDomain);

		const dummyEntity: GuideDailyEntity = {
			id: 123,
			guide: { id: 999 } as Partial<Guide>, // este objeto será convertido por GuideMapper.toDomain
			accommodation: 'Test Accommodation',
			qtdeDaysRequested: 5,
			qtdeDaysAuthorized: 4,
			qtdeDaysDenied: 1,
			isCenso: 0,
			userId: 100,
			price: '150.50',
			guideRequest: 200,
			enabled: 1,
			dateStart: new Date('2023-01-01T00:00:00Z'),
			dateFinish: new Date('2023-01-02T00:00:00Z'),
			qtdeDays: 7,
			aux: 0,
			auxId: 300,
			codIntegration: 'INT-123',
			updated: new Date('2023-01-03T00:00:00Z'),
			created: new Date('2023-01-03T00:00:00Z'),
		} as unknown as GuideDailyEntity;

		const result: GuideDaily = GuideDailyMapper.toDomain(dummyEntity);

		const expected = new GuideDaily(
			dummyEntity.id,
			dummyEntity.guide ? dummyGuideDomain : null,
			dummyEntity.accommodation,
			dummyEntity.qtdeDaysRequested,
			dummyEntity.qtdeDaysAuthorized,
			dummyEntity.qtdeDaysDenied,
			dummyEntity.isCenso,
			dummyEntity.userId,
			Number(dummyEntity.price),
			dummyEntity.guideRequest,
			dummyEntity.enabled,
			dummyEntity.dateStart,
			dummyEntity.dateFinish,
			dummyEntity.qtdeDays,
			dummyEntity.aux,
			dummyEntity.auxId,
			dummyEntity.codIntegration,
			dummyEntity.updated,
		);

		expect(result).toEqual(expected);
	});

	it('deve mapear corretamente um GuideDailyEntity para o domínio GuideDaily (guide deve ser nulo)', () => {
		const dummyGuideDomain = { id: 10 } as Guide;
		jest.spyOn(GuideMapper, 'toDomain').mockReturnValue(dummyGuideDomain);

		const dummyEntity: GuideDailyEntity = {
			id: 123,
			guide: null, // este objeto será convertido por GuideMapper.toDomain
			accommodation: 'Test Accommodation',
			qtdeDaysRequested: 5,
			qtdeDaysAuthorized: 4,
			qtdeDaysDenied: 1,
			isCenso: 0,
			userId: 100,
			price: '150.50',
			guideRequest: 200,
			enabled: 1,
			dateStart: new Date('2023-01-01T00:00:00Z'),
			dateFinish: new Date('2023-01-02T00:00:00Z'),
			qtdeDays: 7,
			aux: 0,
			auxId: 300,
			codIntegration: 'INT-123',
			updated: new Date('2023-01-03T00:00:00Z'),
			created: new Date('2023-01-03T00:00:00Z'),
		} as unknown as GuideDailyEntity;

		const result: GuideDaily = GuideDailyMapper.toDomain(dummyEntity);

		const expected = new GuideDaily(
			dummyEntity.id,
			dummyEntity.guide ? dummyGuideDomain : null,
			dummyEntity.accommodation,
			dummyEntity.qtdeDaysRequested,
			dummyEntity.qtdeDaysAuthorized,
			dummyEntity.qtdeDaysDenied,
			dummyEntity.isCenso,
			dummyEntity.userId,
			Number(dummyEntity.price),
			dummyEntity.guideRequest,
			dummyEntity.enabled,
			dummyEntity.dateStart,
			dummyEntity.dateFinish,
			dummyEntity.qtdeDays,
			dummyEntity.aux,
			dummyEntity.auxId,
			dummyEntity.codIntegration,
			dummyEntity.updated,
		);

		expect(result).toEqual(expected);
	});
});
