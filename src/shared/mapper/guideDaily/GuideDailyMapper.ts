import { GuideDaily } from 'src/core/domain/GuideDaily';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { GuideMapper } from '../guide/GuideMapper';

export class GuideDailyMapper {
	public static toDomain(guideDailyEntity: GuideDailyEntity): GuideDaily {
		return new GuideDaily(
			guideDailyEntity.id,
			guideDailyEntity.guide
				? GuideMapper.toDomain(guideDailyEntity.guide)
				: null,
			guideDailyEntity.accommodation,
			guideDailyEntity.qtdeDaysRequested,
			guideDailyEntity.qtdeDaysAuthorized,
			guideDailyEntity.qtdeDaysDenied,
			guideDailyEntity.isCenso,
			guideDailyEntity.userId,
			Number(guideDailyEntity.price),
			guideDailyEntity.guideRequest,
			guideDailyEntity.enabled,
			guideDailyEntity.dateStart,
			guideDailyEntity.dateFinish,
			guideDailyEntity.qtdeDays,
			guideDailyEntity.aux,
			guideDailyEntity.auxId,
			guideDailyEntity.codIntegration,
			guideDailyEntity.updated,
		);
	}
}
