import { PlanosMapper } from 'src/shared/mapper/planos/planos.mapper';
import { Planos } from 'src/core/domain/Planos';
import { TipoContrato } from 'src/core/domain/TipoContrato';
import { PlanosEntity } from 'src/gateway/entities/planos.entity';
import { TipoContratoEntity } from 'src/gateway/entities/tipoContrato.entity';

describe('PlanosMapper', () => {
	describe('toPlanosDomain', () => {
		it('deve mapear um PlanosEntity para um Planos corretamente', () => {
			// Arrange
			const dummyTipoContratoEntity: TipoContratoEntity = {
				id: 1,
				name: 'Tipo Contrato Teste',
				codInterno: 'TC-001',
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				companyId: 100,
				enabled: true,
				planos: [],
			} as TipoContratoEntity;

			const dummyPlanosEntity: PlanosEntity = {
				id: 10,
				name: 'Plano Teste',
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				companyId: 100,
				codInterno: 'PL-001',
				enabled: true,
				tipoContrato: dummyTipoContratoEntity,
			} as PlanosEntity;

			// Act
			const result: Planos = PlanosMapper.toPlanosDomain(dummyPlanosEntity);

			// Assert
			expect(result).toBeInstanceOf(Planos);
			expect(result.id).toBe(dummyPlanosEntity.id);
			expect(result.nome).toBe(dummyPlanosEntity.name);

			// Verificar o objeto TipoContrato
			expect(result.tipoContrato).toBeInstanceOf(TipoContrato);
			expect(result.tipoContrato.id).toBe(dummyPlanosEntity.tipoContrato.id);
			expect(result.tipoContrato.nome).toBe(
				dummyPlanosEntity.tipoContrato.name,
			);
			expect(result.tipoContrato.codigoInterno).toBe(
				dummyPlanosEntity.tipoContrato.codInterno,
			);
		});
	});
});
