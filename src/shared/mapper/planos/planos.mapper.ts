import { PlanosEntity } from '../../../gateway/entities/planos.entity';
import { Planos } from '../../../core/domain/Planos';
import { TipoContrato } from '../../../core/domain/TipoContrato';

export class PlanosMapper {
	static toPlanosDomain(planosEntity: PlanosEntity): Planos {
		return new Planos(
			planosEntity.id,
			planosEntity.name,
			new TipoContrato(
				planosEntity.tipoContrato.id,
				planosEntity.tipoContrato.name,
				planosEntity.tipoContrato.codInterno,
			),
		);
	}
}
