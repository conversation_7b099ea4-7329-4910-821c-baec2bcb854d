import { CensoDados } from 'src/core/domain/CensoDados';
import { parseBoolean } from 'src/helpers/parseBoolean.helper';
import { parseDate } from 'src/helpers/parseDate.helper';
import { parseNumber } from 'src/helpers/parseNumber.helper';

export const toSFO = (linhaCenso: Record<string, string>): CensoDados => {
	return new CensoDados(
		0,
		0,
		0,
		0,
		new Date(),
		1,
		parseDate(linhaCenso['data']),
		linhaCenso['municipio'] || '',
		linhaCenso['hospital credenciado'] || '',
		linhaCenso['controle'] || '',
		parseDate(linhaCenso['dt nascimento']),
		parseDate(linhaCenso['data de internacao']),
		parseDate(linhaCenso['data alta']),
		linhaCenso['motivo alta'] || '',
		null,
		null,
		parseDate(linhaCenso['previsao alta']),
		linhaCenso['carater de internacao'] || '',
		linhaCenso['tipo internacao'] || '',
		linhaCenso['codigo da guia'] || '',
		linhaCenso['alto custo status'] || '',
		linhaCenso['beneficiario'] || '',
		linhaCenso['cod beneficiario'] || '',
		linhaCenso['cidade benef.'] || '',
		linhaCenso['estado benef.'] || '',
		parseBoolean(linhaCenso['recem nascido']),
		linhaCenso['tipo cliente'] || '',
		parseNumber(linhaCenso['valor/diaria']) || 0,
		linhaCenso['regional beneficiario'] || '',
		linhaCenso['tipo controle (fixo)'] || '',
		parseNumber(linhaCenso['diarias autorizadas']),
		linhaCenso['cnes'],
		null,
		null,
		null,
		null,
		null,
		null,
		null,
		undefined,
		undefined,
		linhaCenso['acomodacao'] || '',
	);
};
