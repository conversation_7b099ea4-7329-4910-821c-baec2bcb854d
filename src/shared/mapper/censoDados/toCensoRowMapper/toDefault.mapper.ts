import { CensoDados } from 'src/core/domain/CensoDados';
import { parseBoolean } from 'src/helpers/parseBoolean.helper';
import { parseDate } from 'src/helpers/parseDate.helper';
import { parseNumber } from 'src/helpers/parseNumber.helper';

export const toDefault = (linhaCenso: Record<string, string>): CensoDados => {
	return new CensoDados(
		0,
		0,
		0,
		0,
		new Date(),
		1,
		parseDate(linhaCenso['data']),
		linhaCenso['municipio'] || '',
		linhaCenso['hospital credenciado'] || '',
		linhaCenso['controle'] || '',
		parseDate(linhaCenso['dt nascimento']),
		parseDate(linhaCenso['data internacao']),
		parseDate(linhaCenso['data alta']),
		linhaCenso['motivo alta'] || '',
		linhaCenso['diagnostico'] || '',
		linhaCenso['diagnostico secundario'] || '',
		parseDate(linhaCenso['previsao alta']),
		linhaCenso['carater de internacao'] || '',
		linhaCenso['tipo internacao'] || '',
		linhaCenso['codigo da guia'] || '',
		linhaCenso['alto custo status'] || '',
		linhaCenso['beneficiario'] || '',
		linhaCenso['cod beneficiario'] || '',
		linhaCenso['cidade benef.'] || '',
		linhaCenso['estado benef.'] || '',
		parseBoolean(linhaCenso['recem nascido']),
		linhaCenso['tipo cliente'] || '',
		parseNumber(linhaCenso['valor/diaria']) || 0,
		linhaCenso['regional beneficiario'] || '',
		linhaCenso['tipo controle'] || '',
		parseNumber(linhaCenso['diarias autorizadas']),
		linhaCenso['codigo hospital'] || '',
		linhaCenso['codigo plano'] || '',
		linhaCenso['nome do plano'] || '',
		linhaCenso['codigo empresa'] || '',
		linhaCenso['nome da empresa'] || '',
		linhaCenso['status plano'] || '',
		parseDate(linhaCenso['data plano desde']),
		null,
		undefined,
		undefined,
		linhaCenso['acomodacao'] || '',
	);
};
