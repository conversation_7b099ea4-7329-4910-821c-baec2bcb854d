import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosEntity } from 'src/gateway/entities/censoDados.entity';
import { CensoDadosMapper } from './censoDados.mapper';
import { LinhasConflitadasCensoEntity } from 'src/gateway/entities/linhasConflitadasCenso.entity';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import Operadoras from 'src/shared/operadoras.enum';
describe('CensoDadosMapper', () => {
	const operadoras = [Operadoras.SAO_FRANCISCO, Operadoras.MINHA_OPERADORA];
	describe('toCensoDados', () => {
		it('deve mapear corretamente um CensoDadosEntity para o domínio CensoDados', () => {
			// Mock da linha conflitante
			const linhaConflitada: LinhasConflitadasCensoEntity = {
				tipoConflito: { id: 1, descricao: 'Teste de Conflito' },
			} as LinhasConflitadasCensoEntity;

			// Mock do CensoDadosEntity
			const censoDadosEntity: CensoDadosEntity = {
				id: 1,
				censoId: 2,
				companyId: 3,
				dataCriacao: new Date('2024-10-08'),
				conflito: false,
				data: new Date('2024-10-08'),
				municipio: 'São Paulo',
				hospitalCredenciado: 'Hospital Hsanp',
				controle: 'Controle A',
				dtNascimento: new Date('1985-08-22'),
				dataInternacao: new Date('2024-10-07'),
				dataAlta: null,
				motivoAlta: '',
				diagnostico: 'Z000',
				diagnosticoSecundario: '',
				previsaoAlta: null,
				caraterInternacao: 'Eletiva',
				tipoInternacao: 'Clínica',
				codigoGuia: '123456',
				altoCustoStatus: '',
				nomeBeneficiario: 'Maria Souza',
				codBeneficiario: '*********',
				cidadeBeneficiario: 'Campinas',
				estadoBeneficiario: 'SP',
				recemNascido: false,
				tipoCliente: '',
				valorDiaria: '',
				regionalBeneficiario: '',
				tipoControle: '',
				diariasAutorizadas: 3,
				codigoHospital: '123',
				codigoPlano: '456',
				nomePlano: 'Plano A',
				codigoEmpresa: '789',
				nomeEmpresa: 'Empresa X',
				statusPlano: 'Ativo',
				dataPlanoDesde: new Date('2022-01-01'),
				censo: undefined,
				tipoConflito: undefined,
				linhasConflitadas: [linhaConflitada],
			} as unknown as CensoDadosEntity;

			const toTipoConflitoSpy = jest.spyOn(CensoDadosMapper, 'toTipoConflito');

			toTipoConflitoSpy.mockReturnValue(
				new TipoConflito(1, 'Teste de Conflito'),
			);

			const result = CensoDadosMapper.toCensoDados(censoDadosEntity);

			expect(result).toBeInstanceOf(CensoDados);
			expect(CensoDadosMapper.toTipoConflito).toHaveBeenCalledTimes(1);
			expect(CensoDadosMapper.toTipoConflito).toHaveBeenCalledWith(
				linhaConflitada,
			);
			expect(result.id).toBe(censoDadosEntity.id);
			expect(result.dataCriacao).toEqual(censoDadosEntity.dataCriacao);
			expect(result.municipio).toBe(censoDadosEntity.municipio);
			expect(result.diagnostico).toBe(censoDadosEntity.diagnostico);
			expect(result.recemNascido).toBe(censoDadosEntity.recemNascido);
			expect(result.dataPlanoDesde).toEqual(censoDadosEntity.dataPlanoDesde);

			toTipoConflitoSpy.mockRestore();
		});
	});

	describe('toCensoDadosDomain', () => {
		const rowCensoFromCompany = (companyId: number): Record<string, string> => {
			switch (companyId) {
				case Operadoras.SAO_FRANCISCO:
					return {
						data: '08/10/2024',
						municipio: 'São Paulo',
						'Hospital Credenciado': 'Hospital Hsanp',
						controle: 'CENSO',
						beneficiario: 'FELIPE MARTINS DOS SANTOS',
						'cod beneficiario': '9942371901909003',
						'dt nascimento': '19/08/2003',
						'data de internacao': '07/10/2024',
						acomodacao: 'Apartamento',
						'data alta': '',
						'motivo alta': '',
						'hipotese diagnostica diagnostico': '',
						'previsao alta': '',
						'carater de internacao': '',
						'tipo internacao': 'CLINICO/CIRURGICO',
						'uti na internacao': 'UTI',
						'codigo da guia': '',
						'alto custo status': '',
						'cidade benef': 'DOURADOS',
						'estado benef': 'MS',
						'tipo cliente': 'EMPRESA',
						valordiaria: '2002',
						'regional beneficiario': '846738',
						'tipo controle fixo': 'Dourados',
						'recem nascido': 'Aplicativo',
						'diarias autorizadas': '',
						sexo: '',
						regime: '',
					};
				default:
					return {
						Data: '08/10/2024',
						Municipio: 'São Paulo',
						'Hospital Credenciado': 'Hospital Hsanp',
						Controle: '',
						Beneficiario: 'FELIPE MARTINS DOS SANTOS',
						'Cod. Beneficiario': '9942371901909003',
						'Dt Nascimento': '19/08/2003',
						'Data Internacao': '07/10/2024',
						Acomodacao: 'Apartamento',
						'Data Alta': '',
						'Motivo Alta': '',
						Diagnostico: 'Z000',
						'Diagnostico Secundario': '',
						'Previsao Alta': '',
						'Carater de Internacao': 'URGÊNCIA',
						'Tipo Internacao': 'INTERNAÇÃO - CLÍNICA',
						'UTI na Internacao': 'NAO',
						'Codigo da Guia': '*********',
						'Alto Custo Status': '',
						'Cidade Benef.': '',
						'Estado Benef.': '',
						'Tipo Cliente': '',
						'Valor/Diaria': '',
						'Regional Beneficiario': '',
						'Tipo Controle': '',
						'Recem Nascido': 'N',
						'Diarias Autorizadas': '',
						Sexo: 'M',
						'Codigo Hospital': '994764',
						'Codigo Plano': 'ENESPIII',
						'Nome do Plano': 'NOVO ESSENCIAL SP III',
						'Codigo Empresa': '23994037495',
						'Nome da Empresa': 'NORTE LUMI INDUSTRIA E COMERCIO DE METAIS LTDA',
						'Status Plano': 'A',
						'Data Plano Desde': '10/01/2022',
						validação1: '*********',
					};
			}
		};
		it.each(operadoras)(
			'deve mapear corretamente uma linha para o domínio CensoDados para a company $i',
			(companyId) => {
				const linhaCenso = rowCensoFromCompany(companyId);

				const result = CensoDadosMapper.toCensoDadosFromLinhaCenso(
					companyId,
					linhaCenso,
				);

				expect(result).toBeInstanceOf(CensoDados);
				expect(result.data).toEqual(new Date('2024-10-08'));
				expect(result.municipio).toBe('São Paulo');
				expect(result.hospitalCredenciado).toBe('Hospital Hsanp');
				expect(result.nomeBeneficiario).toBe('FELIPE MARTINS DOS SANTOS');
				expect(result.codBeneficiario).toBe('9942371901909003');
				expect(result.dtNascimento).toEqual(new Date('2003-08-19'));
				expect(result.dataInternacao).toEqual(new Date('2024-10-07'));
				expect(result.dataAlta).toBeNull();
			},
		);

		it.each(operadoras)(
			'deve lidar com valores nulos ou ausentes corretamente',
			(companyId) => {
				const linhaCensoParcial = {
					Data: '',
					Municipio: '',
					'Hospital Credenciado': '',
				};

				const result = CensoDadosMapper.toCensoDadosFromLinhaCenso(
					companyId,
					linhaCensoParcial,
				);

				expect(result).toBeInstanceOf(CensoDados);
				expect(result.data).toBeNull();
				expect(result.municipio).toBe('');
				expect(result.hospitalCredenciado).toBe('');
			},
		);
	});

	describe('toTipoConflito', () => {
		it('deve mapear corretamente uma entidade de LinhasConflitadas para TipoConflito', () => {
			const conflitoEntity: LinhasConflitadasCensoEntity = {
				tipoConflito: { id: 1, descricao: 'Conflito A' },
			} as LinhasConflitadasCensoEntity;

			const result = CensoDadosMapper.toTipoConflito(conflitoEntity);

			expect(result).toBeInstanceOf(TipoConflito);
			expect(result.id).toBe(1);
			expect(result.descricao).toBe('Conflito A');
		});

		it('deve lançar um erro ao tentar mapear um conflito sem tipoConflito válido', () => {
			const conflitoEntity: LinhasConflitadasCensoEntity = {
				tipoConflito: null,
			} as LinhasConflitadasCensoEntity;

			expect(() => CensoDadosMapper.toTipoConflito(conflitoEntity)).toThrow();
		});
	});
});
