import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { CensoDadosEntity } from 'src/gateway/entities/censoDados.entity';
import { LinhasConflitadasCensoEntity } from 'src/gateway/entities/linhasConflitadasCenso.entity';
import { toDefault } from './toCensoRowMapper/toDefault.mapper';
import Operadoras from 'src/shared/operadoras.enum';
import { toSFO } from './toCensoRowMapper/toSFO.mapper';
import { sanitizeHeader } from 'src/helpers/sanitizeHeader.helper';

export class CensoDadosMapper {
	public static toCensoDados(censoDados: CensoDadosEntity): CensoDados {
		return new CensoDados(
			censoDados.id,
			censoDados.censoId,
			censoDados.companyId,
			censoDados.userId,
			censoDados.dataCriacao,
			censoDados.conflito,
			censoDados.data,
			censoDados.municipio,
			censoDados.hospitalCredenciado,
			censoDados.controle,
			censoDados.dtNascimento,
			censoDados.dataInternacao,
			censoDados.dataAlta,
			censoDados.motivoAlta,
			censoDados.diagnostico,
			censoDados.diagnosticoSecundario,
			censoDados.previsaoAlta,
			censoDados.caraterInternacao,
			censoDados.tipoInternacao,
			censoDados.codigoGuia,
			censoDados.altoCustoStatus,
			censoDados.nomeBeneficiario,
			censoDados.codBeneficiario,
			censoDados.cidadeBeneficiario,
			censoDados.estadoBeneficiario,
			censoDados.recemNascido,
			censoDados.tipoCliente,
			Number(censoDados.valorDiaria),
			censoDados.regionalBeneficiario,
			censoDados.tipoControle,
			censoDados.diariasAutorizadas,
			censoDados.codigoHospital,
			censoDados.codigoPlano,
			censoDados.nomePlano,
			censoDados.codigoEmpresa,
			censoDados.nomeEmpresa,
			censoDados.statusPlano,
			censoDados.dataPlanoDesde,
			null,
			censoDados.censo,
			censoDados.linhasConflitadas.map((linhas) => this.toTipoConflito(linhas)),
			censoDados.acomodacao,
		);
	}

	public static toTipoConflito(
		linhasConflitadas: LinhasConflitadasCensoEntity,
	): TipoConflito {
		return new TipoConflito(
			linhasConflitadas.tipoConflito.id,
			linhasConflitadas.tipoConflito.descricao,
			linhasConflitadas.tipoConflito.gravidade,
		);
	}

	public static toCensoDadosFromLinhaCenso(
		companyId: Operadoras,
		linhaCenso: Record<string, string>,
	): CensoDados {
		const rowFormatted = Object.entries(linhaCenso).reduce(
			(acc, [key, value]) => {
				const newKey = sanitizeHeader(key);
				acc[newKey] = value;
				return acc;
			},
			{} as Record<string, string>,
		);
		switch (companyId) {
			case Operadoras.SAO_FRANCISCO:
				return toSFO(rowFormatted);
			default:
				return toDefault(rowFormatted);
		}
	}
}
