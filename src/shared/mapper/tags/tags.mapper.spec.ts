import { Tags } from 'src/core/domain/Tags';
import { TagsEntity } from 'src/gateway/entities/tags.entity';
import { TagsMapper } from './tags.mapper';

describe('TagsMapper', () => {
	describe('toTagsDomain', () => {
		it('deve mapear TagsEntity para Tags corretamente', () => {
			const mockTagsEntity: TagsEntity = {
				id: 1,
				tag: 'Test Tag',
				companyId: 1,
				modulo: 'test',
				habilitado: true,
				enabled: true,
				dataCriacao: new Date('2024-01-01T10:00:00Z'),
			};

			const result = TagsMapper.toTagsDomain(mockTagsEntity);

			expect(result).toBeInstanceOf(Tags);
			expect(result.id).toBe(mockTagsEntity.id);
			expect(result.tag).toBe(mockTagsEntity.tag);
			expect(result.habilitado).toBe(mockTagsEntity.habilitado);
			expect(result.modulo).toBe(mockTagsEntity.modulo);
		});

		it('deve mapear TagsEntity com valores nulos/undefined', () => {
			const mockTagsEntity: TagsEntity = {
				id: 1,
				tag: null,
				companyId: undefined,
				modulo: null,
				habilitado: false,
				enabled: true,
				dataCriacao: null,
			};

			const result = TagsMapper.toTagsDomain(mockTagsEntity);

			expect(result).toBeInstanceOf(Tags);
			expect(result.id).toBe(mockTagsEntity.id);
			expect(result.tag).toBeNull();
			expect(result.habilitado).toBe(mockTagsEntity.habilitado);
			expect(result.modulo).toBeNull();
		});

		it('deve preservar os tipos de dados corretos ao mapear', () => {
			const mockTagsEntity: TagsEntity = {
				id: 1,
				tag: 'Test Tag',
				companyId: 1,
				modulo: 'test',
				habilitado: true,
				enabled: true,
				dataCriacao: new Date('2024-01-01T10:00:00Z'),
			};

			const result = TagsMapper.toTagsDomain(mockTagsEntity);

			expect(typeof result.id).toBe('number');
			expect(typeof result.tag).toBe('string');
			expect(typeof result.habilitado).toBe('boolean');
			expect(typeof result.modulo).toBe('string');
		});

		it('deve mapear múltiplas tags corretamente', () => {
			const mockTagsEntities: TagsEntity[] = [
				{
					id: 1,
					tag: 'Tag 1',
					companyId: 1,
					modulo: 'test1',
					habilitado: true,
					enabled: true,
					dataCriacao: new Date(),
				},
				{
					id: 2,
					tag: 'Tag 2',
					companyId: 1,
					modulo: 'test2',
					habilitado: false,
					enabled: true,
					dataCriacao: new Date(),
				},
			];

			const results = mockTagsEntities.map((entity) =>
				TagsMapper.toTagsDomain(entity),
			);

			expect(results).toHaveLength(2);
			expect(results[0]).toBeInstanceOf(Tags);
			expect(results[1]).toBeInstanceOf(Tags);
			expect(results[0].tag).toBe('Tag 1');
			expect(results[1].tag).toBe('Tag 2');
		});
	});
});
