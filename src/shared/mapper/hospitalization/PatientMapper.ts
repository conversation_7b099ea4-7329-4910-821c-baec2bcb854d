import { CensoDados } from 'src/core/domain/CensoDados';
import { Patient } from 'src/core/domain/Patient';
import { PatientEntity } from 'src/gateway/entities/patient.entity';

export class PatientMapper {
	public static toPatientDomain(patientEntity: PatientEntity): Patient {
		return new Patient(
			patientEntity.id,
			patientEntity.enabled,
			patientEntity.created,
			patientEntity.updated,
			patientEntity.userId,
			patientEntity.name,
			patientEntity.email,
			patientEntity.motherName,
			patientEntity.companyId,
			patientEntity.gender,
			patientEntity.codBeneficiario,
			patientEntity.birthday,
			patientEntity.planId,
			patientEntity.planStartDate,
			patientEntity.planEndDate,
			patientEntity.group,
			patientEntity.occupation,
			patientEntity.pregnantAddInfo,
			patientEntity.lactatingAddInfo,
			patientEntity.permissionAppMaxDate,
			patientEntity.startDate,
			patientEntity.image,
			patientEntity.tel,
			patientEntity.patientCity,
			patientEntity.codCidadeIbge,
			patientEntity.regHosp,
			patientEntity.color,
			patientEntity.cpf,
			patientEntity.internadoRepasse,
			patientEntity.isCenso,
			patientEntity.replicateFlag,
			patientEntity.cityId,
			patientEntity.street,
			patientEntity.homeNumber,
			patientEntity.neighborhood,
			patientEntity.zipcode,
			patientEntity.complement,
			patientEntity.corporationId,
			patientEntity.status,
			patientEntity.error,
			patientEntity.codIntegration,
			patientEntity.planRegulamentado,
			patientEntity.planoTipo,
			patientEntity.planoAbrangencia,
			patientEntity.planoTipoRede,
			patientEntity.planoCarencia,
			patientEntity.gruposCidadesIbge,
			patientEntity.isRn,
			patientEntity.carteiraNacionalSaude,
			patientEntity.idade,
			patientEntity.sexo,
			patientEntity.massa,
			patientEntity.altura,
			patientEntity.superficieCorporal,
		);
	}

	public static fromCensoDados(censoDados: CensoDados): Patient {
		return new Patient(
			null,
			1,
			new Date(),
			new Date(),
			censoDados.userId,
			censoDados.nomeBeneficiario,
			null,
			null,
			censoDados.companyId,
			null,
			censoDados.codBeneficiario,
			censoDados.dtNascimento || null,
			isNaN(Number(censoDados.codigoPlano))
				? null
				: Number(censoDados.codigoPlano),
			censoDados.dataPlanoDesde,
			null,
			null,
			null,
			null,
			null,
			null,
			censoDados.dataInternacao ?? null,
			'5.jpg',
			null,
			censoDados.cidadeBeneficiario ?? null,
			null,
			censoDados.codigoHospital ?? null,
			null,
			null,
			null,
			1,
			0,
			null,
			null,
			null,
			null,
			null,
			null,
			null, //corporation id
			censoDados.statusPlano ?? null,
			null,
			null,
			null,
			censoDados.nomePlano ?? null,
			null,
			null,
			null,
			null,
			censoDados.recemNascido ? 1 : 0,
			null,
			null,
			null,
			null,
			null,
			null,
		);
	}

	public static toEntity(patient: Patient | Partial<Patient>): PatientEntity {
		const patientEntity = new PatientEntity();
		patientEntity.id = patient.id;
		patientEntity.enabled = patient.enabled;
		patientEntity.created = patient.created;
		patientEntity.updated = patient.updated;
		patientEntity.userId = patient.userId;
		patientEntity.name = patient.name;
		patientEntity.email = patient.email;
		patientEntity.motherName = patient.motherName;
		patientEntity.companyId = patient.companyId;
		patientEntity.gender = patient.gender;
		patientEntity.codBeneficiario = patient.codBeneficiario;
		patientEntity.birthday = patient.birthday;
		patientEntity.planId = patient.planId;
		patientEntity.planStartDate = patient.planStartDate;
		patientEntity.planEndDate = patient.planEndDate;
		patientEntity.group = patient.group;
		patientEntity.occupation = patient.occupation;
		patientEntity.pregnantAddInfo = patient.pregnantAddInfo;
		patientEntity.lactatingAddInfo = patient.lactatingAddInfo;
		patientEntity.permissionAppMaxDate = patient.permissionAppMaxDate;
		patientEntity.startDate = patient.startDate;
		patientEntity.image = patient.image;
		patientEntity.tel = patient.tel;
		patientEntity.patientCity = patient.patientCity;
		patientEntity.codCidadeIbge = patient.codCidadeIbge;
		patientEntity.regHosp = patient.regHosp;
		patientEntity.color = patient.color;
		patientEntity.cpf = patient.cpf;
		patientEntity.internadoRepasse = patient.internadoRepasse;
		patientEntity.isCenso = patient.isCenso;
		patientEntity.replicateFlag = patient.replicateFlag;
		patientEntity.cityId = patient.cityId;
		patientEntity.street = patient.street;
		patientEntity.homeNumber = patient.homeNumber;
		patientEntity.neighborhood = patient.neighborhood;
		patientEntity.zipcode = patient.zipcode;
		patientEntity.complement = patient.complement;
		patientEntity.corporationId = patient.corporationId;
		patientEntity.status = patient.status;
		patientEntity.error = patient.error;
		patientEntity.codIntegration = patient.codIntegration;
		patientEntity.planRegulamentado = patient.planRegulamentado;
		patientEntity.planoTipo = patient.planoTipo;
		patientEntity.planoAbrangencia = patient.planoAbrangencia;
		patientEntity.planoTipoRede = patient.planoTipoRede;
		patientEntity.planoCarencia = patient.planoCarencia;
		patientEntity.gruposCidadesIbge = patient.gruposCidadesIbge;
		patientEntity.isRn = patient.isRn;
		patientEntity.carteiraNacionalSaude = patient.carteiraNacionalSaude;
		patientEntity.idade = patient.idade;
		patientEntity.sexo = patient.sexo;
		patientEntity.massa = patient.massa;
		patientEntity.altura = patient.altura;
		patientEntity.superficieCorporal = patient.superficieCorporal;
		return patientEntity;
	}
}
