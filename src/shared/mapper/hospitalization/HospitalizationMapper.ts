import { Hospitalization } from 'src/core/domain/Hospitalization';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { PatientMapper } from './PatientMapper';
import { CensoDados } from 'src/core/domain/CensoDados';
import { Patient } from 'src/core/domain/Patient';
import { BedHospitalMapper } from '../bedHospital/BedHospitalMapper';

export class HospitalizationMapper {
	public static toHospitalizationDomain(
		hospitalizationEntity: HospitalizationEntity,
	): Hospitalization {
		return new Hospitalization(
			hospitalizationEntity.id,
			hospitalizationEntity.created,
			hospitalizationEntity.updated,
			hospitalizationEntity.admissionIn,
			hospitalizationEntity.isAdmissionOutCenso,
			hospitalizationEntity.enabled,
			hospitalizationEntity.isNewborn,
			hospitalizationEntity.isBill,
			hospitalizationEntity.regime,
			hospitalizationEntity.admissionOut,
			hospitalizationEntity.admissionOutCreated,
			hospitalizationEntity.admissionOutUserId,
			hospitalizationEntity.admissionOutCreatedFirstTime,
			hospitalizationEntity.patient
				? PatientMapper.toPatientDomain(hospitalizationEntity.patient)
				: null,
			hospitalizationEntity.outPat,
			hospitalizationEntity.altaPrevista,
			hospitalizationEntity.altaPrevistaIA,
			hospitalizationEntity.altaPrevistaIAMin,
			hospitalizationEntity.altaPrevistaIAMax,
			hospitalizationEntity.altaPrevistaMI4U,
			hospitalizationEntity.obs,
			hospitalizationEntity.nomeMedicoResponsavel,
			hospitalizationEntity.contatoMedicoResponsavel,
			hospitalizationEntity.crmMedicoResponsavel,
			hospitalizationEntity.ufcrmMedicoResponsavel,
			hospitalizationEntity.especialidadeMedicoResponsavel,
			hospitalizationEntity.numeroGuia,
			hospitalizationEntity.userId,
			hospitalizationEntity.isEventoAdverso,
			hospitalizationEntity.eventoAdversoObs,
			hospitalizationEntity.isInternacaoDiaAnterior,
			hospitalizationEntity.carater,
			hospitalizationEntity.caraterTipo,
			hospitalizationEntity.hasPrenatal,
			hospitalizationEntity.isPregnant,
			hospitalizationEntity.isPuerperium,
			hospitalizationEntity.numeroSenha,
			hospitalizationEntity.clinicalHistory,
			hospitalizationEntity.dataEmissaoGuia,
			hospitalizationEntity.supervisorId,
			hospitalizationEntity.palliativeCare,
			hospitalizationEntity.codigoMedicoResponsavel,
			hospitalizationEntity.conselhoMedicoResponsavel,
			hospitalizationEntity.dataHoraSolicitacao,
			hospitalizationEntity.codIntegration,
			hospitalizationEntity.dataAuthorizationGuide,
			hospitalizationEntity.realCost,
			hospitalizationEntity.codigoSituacaoTiss,
			hospitalizationEntity.descricaoSituacaoTiss,
			hospitalizationEntity.longaDecisao,
			hospitalizationEntity.prob,
			hospitalizationEntity.isEditableRealCost,
			hospitalizationEntity.curtaPermanencia,
			hospitalizationEntity.outDoctorName,
			hospitalizationEntity.outDoctorUfCrm,
			hospitalizationEntity.outDoctorCrm,
			hospitalizationEntity.outDoctorSpeciality,
			hospitalizationEntity.outDoctorIsResponsible,
			hospitalizationEntity.numeroRegistro,
			hospitalizationEntity.procedencia,
			hospitalizationEntity.curtaPermanenciaDate,
			hospitalizationEntity.curtaPermanenciaUser,
			hospitalizationEntity.bedHospitals?.map((bed) =>
				BedHospitalMapper.toBedHospitalDomain(bed),
			),
		);
	}

	public static fromCensoDados(
		censo: CensoDados,
		patient: Patient,
	): Hospitalization {
		return new Hospitalization(
			null, // id
			new Date(), // created
			null, // updated
			censo.dataInternacao, // admissionIn
			null, // isAdmissionOutCenso
			1, // enabled (valor default)
			censo.recemNascido ? 1 : 0, // isNewborn (convertendo boolean para number)
			null, // isBill
			null, // regime
			censo.dataAlta, // admissionOut
			null, // admissionOutCreated
			censo.userId, // admissionOutUserId (utilizado aqui para exemplificar)
			null, // admissionOutCreatedFirstTime
			patient, // patient (não há mapeamento direto)
			null, // outPat
			censo.previsaoAlta, // altaPrevista
			null, // altaPrevistaIA
			null, // altaPrevistaIAMin
			null, // altaPrevistaIAMax
			null, // altaPrevistaMI4U
			censo.motivoAlta, // obs – utilizando o campo de motivo de alta como observação
			null, // nomeMedicoResponsavel
			null, // contatoMedicoResponsavel
			null, // crmMedicoResponsavel
			null, // ufcrmMedicoResponsavel
			null, // especialidadeMedicoResponsavel
			censo.codigoGuia, // numeroGuia
			censo.userId, // userId
			null, // isEventoAdverso
			null, // eventoAdversoObs
			null, // isInternacaoDiaAnterior
			censo.caraterInternacao, // carater
			censo.tipoInternacao, // caraterTipo
			null, // hasPrenatal
			null, // isPregnant
			null, // isPuerperium
			null, // numeroSenha
			// Para o campo clinicalHistory, concatenamos o diagnóstico primário e, se existir, o secundário
			`${censo.diagnostico}${censo.diagnosticoSecundario ? ' - ' + censo.diagnosticoSecundario : ''}`,
			censo.dataPlanoDesde, // dataEmissaoGuia (utilizando a data de início do plano)
			null, // supervisorId
			null, // palliativeCare
			null, // codigoMedicoResponsavel
			null, // conselhoMedicoResponsavel
			null, // dataHoraSolicitacao
			null, // codIntegration
			null, // dataAuthorizationGuide
			null, // realCost
			null, // codigoSituacaoTiss
			null, // descricaoSituacaoTiss
			null, // longaDecisao
			null, // prob
			null, // isEditableRealCost
			censo.diariasAutorizadas, // curtaPermanencia (usando as diárias autorizadas)
			null, // outDoctorName
			null, // outDoctorUfCrm
			null, // outDoctorCrm
			null, // outDoctorSpeciality
			null, // outDoctorIsResponsible
			null, // numeroRegistro
			null, // procedencia
			null, // curtaPermanenciaDate
			null, // curtaPermanenciaUser
		);
	}
	/* istanbul ignore next */
	public static toEntity(
		hospitalization: Hospitalization | Partial<Hospitalization>,
	): HospitalizationEntity {
		const entity = new HospitalizationEntity();
		entity.id = hospitalization.id;
		entity.created = hospitalization.created;
		entity.updated = hospitalization.updated;
		entity.admissionIn = hospitalization.admissionIn;
		entity.isAdmissionOutCenso = hospitalization.isAdmissionOutCenso;
		entity.enabled = hospitalization.enabled;
		entity.isNewborn = hospitalization.isNewborn;
		entity.isBill = hospitalization.isBill;
		entity.regime = hospitalization.regime;
		entity.admissionOut = hospitalization.admissionOut;
		entity.admissionOutCreated = hospitalization.admissionOutCreated;
		entity.admissionOutUserId = hospitalization.admissionOutUserId;
		entity.admissionOutCreatedFirstTime =
			hospitalization.admissionOutCreatedFirstTime;
		entity.patient = hospitalization.patient
			? PatientMapper.toEntity(hospitalization.patient)
			: null;
		entity.outPat = hospitalization.outPat;
		entity.altaPrevista = hospitalization.altaPrevista;
		entity.altaPrevistaIA = hospitalization.altaPrevistaIA;
		entity.altaPrevistaIAMin = hospitalization.altaPrevistaIAMin;
		entity.altaPrevistaIAMax = hospitalization.altaPrevistaIAMax;
		entity.altaPrevistaMI4U = hospitalization.altaPrevistaMI4U;
		entity.obs = hospitalization.obs;
		entity.nomeMedicoResponsavel = hospitalization.nomeMedicoResponsavel;
		entity.contatoMedicoResponsavel = hospitalization.contatoMedicoResponsavel;
		entity.crmMedicoResponsavel = hospitalization.crmMedicoResponsavel;
		entity.ufcrmMedicoResponsavel = hospitalization.ufcrmMedicoResponsavel;
		entity.especialidadeMedicoResponsavel =
			hospitalization.especialidadeMedicoResponsavel;
		entity.numeroGuia = hospitalization.numeroGuia;
		entity.userId = hospitalization.userId;
		entity.isEventoAdverso = hospitalization.isEventoAdverso;
		entity.eventoAdversoObs = hospitalization.eventoAdversoObs;
		entity.isInternacaoDiaAnterior = hospitalization.isInternacaoDiaAnterior;
		entity.carater = hospitalization.carater;
		entity.caraterTipo = hospitalization.caraterTipo;
		entity.hasPrenatal = hospitalization.hasPrenatal;
		entity.isPregnant = hospitalization.isPregnant;
		entity.isPuerperium = hospitalization.isPuerperium;
		entity.numeroSenha = hospitalization.numeroSenha;
		entity.clinicalHistory = hospitalization.clinicalHistory;
		entity.dataEmissaoGuia = hospitalization.dataEmissaoGuia;
		entity.supervisorId = hospitalization.supervisorId;
		entity.palliativeCare = hospitalization.palliativeCare;
		entity.codigoMedicoResponsavel = hospitalization.codigoMedicoResponsavel;
		entity.conselhoMedicoResponsavel =
			hospitalization.conselhoMedicoResponsavel;
		entity.dataHoraSolicitacao = hospitalization.dataHoraSolicitacao;
		entity.codIntegration = hospitalization.codIntegration;
		entity.dataAuthorizationGuide = hospitalization.dataAuthorizationGuide;
		entity.realCost = hospitalization.realCost;
		entity.codigoSituacaoTiss = hospitalization.codigoSituacaoTiss;
		entity.descricaoSituacaoTiss = hospitalization.descricaoSituacaoTiss;
		entity.longaDecisao = hospitalization.longaDecisao;
		entity.prob = hospitalization.prob;
		entity.isEditableRealCost = hospitalization.isEditableRealCost;
		entity.curtaPermanencia = hospitalization.curtaPermanencia;
		entity.outDoctorName = hospitalization.outDoctorName;
		entity.outDoctorUfCrm = hospitalization.outDoctorUfCrm;
		entity.outDoctorCrm = hospitalization.outDoctorCrm;
		entity.outDoctorSpeciality = hospitalization.outDoctorSpeciality;
		entity.outDoctorIsResponsible = hospitalization.outDoctorIsResponsible;
		entity.numeroRegistro = hospitalization.numeroRegistro;
		entity.procedencia = hospitalization.procedencia;
		entity.curtaPermanenciaDate = hospitalization.curtaPermanenciaDate;
		entity.curtaPermanenciaUser = hospitalization.curtaPermanenciaUser;
		return entity;
	}
}
