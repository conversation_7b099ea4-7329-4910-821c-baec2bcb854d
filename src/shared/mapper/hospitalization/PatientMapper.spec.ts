import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { Patient } from 'src/core/domain/Patient';
import { PatientMapper } from './PatientMapper';
import { CensoDados } from 'src/core/domain/CensoDados';

describe('PatientMapper', () => {
	it('deve mapear corretamente PatientEntity para Patient', () => {
		const patientEntity: PatientEntity = {
			id: 1,
			enabled: 1,
			created: new Date('2024-01-01'),
			updated: new Date('2024-01-02'),
			userId: 123,
			name: '<PERSON>',
			email: '<EMAIL>',
			birthday: new Date('1990-05-20'),
			companyId: 456,
			planId: 789,
		} as PatientEntity;

		const result: Patient = PatientMapper.toPatientDomain(patientEntity);

		expect(result).toBeInstanceOf(Patient);
		expect(result.id).toBe(1);
		expect(result.enabled).toBe(1);
		expect(result.created).toEqual(new Date('2024-01-01'));
		expect(result.updated).toEqual(new Date('2024-01-02'));
		expect(result.userId).toBe(123);
		expect(result.name).toBe('<PERSON> <PERSON>');
		expect(result.email).toBe('<EMAIL>');
		expect(result.birthday).toEqual(new Date('1990-05-20'));
		expect(result.companyId).toBe(456);
		expect(result.planId).toBe(789);
	});

	it('deve lidar corretamente com valores nulos ou undefined', () => {
		const patientEntity: PatientEntity = {
			id: 2,
			enabled: 0,
			created: null,
			updated: null,
			userId: null,
			name: null,
			email: null,
		} as unknown as PatientEntity;

		const result: Patient = PatientMapper.toPatientDomain(patientEntity);

		expect(result.id).toBe(2);
		expect(result.enabled).toBe(0);
		expect(result.created).toBeNull();
		expect(result.updated).toBeNull();
		expect(result.userId).toBeNull();
		expect(result.name).toBeNull();
		expect(result.email).toBeNull();
	});

	it('deve mapear corretamente CensoDados para Patient', () => {
		const censoDados: CensoDados = {
			id: 1,
			censoId: 10,
			companyId: 20,
			userId: 123,
			dataCriacao: new Date('2024-01-01'),
			conflito: 0,
			data: new Date('2024-01-02'),
			municipio: 'São Paulo',
			hospitalCredenciado: 'Hospital ABC',
			controle: 'Controle XYZ',
			dtNascimento: new Date('1990-05-20'),
			dataInternacao: new Date('2024-02-01'),
			dataAlta: new Date('2024-02-10'),
			motivoAlta: 'Recuperação',
			diagnostico: 'Diagnóstico 1',
			diagnosticoSecundario: 'Diagnóstico 2',
			previsaoAlta: new Date('2024-02-15'),
			caraterInternacao: 'Eletiva',
			tipoInternacao: 'Clínico',
			codigoGuia: '123456',
			altoCustoStatus: 'Alto Custo',
			nomeBeneficiario: 'João Silva',
			codBeneficiario: 'BEN123',
			cidadeBeneficiario: 'São Paulo',
			estadoBeneficiario: 'SP',
			recemNascido: false,
			tipoCliente: 'Particular',
			valorDiaria: 1000,
			regionalBeneficiario: 'Regional SP',
			tipoControle: 'Manual',
			diariasAutorizadas: 5,
			codigoHospital: 'HOSP123',
			codigoPlano: '789',
			nomePlano: 'Plano Ouro',
			codigoEmpresa: '100',
			nomeEmpresa: 'Empresa XYZ',
			statusPlano: 'Ativo',
			dataPlanoDesde: new Date('2022-01-01'),
		} as CensoDados;

		const result: Patient = PatientMapper.fromCensoDados(censoDados);

		expect(result).toBeInstanceOf(Patient);
		expect(result.id).toBeNull();
		expect(result.enabled).toBe(1);
		expect(result.created).toBeInstanceOf(Date);
		expect(result.updated).toBeInstanceOf(Date);
		expect(result.userId).toBe(123);
		expect(result.name).toBe('João Silva');
		expect(result.codBeneficiario).toBe('BEN123');
		expect(result.birthday).toEqual(new Date('1990-05-20'));
		expect(result.planId).toBe(789);
		expect(result.planStartDate).toEqual(new Date('2022-01-01'));
		expect(result.startDate).toEqual(new Date('2024-02-01'));
		expect(result.image).toBe('5.jpg');
		expect(result.patientCity).toBe('São Paulo');
		expect(result.regHosp).toBe('HOSP123');
		expect(result.status).toBe('Ativo');
		expect(result.isRn).toBe(0);
	});

	it('deve lidar corretamente com valores nulos ou inválidos', () => {
		const censoDados: CensoDados = {
			id: 2,
			censoId: null,
			companyId: null,
			userId: null,
			dataCriacao: null,
			conflito: 0,
			data: null,
			municipio: null,
			hospitalCredenciado: null,
			controle: null,
			dtNascimento: null,
			dataInternacao: null,
			dataAlta: null,
			motivoAlta: null,
			diagnostico: null,
			diagnosticoSecundario: null,
			previsaoAlta: null,
			caraterInternacao: null,
			tipoInternacao: null,
			codigoGuia: null,
			altoCustoStatus: null,
			nomeBeneficiario: null,
			codBeneficiario: null,
			cidadeBeneficiario: null,
			estadoBeneficiario: null,
			recemNascido: null,
			tipoCliente: null,
			valorDiaria: null,
			regionalBeneficiario: null,
			tipoControle: null,
			diariasAutorizadas: null,
			codigoHospital: null,
			codigoPlano: undefined,
			nomePlano: null,
			codigoEmpresa: null,
			nomeEmpresa: null,
			statusPlano: null,
			dataPlanoDesde: null,
		} as CensoDados;

		const result: Patient = PatientMapper.fromCensoDados(censoDados);

		expect(result.id).toBeNull();
		expect(result.enabled).toBe(1);
		expect(result.created).toBeInstanceOf(Date);
		expect(result.updated).toBeInstanceOf(Date);
		expect(result.userId).toBeNull();
		expect(result.name).toBeNull();
		expect(result.codBeneficiario).toBeNull();
		expect(result.birthday).toBeNull();
		expect(result.planId).toBeNull();
		expect(result.planStartDate).toBeNull();
		expect(result.startDate).toBeNull();
		expect(result.image).toBe('5.jpg');
		expect(result.patientCity).toBeNull();
		expect(result.regHosp).toBeNull();
		expect(result.status).toBeNull();
		expect(result.isRn).toBe(0);
	});

	it('recem nascido true deve ser mapeado para 1', () => {
		const censoDados: CensoDados = {
			id: 1,
			censoId: 10,
			companyId: 20,
			userId: 123,
			dataCriacao: new Date('2024-01-01'),
			conflito: 0,
			data: new Date('2024-01-02'),
			municipio: 'São Paulo',
			hospitalCredenciado: 'Hospital ABC',
			controle: 'Controle XYZ',
			dtNascimento: new Date('1990-05-20'),
			dataInternacao: new Date('2024-02-01'),
			dataAlta: new Date('2024-02-10'),
			motivoAlta: 'Recuperação',
			diagnostico: 'Diagnóstico 1',
			diagnosticoSecundario: 'Diagnóstico 2',
			previsaoAlta: new Date('2024-02-15'),
			caraterInternacao: 'Eletiva',
			tipoInternacao: 'Clínico',
			codigoGuia: '123456',
			altoCustoStatus: 'Alto Custo',
			nomeBeneficiario: 'João Silva',
			codBeneficiario: 'BEN123',
			cidadeBeneficiario: 'São Paulo',
			estadoBeneficiario: 'SP',
			recemNascido: true,
			tipoCliente: 'Particular',
			valorDiaria: 1000,
			regionalBeneficiario: 'Regional SP',
			tipoControle: 'Manual',
			diariasAutorizadas: 5,
			codigoHospital: 'HOSP123',
			codigoPlano: '789',
			nomePlano: 'Plano Ouro',
			codigoEmpresa: '100',
			nomeEmpresa: 'Empresa XYZ',
			statusPlano: 'Ativo',
			dataPlanoDesde: new Date('2022-01-01'),
		} as CensoDados;

		const result: Patient = PatientMapper.fromCensoDados(censoDados);

		expect(result).toBeInstanceOf(Patient);
		expect(result.id).toBeNull();
		expect(result.enabled).toBe(1);
		expect(result.created).toBeInstanceOf(Date);
		expect(result.updated).toBeInstanceOf(Date);
		expect(result.userId).toBe(123);
		expect(result.name).toBe('João Silva');
		expect(result.codBeneficiario).toBe('BEN123');
		expect(result.birthday).toEqual(new Date('1990-05-20'));
		expect(result.planId).toBe(789);
		expect(result.planStartDate).toEqual(new Date('2022-01-01'));
		expect(result.startDate).toEqual(new Date('2024-02-01'));
		expect(result.image).toBe('5.jpg');
		expect(result.patientCity).toBe('São Paulo');
		expect(result.regHosp).toBe('HOSP123');
		expect(result.status).toBe('Ativo');
		expect(result.isRn).toBe(1);
	});
});

describe('PatientMapper.toEntity', () => {
	it('deve mapear corretamente um objeto Patient (ou Partial<Patient>) para PatientEntity', () => {
		const patient: Patient = {
			id: 1,
			enabled: 1,
			created: new Date('2023-01-01T00:00:00Z'),
			updated: new Date('2023-01-02T00:00:00Z'),
			userId: 101,
			name: 'Teste Nome',
			email: '<EMAIL>',
			motherName: 'Mãe Teste',
			companyId: 202,
			gender: 'M',
			codBeneficiario: 'COD123',
			birthday: new Date('1990-01-01T00:00:00Z'),
			planId: 303,
			planStartDate: new Date('2022-01-01T00:00:00Z'),
			planEndDate: new Date('2023-01-01T00:00:00Z'),
			group: 'GrupoA',
			occupation: 'Engenheiro',
			pregnantAddInfo: 'Info Gestante',
			lactatingAddInfo: 'Info Lactante',
			permissionAppMaxDate: new Date('2023-06-01T00:00:00Z'),
			startDate: new Date('2024-01-01T00:00:00Z'),
			image: 'imagem.jpg',
			tel: '*********',
			patientCity: 'CidadeTeste',
			codCidadeIbge: '123456',
			regHosp: 'HOSP001',
			color: 'Branco',
			cpf: '111.222.333-44',
			internadoRepasse: 0,
			isCenso: 1,
			replicateFlag: 0,
			cityId: 404,
			street: 'Rua Teste',
			homeNumber: '10',
			neighborhood: 'Bairro Teste',
			zipcode: '00000-000',
			complement: 'Complemento',
			corporationId: 505,
			status: 'Ativo',
			error: null,
			codIntegration: 'INT-001',
			planoTipo: 'Tipo1',
			planoAbrangencia: 'Nacional',
			planoTipoRede: 'RedeA',
			planoCarencia: '30 dias',
			gruposCidadesIbge: 'GrupoIBGE',
			isRn: 0,
			carteiraNacionalSaude: 'CNS-001',
			idade: 33,
			sexo: 'M',
			massa: 70,
			altura: 1.75,
			superficieCorporal: 1.8,
		};

		const patientEntity: PatientEntity = PatientMapper.toEntity(patient);

		expect(patientEntity.id).toBe(patient.id);
		expect(patientEntity.enabled).toBe(patient.enabled);
		expect(patientEntity.created).toEqual(patient.created);
		expect(patientEntity.updated).toEqual(patient.updated);
		expect(patientEntity.userId).toBe(patient.userId);
		expect(patientEntity.name).toBe(patient.name);
		expect(patientEntity.email).toBe(patient.email);
		expect(patientEntity.motherName).toBe(patient.motherName);
		expect(patientEntity.companyId).toBe(patient.companyId);
		expect(patientEntity.gender).toBe(patient.gender);
		expect(patientEntity.codBeneficiario).toBe(patient.codBeneficiario);
		expect(patientEntity.birthday).toEqual(patient.birthday);
		expect(patientEntity.planId).toBe(patient.planId);
		expect(patientEntity.planStartDate).toEqual(patient.planStartDate);
		expect(patientEntity.planEndDate).toEqual(patient.planEndDate);
		expect(patientEntity.group).toBe(patient.group);
		expect(patientEntity.occupation).toBe(patient.occupation);
		expect(patientEntity.pregnantAddInfo).toBe(patient.pregnantAddInfo);
		expect(patientEntity.lactatingAddInfo).toBe(patient.lactatingAddInfo);
		expect(patientEntity.permissionAppMaxDate).toEqual(
			patient.permissionAppMaxDate,
		);
		expect(patientEntity.startDate).toEqual(patient.startDate);
		expect(patientEntity.image).toBe(patient.image);
		expect(patientEntity.tel).toBe(patient.tel);
		expect(patientEntity.patientCity).toBe(patient.patientCity);
		expect(patientEntity.codCidadeIbge).toBe(patient.codCidadeIbge);
		expect(patientEntity.regHosp).toBe(patient.regHosp);
		expect(patientEntity.color).toBe(patient.color);
		expect(patientEntity.cpf).toBe(patient.cpf);
		expect(patientEntity.internadoRepasse).toBe(patient.internadoRepasse);
		expect(patientEntity.isCenso).toBe(patient.isCenso);
		expect(patientEntity.replicateFlag).toBe(patient.replicateFlag);
		expect(patientEntity.cityId).toBe(patient.cityId);
		expect(patientEntity.street).toBe(patient.street);
		expect(patientEntity.homeNumber).toBe(patient.homeNumber);
		expect(patientEntity.neighborhood).toBe(patient.neighborhood);
		expect(patientEntity.zipcode).toBe(patient.zipcode);
		expect(patientEntity.complement).toBe(patient.complement);
		expect(patientEntity.corporationId).toBe(patient.corporationId);
		expect(patientEntity.status).toBe(patient.status);
		expect(patientEntity.error).toBe(patient.error);
		expect(patientEntity.codIntegration).toBe(patient.codIntegration);
		expect(patientEntity.planRegulamentado).toBe(patient.planRegulamentado);
		expect(patientEntity.planoTipo).toBe(patient.planoTipo);
		expect(patientEntity.planoAbrangencia).toBe(patient.planoAbrangencia);
		expect(patientEntity.planoTipoRede).toBe(patient.planoTipoRede);
		expect(patientEntity.planoCarencia).toBe(patient.planoCarencia);
		expect(patientEntity.gruposCidadesIbge).toBe(patient.gruposCidadesIbge);
		expect(patientEntity.isRn).toBe(patient.isRn);
		expect(patientEntity.carteiraNacionalSaude).toBe(
			patient.carteiraNacionalSaude,
		);
		expect(patientEntity.idade).toBe(patient.idade);
		expect(patientEntity.sexo).toBe(patient.sexo);
		expect(patientEntity.massa).toBe(patient.massa);
		expect(patientEntity.altura).toBe(patient.altura);
		expect(patientEntity.superficieCorporal).toBe(patient.superficieCorporal);
	});
});
