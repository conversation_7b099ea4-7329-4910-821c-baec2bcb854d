import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { PatientMapper } from './PatientMapper';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { Patient } from 'src/core/domain/Patient';
import { HospitalizationMapper } from './HospitalizationMapper';
import { CensoDados } from 'src/core/domain/CensoDados';

jest.mock('./PatientMapper');

describe('PatientWayMapper', () => {
	beforeEach(() => {
		jest.clearAllMocks();
	});

	it('deve mapear corretamente uma PatientWayEntity para PatientWay com um Patient', () => {
		const patientEntityMock = {
			id: 1,
			enabled: 1,
			created: new Date('2024-01-01'),
			updated: new Date('2024-01-02'),
			name: '<PERSON>',
		};

		const patientWayEntity: HospitalizationEntity = {
			id: 10,
			created: new Date('2024-02-01'),
			updated: new Date('2024-02-02'),
			admissionIn: new Date('2024-02-03'),
			isAdmissionOutCenso: 1,
			enabled: 1,
			isNewborn: 0,
			isBill: 1,
			regime: 'Hospitalar',
			patient: patientEntityMock,
			admissionOut: null,
			admissionOutCreated: null,
			bedHospitals: [],
		} as HospitalizationEntity;

		const patientDomainMock: Patient = new Patient(
			1,
			1,
			new Date('2024-01-01'),
			new Date('2024-01-02'),
			null,
			'João Silva',
		);

		(PatientMapper.toPatientDomain as jest.Mock).mockReturnValue(
			patientDomainMock,
		);

		const result =
			HospitalizationMapper.toHospitalizationDomain(patientWayEntity);

		expect(result).toBeInstanceOf(Hospitalization);
		expect(result.id).toBe(10);
		expect(result.created).toEqual(new Date('2024-02-01'));
		expect(result.updated).toEqual(new Date('2024-02-02'));
		expect(result.admissionIn).toEqual(new Date('2024-02-03'));
		expect(result.isAdmissionOutCenso).toBe(1);
		expect(result.enabled).toBe(1);
		expect(result.isNewborn).toBe(0);
		expect(result.isBill).toBe(1);
		expect(result.regime).toBe('Hospitalar');
		expect(result.patient).toBeInstanceOf(Patient);
		expect(result.patient).toEqual(patientDomainMock);
		expect(result.admissionOut).toBeNull();
		expect(result.admissionOutCreated).toBeNull();

		expect(PatientMapper.toPatientDomain).toHaveBeenCalledTimes(1);
		expect(PatientMapper.toPatientDomain).toHaveBeenCalledWith(
			patientEntityMock,
		);
	});

	it('deve mapear corretamente uma PatientWayEntity para PatientWay sem Patient', () => {
		const patientWayEntity: HospitalizationEntity = {
			id: 11,
			created: new Date('2024-03-01'),
			updated: null,
			admissionIn: new Date('2024-03-02'),
			isAdmissionOutCenso: 0,
			enabled: 0,
			isNewborn: 1,
			isBill: 0,
			regime: 'Ambulatorial',
			patient: undefined, // Sem relacionamento
			admissionOut: null,
		} as HospitalizationEntity;

		(PatientMapper.toPatientDomain as jest.Mock).mockReturnValue(undefined);

		const result =
			HospitalizationMapper.toHospitalizationDomain(patientWayEntity);

		expect(result).toBeInstanceOf(Hospitalization);
		expect(result.id).toBe(11);
		expect(result.created).toEqual(new Date('2024-03-01'));
		expect(result.updated).toBeNull();
		expect(result.admissionIn).toEqual(new Date('2024-03-02'));
		expect(result.isAdmissionOutCenso).toBe(0);
		expect(result.enabled).toBe(0);
		expect(result.isNewborn).toBe(1);
		expect(result.isBill).toBe(0);
		expect(result.regime).toBe('Ambulatorial');
		expect(result.patient).toBeNull();
	});

	it('deve mapear corretamente um CensoDados para PatientWay com diagnostico secundário', () => {
		const censo = new CensoDados(
			100, // id
			200, // censoId
			300, // companyId
			400, // userId
			new Date('2024-04-01T00:00:00Z'), // dataCriacao
			1, // conflito
			new Date('2024-04-02T00:00:00Z'), // data
			'Municipio Teste', // municipio
			'Hospital Teste', // hospitalCredenciado
			'Controle Teste', // controle
			new Date('1990-01-01T00:00:00Z'), // dtNascimento
			new Date('2024-04-03T00:00:00Z'), // dataInternacao
			new Date('2024-04-04T00:00:00Z'), // dataAlta
			'Motivo Alta Teste', // motivoAlta
			'Diagnostico Principal', // diagnostico
			'Diagnostico Secundario', // diagnosticoSecundario
			new Date('2024-04-05T00:00:00Z'), // previsaoAlta
			'Carater Internacao Teste', // caraterInternacao
			'Tipo Internacao Teste', // tipoInternacao
			'Codigo Guia Teste', // codigoGuia
			'Alto Custo Teste', // altoCustoStatus
			'Nome Beneficiario Teste', // nomeBeneficiario
			'Cod Beneficiario Teste', // codBeneficiario
			'Cidade Beneficiario Teste', // cidadeBeneficiario
			'Estado Beneficiario Teste', // estadoBeneficiario
			true, // recemNascido
			'Tipo Cliente Teste', // tipoCliente
			0, // valorDiaria
			'Regional Beneficiario Teste', // regionalBeneficiario
			'Tipo Controle Teste', // tipoControle
			5, // diariasAutorizadas
			'Codigo Hospital Teste', // codigoHospital
			'Codigo Plano Teste', // codigoPlano
			'Nome Plano Teste', // nomePlano
			'Codigo Empresa Teste', // codigoEmpresa
			'Nome Empresa Teste', // nomeEmpresa
			'Status Plano Teste', // statusPlano
			new Date('2024-04-06T00:00:00Z'), // dataPlanoDesde
			undefined, // censo (opcional)
			undefined, // tiposConflitos (opcional)
		);

		const patient = new Patient(
			500, // id
			1, // algum outro parâmetro
			new Date('2024-01-01T00:00:00Z'), // created
			new Date('2024-01-02T00:00:00Z'), // updated
			600, // exemplo de dado adicional
			'Paciente Teste', // nome
		);

		const result: Hospitalization = HospitalizationMapper.fromCensoDados(
			censo,
			patient,
		);

		expect(result).toBeInstanceOf(Hospitalization);
		expect(result.updated).toBeNull();
		expect(result.admissionIn).toEqual(new Date('2024-04-03T00:00:00Z'));
		expect(result.enabled).toBe(1);
		expect(result.isNewborn).toBe(1); // recemNascido true converte para 1
		expect(result.admissionOut).toEqual(new Date('2024-04-04T00:00:00Z'));
		expect(result.admissionOutUserId).toBe(400);
		expect(result.patient).toEqual(patient);
		expect(result.altaPrevista).toEqual(new Date('2024-04-05T00:00:00Z'));
		expect(result.obs).toBe('Motivo Alta Teste');
		expect(result.numeroGuia).toBe('Codigo Guia Teste');
		expect(result.userId).toBe(400);
		expect(result.carater).toBe('Carater Internacao Teste');
		expect(result.caraterTipo).toBe('Tipo Internacao Teste');
		expect(result.clinicalHistory).toBe(
			'Diagnostico Principal - Diagnostico Secundario',
		);
		expect(result.dataEmissaoGuia).toEqual(new Date('2024-04-06T00:00:00Z'));
		expect(result.curtaPermanencia).toBe(5);
	});

	it('deve mapear corretamente o clinicalHistory quando não houver diagnosticoSecundario', () => {
		const censo = new CensoDados(
			101, // id
			201, // censoId
			301, // companyId
			401, // userId
			new Date('2024-05-01T00:00:00Z'), // dataCriacao
			1, // conflito
			new Date('2024-05-02T00:00:00Z'), // data
			'Municipio Exemplo', // municipio
			'Hospital Exemplo', // hospitalCredenciado
			'Controle Exemplo', // controle
			new Date('1985-01-01T00:00:00Z'), // dtNascimento
			new Date('2024-05-03T00:00:00Z'), // dataInternacao
			new Date('2024-05-04T00:00:00Z'), // dataAlta
			'Motivo Alta Exemplo', // motivoAlta
			'Diagnostico Principal', // diagnostico
			'', // diagnosticoSecundario vazio
			new Date('2024-05-05T00:00:00Z'), // previsaoAlta
			'Carater Internacao Exemplo', // caraterInternacao
			'Tipo Internacao Exemplo', // tipoInternacao
			'Codigo Guia Exemplo', // codigoGuia
			'Alto Custo Exemplo', // altoCustoStatus
			'Nome Beneficiario Exemplo', // nomeBeneficiario
			'Cod Beneficiario Exemplo', // codBeneficiario
			'Cidade Beneficiario Exemplo', // cidadeBeneficiario
			'Estado Beneficiario Exemplo', // estadoBeneficiario
			false, // recemNascido
			'Tipo Cliente Exemplo', // tipoCliente
			0, // valorDiaria
			'Regional Beneficiario Exemplo', // regionalBeneficiario
			'Tipo Controle Exemplo', // tipoControle
			3, // diariasAutorizadas
			'Codigo Hospital Exemplo', // codigoHospital
			'Codigo Plano Exemplo', // codigoPlano
			'Nome Plano Exemplo', // nomePlano
			'Codigo Empresa Exemplo', // codigoEmpresa
			'Nome Empresa Exemplo', // nomeEmpresa
			'Status Plano Exemplo', // statusPlano
			new Date('2024-05-06T00:00:00Z'), // dataPlanoDesde
			undefined, // censo (opcional)
			undefined, // tiposConflitos (opcional)
		);

		const patient = new Patient(
			501,
			1,
			new Date('2024-02-01T00:00:00Z'),
			new Date('2024-02-02T00:00:00Z'),
			601,
			'Paciente Exemplo',
		);

		const result: Hospitalization = HospitalizationMapper.fromCensoDados(
			censo,
			patient,
		);

		expect(result).toBeInstanceOf(Hospitalization);
		expect(result.updated).toBeNull();
		expect(result.admissionIn).toEqual(new Date('2024-05-03T00:00:00Z'));
		expect(result.enabled).toBe(1);
		expect(result.isNewborn).toBe(0); // recemNascido false converte para 0
		expect(result.admissionOut).toEqual(new Date('2024-05-04T00:00:00Z'));
		expect(result.admissionOutUserId).toBe(401);
		expect(result.patient).toEqual(patient);
		expect(result.altaPrevista).toEqual(new Date('2024-05-05T00:00:00Z'));
		expect(result.obs).toBe('Motivo Alta Exemplo');
		expect(result.numeroGuia).toBe('Codigo Guia Exemplo');
		expect(result.userId).toBe(401);
		expect(result.carater).toBe('Carater Internacao Exemplo');
		expect(result.caraterTipo).toBe('Tipo Internacao Exemplo');
		expect(result.clinicalHistory).toBe('Diagnostico Principal');
		expect(result.dataEmissaoGuia).toEqual(new Date('2024-05-06T00:00:00Z'));
		expect(result.curtaPermanencia).toBe(3);
	});

	it('deve mapear patientWay para patientwayentity', () => {
		const domainPatientWay = new Hospitalization(
			1,
			new Date('2024-05-04T00:00:00Z'),
			new Date('2024-05-04T00:00:00Z'),
			new Date('2024-05-04T00:00:00Z'),
			2,
			1,
			0,
			0,
			'regime',
		);

		const entity = HospitalizationMapper.toEntity(domainPatientWay);

		expect(entity).toBeInstanceOf(HospitalizationEntity);
		expect(entity.id).toBe(domainPatientWay.id);
		expect(entity.created).toEqual(domainPatientWay.created);
		expect(entity.updated).toEqual(domainPatientWay.updated);
		expect(entity.admissionIn).toEqual(domainPatientWay.admissionIn);
		expect(entity.isAdmissionOutCenso).toBe(
			domainPatientWay.isAdmissionOutCenso,
		);
		expect(entity.enabled).toBe(domainPatientWay.enabled);
		expect(entity.isNewborn).toBe(domainPatientWay.isNewborn);
		expect(entity.isBill).toBe(domainPatientWay.isBill);
		expect(entity.regime).toBe(domainPatientWay.regime);
		expect(entity.admissionOut).toEqual(domainPatientWay.admissionOut);
		expect(entity.admissionOutCreated).toEqual(
			domainPatientWay.admissionOutCreated,
		);
		expect(entity.admissionOutUserId).toBe(domainPatientWay.admissionOutUserId);
		expect(entity.admissionOutCreatedFirstTime).toEqual(
			domainPatientWay.admissionOutCreatedFirstTime,
		);
		expect(entity.outPat).toBe(domainPatientWay.outPat);
		expect(entity.altaPrevista).toEqual(domainPatientWay.altaPrevista);
		expect(entity.altaPrevistaIA).toBe(domainPatientWay.altaPrevistaIA);
		expect(entity.altaPrevistaIAMin).toBe(domainPatientWay.altaPrevistaIAMin);
		expect(entity.altaPrevistaIAMax).toBe(domainPatientWay.altaPrevistaIAMax);
		expect(entity.altaPrevistaMI4U).toBe(domainPatientWay.altaPrevistaMI4U);
		expect(entity.obs).toBe(domainPatientWay.obs);
		expect(entity.nomeMedicoResponsavel).toBe(
			domainPatientWay.nomeMedicoResponsavel,
		);
		expect(entity.contatoMedicoResponsavel).toBe(
			domainPatientWay.contatoMedicoResponsavel,
		);
		expect(entity.crmMedicoResponsavel).toBe(
			domainPatientWay.crmMedicoResponsavel,
		);
		expect(entity.ufcrmMedicoResponsavel).toBe(
			domainPatientWay.ufcrmMedicoResponsavel,
		);
		expect(entity.especialidadeMedicoResponsavel).toBe(
			domainPatientWay.especialidadeMedicoResponsavel,
		);
		expect(entity.numeroGuia).toBe(domainPatientWay.numeroGuia);
		expect(entity.userId).toBe(domainPatientWay.userId);
		expect(entity.isEventoAdverso).toBe(domainPatientWay.isEventoAdverso);
		expect(entity.eventoAdversoObs).toBe(domainPatientWay.eventoAdversoObs);
		expect(entity.isInternacaoDiaAnterior).toBe(
			domainPatientWay.isInternacaoDiaAnterior,
		);
		expect(entity.carater).toBe(domainPatientWay.carater);
		expect(entity.caraterTipo).toBe(domainPatientWay.caraterTipo);
		expect(entity.hasPrenatal).toBe(domainPatientWay.hasPrenatal);
		expect(entity.isPregnant).toBe(domainPatientWay.isPregnant);
		expect(entity.isPuerperium).toBe(domainPatientWay.isPuerperium);
		expect(entity.numeroSenha).toBe(domainPatientWay.numeroSenha);
		expect(entity.clinicalHistory).toBe(domainPatientWay.clinicalHistory);
		expect(entity.dataEmissaoGuia).toEqual(domainPatientWay.dataEmissaoGuia);
		expect(entity.supervisorId).toBe(domainPatientWay.supervisorId);
		expect(entity.palliativeCare).toBe(domainPatientWay.palliativeCare);
		expect(entity.codigoMedicoResponsavel).toBe(
			domainPatientWay.codigoMedicoResponsavel,
		);
		expect(entity.conselhoMedicoResponsavel).toBe(
			domainPatientWay.conselhoMedicoResponsavel,
		);
		expect(entity.dataHoraSolicitacao).toEqual(
			domainPatientWay.dataHoraSolicitacao,
		);
		expect(entity.codIntegration).toBe(domainPatientWay.codIntegration);
		expect(entity.dataAuthorizationGuide).toEqual(
			domainPatientWay.dataAuthorizationGuide,
		);
		expect(entity.realCost).toBe(domainPatientWay.realCost);
		expect(entity.codigoSituacaoTiss).toBe(domainPatientWay.codigoSituacaoTiss);
		expect(entity.descricaoSituacaoTiss).toBe(
			domainPatientWay.descricaoSituacaoTiss,
		);
		expect(entity.longaDecisao).toBe(domainPatientWay.longaDecisao);
		expect(entity.prob).toBe(domainPatientWay.prob);
		expect(entity.isEditableRealCost).toBe(domainPatientWay.isEditableRealCost);
		expect(entity.curtaPermanencia).toBe(domainPatientWay.curtaPermanencia);
		expect(entity.outDoctorName).toBe(domainPatientWay.outDoctorName);
		expect(entity.outDoctorUfCrm).toBe(domainPatientWay.outDoctorUfCrm);
		expect(entity.outDoctorCrm).toBe(domainPatientWay.outDoctorCrm);
		expect(entity.outDoctorSpeciality).toBe(
			domainPatientWay.outDoctorSpeciality,
		);
		expect(entity.outDoctorIsResponsible).toBe(
			domainPatientWay.outDoctorIsResponsible,
		);
		expect(entity.numeroRegistro).toBe(domainPatientWay.numeroRegistro);
		expect(entity.procedencia).toBe(domainPatientWay.procedencia);
		expect(entity.curtaPermanenciaDate).toEqual(
			domainPatientWay.curtaPermanenciaDate,
		);
		expect(entity.curtaPermanenciaUser).toBe(
			domainPatientWay.curtaPermanenciaUser,
		);
	});

	it('deve mapear patientWay para patientwayentity', () => {
		const domainPatientWay = new Hospitalization(
			1,
			new Date('2024-05-04T00:00:00Z'),
			new Date('2024-05-04T00:00:00Z'),
			new Date('2024-05-04T00:00:00Z'),
			2,
			1,
			0,
			0,
			'regime',
		);

		const entity = HospitalizationMapper.toEntity(domainPatientWay);

		expect(entity).toBeInstanceOf(HospitalizationEntity);
		expect(entity.id).toBe(domainPatientWay.id);
		expect(entity.created).toEqual(domainPatientWay.created);
		expect(entity.updated).toEqual(domainPatientWay.updated);
		expect(entity.admissionIn).toEqual(domainPatientWay.admissionIn);
		expect(entity.isAdmissionOutCenso).toBe(
			domainPatientWay.isAdmissionOutCenso,
		);
		expect(entity.enabled).toBe(domainPatientWay.enabled);
		expect(entity.isNewborn).toBe(domainPatientWay.isNewborn);
		expect(entity.isBill).toBe(domainPatientWay.isBill);
		expect(entity.regime).toBe(domainPatientWay.regime);
		expect(entity.admissionOut).toEqual(domainPatientWay.admissionOut);
		expect(entity.admissionOutCreated).toEqual(
			domainPatientWay.admissionOutCreated,
		);
		expect(entity.admissionOutUserId).toBe(domainPatientWay.admissionOutUserId);
		expect(entity.admissionOutCreatedFirstTime).toEqual(
			domainPatientWay.admissionOutCreatedFirstTime,
		);
		expect(entity.outPat).toBe(domainPatientWay.outPat);
		expect(entity.altaPrevista).toEqual(domainPatientWay.altaPrevista);
		expect(entity.altaPrevistaIA).toBe(domainPatientWay.altaPrevistaIA);
		expect(entity.altaPrevistaIAMin).toBe(domainPatientWay.altaPrevistaIAMin);
		expect(entity.altaPrevistaIAMax).toBe(domainPatientWay.altaPrevistaIAMax);
		expect(entity.altaPrevistaMI4U).toBe(domainPatientWay.altaPrevistaMI4U);
		expect(entity.obs).toBe(domainPatientWay.obs);
		expect(entity.nomeMedicoResponsavel).toBe(
			domainPatientWay.nomeMedicoResponsavel,
		);
		expect(entity.contatoMedicoResponsavel).toBe(
			domainPatientWay.contatoMedicoResponsavel,
		);
		expect(entity.crmMedicoResponsavel).toBe(
			domainPatientWay.crmMedicoResponsavel,
		);
		expect(entity.ufcrmMedicoResponsavel).toBe(
			domainPatientWay.ufcrmMedicoResponsavel,
		);
		expect(entity.especialidadeMedicoResponsavel).toBe(
			domainPatientWay.especialidadeMedicoResponsavel,
		);
		expect(entity.numeroGuia).toBe(domainPatientWay.numeroGuia);
		expect(entity.userId).toBe(domainPatientWay.userId);
		expect(entity.isEventoAdverso).toBe(domainPatientWay.isEventoAdverso);
		expect(entity.eventoAdversoObs).toBe(domainPatientWay.eventoAdversoObs);
		expect(entity.isInternacaoDiaAnterior).toBe(
			domainPatientWay.isInternacaoDiaAnterior,
		);
		expect(entity.carater).toBe(domainPatientWay.carater);
		expect(entity.caraterTipo).toBe(domainPatientWay.caraterTipo);
		expect(entity.hasPrenatal).toBe(domainPatientWay.hasPrenatal);
		expect(entity.isPregnant).toBe(domainPatientWay.isPregnant);
		expect(entity.isPuerperium).toBe(domainPatientWay.isPuerperium);
		expect(entity.numeroSenha).toBe(domainPatientWay.numeroSenha);
		expect(entity.clinicalHistory).toBe(domainPatientWay.clinicalHistory);
		expect(entity.dataEmissaoGuia).toEqual(domainPatientWay.dataEmissaoGuia);
		expect(entity.supervisorId).toBe(domainPatientWay.supervisorId);
		expect(entity.palliativeCare).toBe(domainPatientWay.palliativeCare);
		expect(entity.codigoMedicoResponsavel).toBe(
			domainPatientWay.codigoMedicoResponsavel,
		);
		expect(entity.conselhoMedicoResponsavel).toBe(
			domainPatientWay.conselhoMedicoResponsavel,
		);
		expect(entity.dataHoraSolicitacao).toEqual(
			domainPatientWay.dataHoraSolicitacao,
		);
		expect(entity.codIntegration).toBe(domainPatientWay.codIntegration);
		expect(entity.dataAuthorizationGuide).toEqual(
			domainPatientWay.dataAuthorizationGuide,
		);
		expect(entity.realCost).toBe(domainPatientWay.realCost);
		expect(entity.codigoSituacaoTiss).toBe(domainPatientWay.codigoSituacaoTiss);
		expect(entity.descricaoSituacaoTiss).toBe(
			domainPatientWay.descricaoSituacaoTiss,
		);
		expect(entity.longaDecisao).toBe(domainPatientWay.longaDecisao);
		expect(entity.prob).toBe(domainPatientWay.prob);
		expect(entity.isEditableRealCost).toBe(domainPatientWay.isEditableRealCost);
		expect(entity.curtaPermanencia).toBe(domainPatientWay.curtaPermanencia);
		expect(entity.outDoctorName).toBe(domainPatientWay.outDoctorName);
		expect(entity.outDoctorUfCrm).toBe(domainPatientWay.outDoctorUfCrm);
		expect(entity.outDoctorCrm).toBe(domainPatientWay.outDoctorCrm);
		expect(entity.outDoctorSpeciality).toBe(
			domainPatientWay.outDoctorSpeciality,
		);
		expect(entity.outDoctorIsResponsible).toBe(
			domainPatientWay.outDoctorIsResponsible,
		);
		expect(entity.numeroRegistro).toBe(domainPatientWay.numeroRegistro);
		expect(entity.procedencia).toBe(domainPatientWay.procedencia);
		expect(entity.curtaPermanenciaDate).toEqual(
			domainPatientWay.curtaPermanenciaDate,
		);
		expect(entity.curtaPermanenciaUser).toBe(
			domainPatientWay.curtaPermanenciaUser,
		);
	});
});
