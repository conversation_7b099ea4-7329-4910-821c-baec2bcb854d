import { GuideStatusMapper } from 'src/shared/mapper/guide/GuideStatusMapper';
import { GuideStatusEntity } from 'src/gateway/entities/guideStatus.entity';
import { GuideStatus } from 'src/core/domain/GuideStatus';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { Guide } from 'src/core/domain/Guide';
import { GuideEntity } from 'src/gateway/entities/guide.entity';

describe('GuideStatusMapper', () => {
	describe('toDomain', () => {
		it('deve mapear um objeto de domínio GuideStatus para um GuideStatusEntity corretamente', () => {
			const dummyGuideStatusEntity: GuideStatusEntity = {
				id: 1,
				approvedBy: 100,
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				enabled: 1,
				guide: { id: 1 } as GuideEntity,
				justificativa: 50,
				obs: 'Observação de teste',
				status: 2,
				userId: 10,
			} as GuideStatusEntity;

			const guideDomain = new Guide(1, null, null, null);

			jest.spyOn(GuideMapper, 'toDomain').mockReturnValue(guideDomain);

			const result: GuideStatus = GuideStatusMapper.toDomain(
				dummyGuideStatusEntity,
			);

			const expected = new GuideStatus(
				dummyGuideStatusEntity.id,
				guideDomain,
				dummyGuideStatusEntity.approvedBy,
				dummyGuideStatusEntity.status,
				dummyGuideStatusEntity.justificativa,
				dummyGuideStatusEntity.obs,
				dummyGuideStatusEntity.created,
				dummyGuideStatusEntity.updated,
				dummyGuideStatusEntity.enabled,
				dummyGuideStatusEntity.userId,
			);

			expect(result).toEqual(expected);
		});

		it('deve mapear um objeto de domínio GuideStatus com guide null corretamente', () => {
			const dummyGuideStatusEntity: GuideStatusEntity = {
				id: 1,
				approvedBy: 100,
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				enabled: 1,
				guide: null,
				justificativa: 50,
				obs: 'Observação de teste',
				status: 2,
				userId: 10,
			} as GuideStatusEntity;

			const result: GuideStatus = GuideStatusMapper.toDomain(
				dummyGuideStatusEntity,
			);

			const expected = new GuideStatus(
				dummyGuideStatusEntity.id,
				null,
				dummyGuideStatusEntity.approvedBy,
				dummyGuideStatusEntity.status,
				dummyGuideStatusEntity.justificativa,
				dummyGuideStatusEntity.obs,
				dummyGuideStatusEntity.created,
				dummyGuideStatusEntity.updated,
				dummyGuideStatusEntity.enabled,
				dummyGuideStatusEntity.userId,
			);

			expect(result).toEqual(expected);
		});
	});
});
