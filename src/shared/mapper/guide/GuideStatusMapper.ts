import { GuideStatus } from 'src/core/domain/GuideStatus';
import { GuideStatusEntity } from 'src/gateway/entities/guideStatus.entity';
import { GuideMapper } from './GuideMapper';

export class GuideStatusMapper {
	public static toDomain(guideStatusEntity: GuideStatusEntity): GuideStatus {
		return new GuideStatus(
			guideStatusEntity.id,
			guideStatusEntity.guide
				? GuideMapper.toDomain(guideStatusEntity.guide)
				: null,
			guideStatusEntity.approvedBy,
			guideStatusEntity.status,
			guideStatusEntity.justificativa,
			guideStatusEntity.obs,
			guideStatusEntity.created,
			guideStatusEntity.updated,
			guideStatusEntity.enabled,
			guideStatusEntity.userId,
		);
	}
}
