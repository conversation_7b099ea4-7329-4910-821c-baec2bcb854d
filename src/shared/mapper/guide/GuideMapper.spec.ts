import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { Guide } from 'src/core/domain/Guide';
import { GuideEntity } from 'src/gateway/entities/guide.entity';
import { PatientMapper } from 'src/shared/mapper/hospitalization/PatientMapper';
import { HospitalizationMapper } from 'src/shared/mapper/hospitalization/HospitalizationMapper';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';
import { Patient } from 'src/core/domain/Patient';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { Hospital } from 'src/core/domain/Hospital';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';

describe('GuideMapper', () => {
	describe('toDomain', () => {
		it('deve mapear um GuideEntity para um objeto de domínio Guide corretamente', () => {
			const dummyGuideEntity: GuideEntity = {
				id: 1,
				patient: { id: 101 },
				patientWay: { id: 102 },
				hospital: { id: 103 },
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				numeroSenha: 'senha123',
				numeroGuia: 'guia123',
				dataEmissaoGuia: new Date('2023-01-03T00:00:00Z'),
				dataAutorizacaoGuia: new Date('2023-01-04T00:00:00Z'),
				speciality: 'Cardiology',
				type: 1,
				obs: 'Observation',
				obsDaily: 'Daily Observation',
				obsProcedure: 'Procedure Observation',
				obsDrugs: 'Drugs Observation',
				obsMaterials: 'Materials Observation',
				inicioPeriodo: new Date('2023-01-05T00:00:00Z'),
				finalPeriodo: new Date('2023-01-06T00:00:00Z'),
				valorTotal: '100.00',
				enabled: 1,
				userId: 201,
				auditorId: 301,
				tissLink: 'http://example.com',
				isViewed: 1,
				valueRequest: 50,
				valueSaved: 40,
				valueAuthorized: 45,
				isCenso: 0,
				reanalysis: 0,
				codIntegration: 'CODE123',
				obsDailyProvider: 'Daily Provider Obs',
				obsProcedureProvider: 'Procedure Provider Obs',
				obsDrugsProvider: 'Drugs Provider Obs',
				obsMaterialsProvider: 'Materials Provider Obs',
				motivoNegativa: 'Negative Reason',
				dataExpiracaoEdicao: new Date('2023-01-07T00:00:00Z'),
			} as GuideEntity;

			jest
				.spyOn(PatientMapper, 'toPatientDomain')
				.mockReturnValue({ id: 101 } as Patient);
			jest
				.spyOn(HospitalizationMapper, 'toHospitalizationDomain')
				.mockReturnValue({ id: 102 } as Hospitalization);
			jest
				.spyOn(HospitalMapper, 'toHospitalDomain')
				.mockReturnValue({ id: 103 } as Hospital);

			const result: Guide = GuideMapper.toDomain(dummyGuideEntity);

			const expected = new Guide(
				dummyGuideEntity.id,
				{ id: 101 } as Patient,
				{ id: 102 } as Hospitalization,
				{ id: 103 } as Hospital,
				dummyGuideEntity.created,
				dummyGuideEntity.updated,
				dummyGuideEntity.numeroSenha,
				dummyGuideEntity.numeroGuia,
				dummyGuideEntity.dataEmissaoGuia,
				dummyGuideEntity.dataAutorizacaoGuia,
				dummyGuideEntity.speciality,
				dummyGuideEntity.type,
				dummyGuideEntity.obs,
				dummyGuideEntity.obsDaily,
				dummyGuideEntity.obsProcedure,
				dummyGuideEntity.obsDrugs,
				dummyGuideEntity.obsMaterials,
				dummyGuideEntity.inicioPeriodo,
				dummyGuideEntity.finalPeriodo,
				dummyGuideEntity.valorTotal,
				dummyGuideEntity.enabled,
				dummyGuideEntity.userId,
				dummyGuideEntity.auditorId,
				dummyGuideEntity.tissLink,
				dummyGuideEntity.isViewed,
				dummyGuideEntity.valueRequest,
				dummyGuideEntity.valueSaved,
				dummyGuideEntity.valueAuthorized,
				dummyGuideEntity.isCenso,
				dummyGuideEntity.reanalysis,
				dummyGuideEntity.codIntegration,
				dummyGuideEntity.obsDailyProvider,
				dummyGuideEntity.obsProcedureProvider,
				dummyGuideEntity.obsDrugsProvider,
				dummyGuideEntity.obsMaterialsProvider,
				dummyGuideEntity.motivoNegativa,
				dummyGuideEntity.dataExpiracaoEdicao,
			);

			expect(result).toEqual(expected);
		});

		it('deve mapear um GuideEntity com Patient, PatientWay e Hospital como null', () => {
			const dummyGuideEntity: GuideEntity = {
				id: 1,
				patient: null,
				patientWay: null,
				hospital: null,
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				numeroSenha: 'senha123',
				numeroGuia: 'guia123',
				dataEmissaoGuia: new Date('2023-01-03T00:00:00Z'),
				dataAutorizacaoGuia: new Date('2023-01-04T00:00:00Z'),
				speciality: 'Cardiology',
				type: 1,
				obs: 'Observation',
				obsDaily: 'Daily Observation',
				obsProcedure: 'Procedure Observation',
				obsDrugs: 'Drugs Observation',
				obsMaterials: 'Materials Observation',
				inicioPeriodo: new Date('2023-01-05T00:00:00Z'),
				finalPeriodo: new Date('2023-01-06T00:00:00Z'),
				valorTotal: '100.00',
				enabled: 1,
				userId: 201,
				auditorId: 301,
				tissLink: 'http://example.com',
				isViewed: 1,
				valueRequest: 50,
				valueSaved: 40,
				valueAuthorized: 45,
				isCenso: 0,
				reanalysis: 0,
				codIntegration: 'CODE123',
				obsDailyProvider: 'Daily Provider Obs',
				obsProcedureProvider: 'Procedure Provider Obs',
				obsDrugsProvider: 'Drugs Provider Obs',
				obsMaterialsProvider: 'Materials Provider Obs',
				motivoNegativa: 'Negative Reason',
				dataExpiracaoEdicao: new Date('2023-01-07T00:00:00Z'),
			} as GuideEntity;

			jest.spyOn(PatientMapper, 'toPatientDomain').mockReturnValue(null);
			jest
				.spyOn(HospitalizationMapper, 'toHospitalizationDomain')
				.mockReturnValue(null);
			jest.spyOn(HospitalMapper, 'toHospitalDomain').mockReturnValue(null);

			const result: Guide = GuideMapper.toDomain(dummyGuideEntity);

			const expected = new Guide(
				dummyGuideEntity.id,
				null,
				null,
				null,
				dummyGuideEntity.created,
				dummyGuideEntity.updated,
				dummyGuideEntity.numeroSenha,
				dummyGuideEntity.numeroGuia,
				dummyGuideEntity.dataEmissaoGuia,
				dummyGuideEntity.dataAutorizacaoGuia,
				dummyGuideEntity.speciality,
				dummyGuideEntity.type,
				dummyGuideEntity.obs,
				dummyGuideEntity.obsDaily,
				dummyGuideEntity.obsProcedure,
				dummyGuideEntity.obsDrugs,
				dummyGuideEntity.obsMaterials,
				dummyGuideEntity.inicioPeriodo,
				dummyGuideEntity.finalPeriodo,
				dummyGuideEntity.valorTotal,
				dummyGuideEntity.enabled,
				dummyGuideEntity.userId,
				dummyGuideEntity.auditorId,
				dummyGuideEntity.tissLink,
				dummyGuideEntity.isViewed,
				dummyGuideEntity.valueRequest,
				dummyGuideEntity.valueSaved,
				dummyGuideEntity.valueAuthorized,
				dummyGuideEntity.isCenso,
				dummyGuideEntity.reanalysis,
				dummyGuideEntity.codIntegration,
				dummyGuideEntity.obsDailyProvider,
				dummyGuideEntity.obsProcedureProvider,
				dummyGuideEntity.obsDrugsProvider,
				dummyGuideEntity.obsMaterialsProvider,
				dummyGuideEntity.motivoNegativa,
				dummyGuideEntity.dataExpiracaoEdicao,
			);

			expect(result).toEqual(expected);
		});
	});

	describe('toEntity', () => {
		it('deve mapear um objeto de domínio Guide para um GuideEntity corretamente', () => {
			const dummyGuideDomain = new Guide(
				2,
				{ id: 201 } as Patient,
				{ id: 202 } as Hospitalization,
				{ id: 203 } as Hospital,
				new Date('2023-02-01T00:00:00Z'),
				new Date('2023-02-02T00:00:00Z'),
				'senha456',
				'guia456',
				new Date('2023-02-03T00:00:00Z'),
				new Date('2023-02-04T00:00:00Z'),
				'Neurology',
				2,
				'Obs Domain',
				'Obs Daily Domain',
				'Obs Procedure Domain',
				'Obs Drugs Domain',
				'Obs Materials Domain',
				new Date('2023-02-05T00:00:00Z'),
				new Date('2023-02-06T00:00:00Z'),
				'200.00',
				1,
				401,
				501,
				'http://example.org',
				1,
				60,
				55,
				58,
				0,
				0,
				'CODE456',
				'Obs Daily Provider Domain',
				'Obs Procedure Provider Domain',
				'Obs Drugs Provider Domain',
				'Obs Materials Provider Domain',
				'Negative Reason Domain',
				new Date('2023-02-07T00:00:00Z'),
			);

			jest
				.spyOn(PatientMapper, 'toEntity')
				.mockReturnValue({ id: 201 } as PatientEntity);
			jest
				.spyOn(HospitalizationMapper, 'toEntity')
				.mockReturnValue({ id: 202 } as HospitalizationEntity);
			jest
				.spyOn(HospitalMapper, 'toHospitalEntity')
				.mockReturnValue({ id: 203 } as HospitalEntity);

			const resultEntity = GuideMapper.toEntity(dummyGuideDomain);

			const expectedEntity = new GuideEntity();
			expectedEntity.id = dummyGuideDomain.id as number;
			expectedEntity.patient = { id: 201 } as PatientEntity;
			expectedEntity.patientWay = { id: 202 } as HospitalizationEntity;
			expectedEntity.hospital = { id: 203 } as HospitalEntity;
			expectedEntity.created = dummyGuideDomain.created;
			expectedEntity.updated = dummyGuideDomain.updated;
			expectedEntity.numeroSenha = dummyGuideDomain.numeroSenha;
			expectedEntity.numeroGuia = dummyGuideDomain.numeroGuia;
			expectedEntity.dataEmissaoGuia = dummyGuideDomain.dataEmissaoGuia;
			expectedEntity.dataAutorizacaoGuia = dummyGuideDomain.dataAutorizacaoGuia;
			expectedEntity.speciality = dummyGuideDomain.speciality;
			expectedEntity.type = dummyGuideDomain.type;
			expectedEntity.obs = dummyGuideDomain.obs;
			expectedEntity.obsDaily = dummyGuideDomain.obsDaily;
			expectedEntity.obsProcedure = dummyGuideDomain.obsProcedure;
			expectedEntity.obsDrugs = dummyGuideDomain.obsDrugs;
			expectedEntity.obsMaterials = dummyGuideDomain.obsMaterials;
			expectedEntity.inicioPeriodo = dummyGuideDomain.inicioPeriodo;
			expectedEntity.finalPeriodo = dummyGuideDomain.finalPeriodo;
			expectedEntity.valorTotal = dummyGuideDomain.valorTotal;
			expectedEntity.enabled = dummyGuideDomain.enabled;
			expectedEntity.userId = dummyGuideDomain.userId;
			expectedEntity.auditorId = dummyGuideDomain.auditorId;
			expectedEntity.tissLink = dummyGuideDomain.tissLink;
			expectedEntity.isViewed = dummyGuideDomain.isViewed;
			expectedEntity.valueRequest = dummyGuideDomain.valueRequest;
			expectedEntity.valueSaved = dummyGuideDomain.valueSaved;
			expectedEntity.valueAuthorized = dummyGuideDomain.valueAuthorized;
			expectedEntity.isCenso = dummyGuideDomain.isCenso;
			expectedEntity.reanalysis = dummyGuideDomain.reanalysis;
			expectedEntity.codIntegration = dummyGuideDomain.codIntegration;
			expectedEntity.obsDailyProvider = dummyGuideDomain.obsDailyProvider;
			expectedEntity.obsProcedureProvider =
				dummyGuideDomain.obsProcedureProvider;
			expectedEntity.obsDrugsProvider = dummyGuideDomain.obsDrugsProvider;
			expectedEntity.obsMaterialsProvider =
				dummyGuideDomain.obsMaterialsProvider;
			expectedEntity.motivoNegativa = dummyGuideDomain.motivoNegativa;
			expectedEntity.dataExpiracaoEdicao = dummyGuideDomain.dataExpiracaoEdicao;

			expect(resultEntity).toEqual(expectedEntity);
		});

		it('deve mapear um objeto de domínio Guide para um GuideEntity com Patient, PatientWay e Hospital como null', () => {
			const dummyGuideDomain = new Guide(
				2,
				null,
				null,
				null,
				new Date('2023-02-01T00:00:00Z'),
				new Date('2023-02-02T00:00:00Z'),
				'senha456',
				'guia456',
				new Date('2023-02-03T00:00:00Z'),
				new Date('2023-02-04T00:00:00Z'),
				'Neurology',
				2,
				'Obs Domain',
				'Obs Daily Domain',
				'Obs Procedure Domain',
				'Obs Drugs Domain',
				'Obs Materials Domain',
				new Date('2023-02-05T00:00:00Z'),
				new Date('2023-02-06T00:00:00Z'),
				'200.00',
				1,
				401,
				501,
				'http://example.org',
				1,
				60,
				55,
				58,
				0,
				0,
				'CODE456',
				'Obs Daily Provider Domain',
				'Obs Procedure Provider Domain',
				'Obs Drugs Provider Domain',
				'Obs Materials Provider Domain',
				'Negative Reason Domain',
				new Date('2023-02-07T00:00:00Z'),
			);

			jest.spyOn(PatientMapper, 'toEntity').mockReturnValue(null);
			jest.spyOn(HospitalizationMapper, 'toEntity').mockReturnValue(null);
			jest.spyOn(HospitalMapper, 'toHospitalEntity').mockReturnValue(null);

			const resultEntity = GuideMapper.toEntity(dummyGuideDomain);

			const expectedEntity = new GuideEntity();
			expectedEntity.id = dummyGuideDomain.id as number;
			expectedEntity.patient = null;
			expectedEntity.patientWay = null;
			expectedEntity.hospital = null;
			expectedEntity.created = dummyGuideDomain.created;
			expectedEntity.updated = dummyGuideDomain.updated;
			expectedEntity.numeroSenha = dummyGuideDomain.numeroSenha;
			expectedEntity.numeroGuia = dummyGuideDomain.numeroGuia;
			expectedEntity.dataEmissaoGuia = dummyGuideDomain.dataEmissaoGuia;
			expectedEntity.dataAutorizacaoGuia = dummyGuideDomain.dataAutorizacaoGuia;
			expectedEntity.speciality = dummyGuideDomain.speciality;
			expectedEntity.type = dummyGuideDomain.type;
			expectedEntity.obs = dummyGuideDomain.obs;
			expectedEntity.obsDaily = dummyGuideDomain.obsDaily;
			expectedEntity.obsProcedure = dummyGuideDomain.obsProcedure;
			expectedEntity.obsDrugs = dummyGuideDomain.obsDrugs;
			expectedEntity.obsMaterials = dummyGuideDomain.obsMaterials;
			expectedEntity.inicioPeriodo = dummyGuideDomain.inicioPeriodo;
			expectedEntity.finalPeriodo = dummyGuideDomain.finalPeriodo;
			expectedEntity.valorTotal = dummyGuideDomain.valorTotal;
			expectedEntity.enabled = dummyGuideDomain.enabled;
			expectedEntity.userId = dummyGuideDomain.userId;
			expectedEntity.auditorId = dummyGuideDomain.auditorId;
			expectedEntity.tissLink = dummyGuideDomain.tissLink;
			expectedEntity.isViewed = dummyGuideDomain.isViewed;
			expectedEntity.valueRequest = dummyGuideDomain.valueRequest;
			expectedEntity.valueSaved = dummyGuideDomain.valueSaved;
			expectedEntity.valueAuthorized = dummyGuideDomain.valueAuthorized;
			expectedEntity.isCenso = dummyGuideDomain.isCenso;
			expectedEntity.reanalysis = dummyGuideDomain.reanalysis;
			expectedEntity.codIntegration = dummyGuideDomain.codIntegration;
			expectedEntity.obsDailyProvider = dummyGuideDomain.obsDailyProvider;
			expectedEntity.obsProcedureProvider =
				dummyGuideDomain.obsProcedureProvider;
			expectedEntity.obsDrugsProvider = dummyGuideDomain.obsDrugsProvider;
			expectedEntity.obsMaterialsProvider =
				dummyGuideDomain.obsMaterialsProvider;
			expectedEntity.motivoNegativa = dummyGuideDomain.motivoNegativa;
			expectedEntity.dataExpiracaoEdicao = dummyGuideDomain.dataExpiracaoEdicao;

			expect(resultEntity).toEqual(expectedEntity);
		});
	});
});
