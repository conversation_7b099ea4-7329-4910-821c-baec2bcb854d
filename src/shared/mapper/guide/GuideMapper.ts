import { Guide } from 'src/core/domain/Guide';
import { GuideEntity } from 'src/gateway/entities/guide.entity';
import { PatientMapper } from '../hospitalization/PatientMapper';
import { HospitalizationMapper } from '../hospitalization/HospitalizationMapper';
import { HospitalMapper } from '../hospital/HospitalMapper';

export class GuideMapper {
	public static toDomain(guideEntity: GuideEntity): Guide {
		return new Guide(
			guideEntity.id,
			guideEntity.patient
				? PatientMapper.toPatientDomain(guideEntity.patient)
				: null,
			guideEntity.patientWay
				? HospitalizationMapper.toHospitalizationDomain(guideEntity.patientWay)
				: null,
			guideEntity.hospital
				? HospitalMapper.toHospitalDomain(guideEntity.hospital)
				: null,
			guideEntity.created,
			guideEntity.updated,
			guideEntity.numeroSenha,
			guideEntity.numeroGuia,
			guideEntity.dataEmissaoGuia,
			guideEntity.dataAutorizacaoGuia,
			guideEntity.speciality,
			guideEntity.type,
			guideEntity.obs,
			guideEntity.obsDaily,
			guideEntity.obsProcedure,
			guideEntity.obsDrugs,
			guideEntity.obsMaterials,
			guideEntity.inicioPeriodo,
			guideEntity.finalPeriodo,
			guideEntity.valorTotal,
			guideEntity.enabled,
			guideEntity.userId,
			guideEntity.auditorId,
			guideEntity.tissLink,
			guideEntity.isViewed,
			guideEntity.valueRequest,
			guideEntity.valueSaved,
			guideEntity.valueAuthorized,
			guideEntity.isCenso,
			guideEntity.reanalysis,
			guideEntity.codIntegration,
			guideEntity.obsDailyProvider,
			guideEntity.obsProcedureProvider,
			guideEntity.obsDrugsProvider,
			guideEntity.obsMaterialsProvider,
			guideEntity.motivoNegativa,
			guideEntity.dataExpiracaoEdicao,
		);
	}

	public static toEntity(guide: Guide | Partial<Guide>): GuideEntity {
		const guideEntity = new GuideEntity();
		guideEntity.id = guide.id as number;
		guideEntity.patient = guide.patient
			? PatientMapper.toEntity(guide.patient)
			: null;
		guideEntity.patientWay = guide.patientWay
			? HospitalizationMapper.toEntity(guide.patientWay)
			: null;
		guideEntity.hospital = guide.hospital
			? HospitalMapper.toHospitalEntity(guide.hospital)
			: null;
		guideEntity.created = guide.created;
		guideEntity.updated = guide.updated;
		guideEntity.numeroSenha = guide.numeroSenha;
		guideEntity.numeroGuia = guide.numeroGuia;
		guideEntity.dataEmissaoGuia = guide.dataEmissaoGuia;
		guideEntity.dataAutorizacaoGuia = guide.dataAutorizacaoGuia;
		guideEntity.speciality = guide.speciality;
		guideEntity.type = guide.type;
		guideEntity.obs = guide.obs;
		guideEntity.obsDaily = guide.obsDaily;
		guideEntity.obsProcedure = guide.obsProcedure;
		guideEntity.obsDrugs = guide.obsDrugs;
		guideEntity.obsMaterials = guide.obsMaterials;
		guideEntity.inicioPeriodo = guide.inicioPeriodo;
		guideEntity.finalPeriodo = guide.finalPeriodo;
		guideEntity.valorTotal = guide.valorTotal;
		guideEntity.enabled = guide.enabled;
		guideEntity.userId = guide.userId;
		guideEntity.auditorId = guide.auditorId;
		guideEntity.tissLink = guide.tissLink;
		guideEntity.isViewed = guide.isViewed;
		guideEntity.valueRequest = guide.valueRequest;
		guideEntity.valueSaved = guide.valueSaved;
		guideEntity.valueAuthorized = guide.valueAuthorized;
		guideEntity.isCenso = guide.isCenso;
		guideEntity.reanalysis = guide.reanalysis;
		guideEntity.codIntegration = guide.codIntegration;
		guideEntity.obsDailyProvider = guide.obsDailyProvider;
		guideEntity.obsProcedureProvider = guide.obsProcedureProvider;
		guideEntity.obsDrugsProvider = guide.obsDrugsProvider;
		guideEntity.obsMaterialsProvider = guide.obsMaterialsProvider;
		guideEntity.motivoNegativa = guide.motivoNegativa;
		guideEntity.dataExpiracaoEdicao = guide.dataExpiracaoEdicao;
		return guideEntity;
	}
}
