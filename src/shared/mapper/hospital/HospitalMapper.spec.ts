import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';
import { Hospital } from 'src/core/domain/Hospital';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';

describe('HospitalMapper', () => {
	describe('toHospitalCompanyDomain', () => {
		it('deve mapear um HospitalsCompanyEntity para um HospitalsCompany corretamente', () => {
			const dummyEntity: HospitalsCompanyEntity = {
				id: 1,
				valorMedioDia: 123,
				email: '<EMAIL>',
				enabled: 1, // 1 significa true
				tipo: 2,
				valorAlertaMes: 456,
				enfClinica: 10,
				enfCirurgica: 20,
				enfObstetrica: 30,
				enfPediatrica: 40,
				enfPsiquiatrica: 50,
				utiaClinica: 60,
				utiaCirurgica: 70,
				utiaObstetrica: 80,
				utiaPediatrica: 90,
				utiaPsiquiatrica: 100,
				utipClinica: 110,
				utipCirurgica: 120,
				utipObstetrica: 130,
				utipPediatrica: 140,
				utipPsiquiatrica: 150,
				utinClinica: 160,
				utinCirurgica: 170,
				utinObstetrica: 180,
				utinPediatrica: 190,
				utinPsiquiatrica: 200,
				tsiClinica: 210,
				tsiCirurgica: 220,
				tsiObstetrica: 230,
				tsiPediatrica: 240,
				tsiPsiquiatrica: 250,
				ucoClinica: 260,
				ucoCirurgica: 270,
				ucoObstetrica: 280,
				ucoPediatrica: 290,
				ucoPsiquiatrica: 300,
				uceClinica: 310,
				uceCirurgica: 320,
				uceObstetrica: 330,
				ucePediatrica: 340,
				ucePsiquiatrica: 350,
				bernClinica: 360,
				bernCirurgica: 370,
				bernObstetrica: 380,
				bernPediatrica: 390,
				bernPsiquiatrica: 400,
				berppClinica: 410,
				berppCirurgica: 420,
				berppObstetrica: 430,
				berppPediatrica: 440,
				berppPsiquiatrica: 450,
				apartClinica: 460,
				apartCirurgica: 470,
				apartObstetrica: 480,
				apartPediatrica: 490,
				domiTransicao: 1,
				domiMulti: 2,
				domiMedicamento: 3,
				domiCurativo: 4,
				domi6h: 5,
				domi12h: 6,
				domi24AV: 7,
				domi24VM: 8,
				onedayPsiquiatrica: 9,
				onedayPediatrica: 10,
				onedayObstetrica: 11,
				onedayCirurgica: 12,
				onedayClinica: 13,
				apartPsiquiatrica: 14,
				permanenciaEnf: 15,
				permanenciaUtia: 16,
				utiPsiquiatrica: 17,
				utiPediatrica: 18,
				utiObstetrica: 19,
				utiCirurgica: 20,
				utiClinica: 21,
				utiiPsiquiatrica: 22,
				utiiPediatrica: 23,
				utiiObstetrica: 24,
				utiiCirurgica: 25,
				utiiClinica: 26,
				utiniPsiquiatrica: 27,
				utiniPediatrica: 28,
				utiniObstetrica: 29,
				utiniCirurgica: 30,
				utiniClinica: 31,
				ucoiPsiquiatrica: 32,
				ucoiPediatrica: 33,
				ucoiObstetrica: 34,
				ucoiCirurgica: 35,
				ucoiClinica: 36,
				semiutiiPsiquiatrica: 37,
				semiutiiPediatrica: 38,
				semiutiiObstetrica: 39,
				ssemiutiiCirurgica: 40,
				semiutiiClinica: 41,
				semiutiPsiquiatrica: 42,
				semiutiPediatrica: 43,
				semiutiObstetrica: 44,
				semiutiCirurgica: 45,
				semiutiClinica: 46,
				utmoPsiquiatrica: 47,
				utmoPediatrica: 48,
				utmoObstetrica: 49,
				utmoCirurgica: 50,
				utmoClinica: 51,
				ucgPsiquiatrica: 52,
				ucgPediatrica: 53,
				ucgObstetrica: 54,
				ucgCirurgica: 55,
				ucgClinica: 56,
				ucgiPsiquiatrica: 57,
				ucgiPediatrica: 58,
				ucgiObstetrica: 59,
				ucgiCirurgica: 60,
				ucgiClinica: 61,
				apartiPsiquiatrica: 62,
				apartiPediatrica: 63,
				apartiObstetrica: 64,
				apartiCirurgica: 65,
				apartiClinica: 66,
				complexity: 67,
				homecareEquipamento: 68,
				homecareMaterial: 69,
				assistenciaDomiciliar: 70,
				internacaoDomiciliar: 71,
				homecareFisioterapia: 72,
				homecareInternado24: 73,
				homecareAntibiotic: 74,
				companyId: 75,
				hospital: { id: 999 } as HospitalEntity,
				created: new Date('2023-03-01T00:00:00Z'),
				updated: new Date('2023-03-02T00:00:00Z'),
				name: 'Hospital A',
				name2: 'Hospital A2',
				name3: 'Hospital A3',
				tel: '1111-1111',
				tel2: '2222-2222',
				dscTpLogra: 'Street',
				endereco: 'Address',
				numero: '123',
				bairro: 'Neighborhood',
				estado: 'State',
				cidade: 'City',
				regiao: 'Region',
				regiaoNova: 'New Region',
				controle: 'Control',
				controle2: 'Control2',
				cityId: 80,
				stateId: 81,
				codigoInterno: 'INT-123',
				handlePrestador: 'Handle',
				codDrg: 'DRG-123',
			} as unknown as HospitalsCompanyEntity;

			const dummyHospitalDomain: Hospital = new Hospital(
				555,
				'UnitDomain',
				'CNESDomain',
				new Date('2023-02-01T00:00:00Z'),
				new Date('2023-02-02T00:00:00Z'),
				'CNPJDomain',
				'RazaoDomain',
				'FantasiaDomain',
				'LogradouroDomain',
				'200',
				'ComplementoDomain',
				'BairroDomain',
				'CepDomain',
				'TelefoneDomain',
				'FaxDomain',
				'<EMAIL>',
				'CpfDomain',
				'CnpjDomain',
				'AtividadeDomain',
				'ClientelaDomain',
				1,
				'TurnoDomain',
				2,
				1,
				'2023-02-03T00:00:00Z',
				'http://domain.com',
				'LatDomain',
				'LonDomain',
				'AbertoDomain',
				'InternetDomain',
				'GestaoDomain',
				'LogradouroDomain',
				false,
			);
			jest
				.spyOn(HospitalMapper, 'toHospitalDomain')
				.mockReturnValue(dummyHospitalDomain);

			const result: HospitalsCompany =
				HospitalMapper.toHospitalCompanyDomain(dummyEntity);

			expect(result.id).toBe(dummyEntity.id);
			expect(result.valorMedioDia).toBe(dummyEntity.valorMedioDia);
			expect(result.email).toBe(dummyEntity.email);
			expect(result.enabled).toBe(true); // enabled === 1 → true
			expect(result.tipo).toBe(dummyEntity.tipo);
			expect(result.valorAlertaMes).toBe(dummyEntity.valorAlertaMes);
			expect(result.enfClinica).toBe(dummyEntity.enfClinica);
			expect(result.hospital).toEqual(dummyHospitalDomain);
		});
	});

	describe('toHospitalDomain', () => {
		it('deve mapear um HospitalEntity para um objeto de domínio Hospital corretamente', () => {
			const dummyHospitalEntity: HospitalEntity = {
				id: 555,
				coUnidade: 'UnitDomain',
				coCnes: 'CNESDomain',
				created: new Date('2023-02-01T00:00:00Z'),
				updated: new Date('2023-02-02T00:00:00Z'),
				nuCnpjMantenedora: 'CNPJDomain',
				noRazaoSocial: 'RazaoDomain',
				noFantasia: 'FantasiaDomain',
				noLogradouro: 'LogradouroDomain',
				nuEndereco: '200',
				noComplemento: 'ComplementoDomain',
				noBairro: 'BairroDomain',
				coCep: 'CepDomain',
				nuTelefone: 'TelefoneDomain',
				nuFax: 'FaxDomain',
				noEmail: '<EMAIL>',
				nuCpf: 'CpfDomain',
				nuCnpj: 'CnpjDomain',
				coAtividade: 'AtividadeDomain',
				coClientela: 'ClientelaDomain',
				tpUnidade: 1,
				coTurnoAtendimento: 'TurnoDomain',
				coEstadoGestor: 2,
				coMunicipioGestor: 1,
				dtAtualizacao: '2023-02-03T00:00:00Z',
				noUrl: 'http://domain.com',
				nuLatitude: 'LatDomain',
				nuLongitude: 'LonDomain',
				tpEstabSempreAberto: 'AbertoDomain',
				stConexaoInternet: 'InternetDomain',
				tpGestao: 'GestaoDomain',
				tpLogradouroCarefy: 'LogradouroDomain',
				isUnimed: false,
			};

			const result: Hospital =
				HospitalMapper.toHospitalDomain(dummyHospitalEntity);

			expect(result.id).toBe(dummyHospitalEntity.id);
			expect(result.coUnidade).toBe(dummyHospitalEntity.coUnidade);
		});
	});

	describe('toHospitalEntity', () => {
		it('deve mapear um objeto de domínio Hospital para HospitalEntity corretamente', () => {
			const dummyHospitalDomain: Hospital = new Hospital(
				555,
				'UnitDomain',
				'CNESDomain',
				new Date('2023-02-01T00:00:00Z'),
				new Date('2023-02-02T00:00:00Z'),
				'CNPJDomain',
				'RazaoDomain',
				'FantasiaDomain',
				'LogradouroDomain',
				'200',
				'ComplementoDomain',
				'BairroDomain',
				'CepDomain',
				'TelefoneDomain',
				'FaxDomain',
				'<EMAIL>',
				'CpfDomain',
				'CnpjDomain',
				'AtividadeDomain',
				'ClientelaDomain',
				1,
				'TurnoDomain',
				2,
				1,
				'2023-02-03T00:00:00Z',
				'http://domain.com',
				'LatDomain',
				'LonDomain',
				'AbertoDomain',
				'InternetDomain',
				'GestaoDomain',
				'LogradouroDomain',
				false,
			);

			const result: HospitalEntity =
				HospitalMapper.toHospitalEntity(dummyHospitalDomain);

			expect(result.id).toBe(dummyHospitalDomain.id);
			expect(result.coUnidade).toBe(dummyHospitalDomain.coUnidade);
			expect(result.coCnes).toBe(dummyHospitalDomain.coCnes);
			expect(result.noRazaoSocial).toBe(dummyHospitalDomain.noRazaoSocial);
			expect(result.noFantasia).toBe(dummyHospitalDomain.noFantasia);
			expect(result.isUnimed).toBe(dummyHospitalDomain.isUnimed);
		});
	});
});
