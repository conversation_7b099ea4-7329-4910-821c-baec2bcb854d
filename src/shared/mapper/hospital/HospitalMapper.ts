import { Hospital } from 'src/core/domain/Hospital';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';

export class HospitalMapper {
	public static toHospitalCompanyDomain(
		hospitalEntity: HospitalsCompanyEntity,
	): HospitalsCompany {
		return new HospitalsCompany(
			hospitalEntity.id,
			hospitalEntity.valorMedioDia,
			hospitalEntity.email,
			hospitalEntity.enabled === 1,
			hospitalEntity.tipo,
			hospitalEntity.valorAlertaMes,
			hospitalEntity.enfClinica,
			hospitalEntity.enfCirurgica,
			hospitalEntity.enfObstetrica,
			hospitalEntity.enfPediatrica,
			hospitalEntity.enfPsiquiatrica,
			hospitalEntity.utiaClinica,
			hospitalEntity.utiaCirurgica,
			hospitalEntity.utiaObstetrica,
			hospitalEntity.utiaPediatrica,
			hospitalEntity.utiaPsiquiatrica,
			hospitalEntity.utipClinica,
			hospitalEntity.utipCirurgica,
			hospitalEntity.utipObstetrica,
			hospitalEntity.utipPediatrica,
			hospitalEntity.utipPsiquiatrica,
			hospitalEntity.utinClinica,
			hospitalEntity.utinCirurgica,
			hospitalEntity.utinObstetrica,
			hospitalEntity.utinPediatrica,
			hospitalEntity.utinPsiquiatrica,
			hospitalEntity.tsiClinica,
			hospitalEntity.tsiCirurgica,
			hospitalEntity.tsiObstetrica,
			hospitalEntity.tsiPediatrica,
			hospitalEntity.tsiPsiquiatrica,
			hospitalEntity.ucoClinica,
			hospitalEntity.ucoCirurgica,
			hospitalEntity.ucoObstetrica,
			hospitalEntity.ucoPediatrica,
			hospitalEntity.ucoPsiquiatrica,
			hospitalEntity.uceClinica,
			hospitalEntity.uceCirurgica,
			hospitalEntity.uceObstetrica,
			hospitalEntity.ucePediatrica,
			hospitalEntity.ucePsiquiatrica,
			hospitalEntity.bernClinica,
			hospitalEntity.bernCirurgica,
			hospitalEntity.bernObstetrica,
			hospitalEntity.bernPediatrica,
			hospitalEntity.bernPsiquiatrica,
			hospitalEntity.berppClinica,
			hospitalEntity.berppCirurgica,
			hospitalEntity.berppObstetrica,
			hospitalEntity.berppPediatrica,
			hospitalEntity.berppPsiquiatrica,
			hospitalEntity.apartClinica,
			hospitalEntity.apartCirurgica,
			hospitalEntity.apartObstetrica,
			hospitalEntity.apartPediatrica,
			hospitalEntity.domiTransicao,
			hospitalEntity.domiMulti,
			hospitalEntity.domiMedicamento,
			hospitalEntity.domiCurativo,
			hospitalEntity.domi6h,
			hospitalEntity.domi12h,
			hospitalEntity.domi24AV,
			hospitalEntity.domi24VM,
			hospitalEntity.onedayPsiquiatrica,
			hospitalEntity.onedayPediatrica,
			hospitalEntity.onedayObstetrica,
			hospitalEntity.onedayCirurgica,
			hospitalEntity.onedayClinica,
			hospitalEntity.apartPsiquiatrica,
			hospitalEntity.permanenciaEnf,
			hospitalEntity.permanenciaUtia,
			hospitalEntity.utiPsiquiatrica,
			hospitalEntity.utiPediatrica,
			hospitalEntity.utiObstetrica,
			hospitalEntity.utiCirurgica,
			hospitalEntity.utiClinica,
			hospitalEntity.utiiPsiquiatrica,
			hospitalEntity.utiiPediatrica,
			hospitalEntity.utiiObstetrica,
			hospitalEntity.utiiCirurgica,
			hospitalEntity.utiiClinica,
			hospitalEntity.utiniPsiquiatrica,
			hospitalEntity.utiniPediatrica,
			hospitalEntity.utiniObstetrica,
			hospitalEntity.utiniCirurgica,
			hospitalEntity.utiniClinica,
			hospitalEntity.ucoiPsiquiatrica,
			hospitalEntity.ucoiPediatrica,
			hospitalEntity.ucoiObstetrica,
			hospitalEntity.ucoiCirurgica,
			hospitalEntity.ucoiClinica,
			hospitalEntity.semiutiiPsiquiatrica,
			hospitalEntity.semiutiiPediatrica,
			hospitalEntity.semiutiiObstetrica,
			hospitalEntity.ssemiutiiCirurgica,
			hospitalEntity.semiutiiClinica,
			hospitalEntity.semiutiPsiquiatrica,
			hospitalEntity.semiutiPediatrica,
			hospitalEntity.semiutiObstetrica,
			hospitalEntity.semiutiCirurgica,
			hospitalEntity.semiutiClinica,
			hospitalEntity.utmoPsiquiatrica,
			hospitalEntity.utmoPediatrica,
			hospitalEntity.utmoObstetrica,
			hospitalEntity.utmoCirurgica,
			hospitalEntity.utmoClinica,
			hospitalEntity.ucgPsiquiatrica,
			hospitalEntity.ucgPediatrica,
			hospitalEntity.ucgObstetrica,
			hospitalEntity.ucgCirurgica,
			hospitalEntity.ucgClinica,
			hospitalEntity.ucgiPsiquiatrica,
			hospitalEntity.ucgiPediatrica,
			hospitalEntity.ucgiObstetrica,
			hospitalEntity.ucgiCirurgica,
			hospitalEntity.ucgiClinica,
			hospitalEntity.apartiPsiquiatrica,
			hospitalEntity.apartiPediatrica,
			hospitalEntity.apartiObstetrica,
			hospitalEntity.apartiCirurgica,
			hospitalEntity.apartiClinica,
			hospitalEntity.complexity,
			hospitalEntity.homecareEquipamento,
			hospitalEntity.homecareMaterial,
			hospitalEntity.assistenciaDomiciliar,
			hospitalEntity.internacaoDomiciliar,
			hospitalEntity.homecareFisioterapia,
			hospitalEntity.homecareInternado24,
			hospitalEntity.homecareAntibiotic,
			hospitalEntity.companyId,
			hospitalEntity.hospital
				? HospitalMapper.toHospitalDomain(hospitalEntity.hospital)
				: undefined,
			hospitalEntity.created,
			hospitalEntity.updated,
			hospitalEntity.name,
			hospitalEntity.name2,
			hospitalEntity.name3,
			hospitalEntity.tel,
			hospitalEntity.tel2,
			hospitalEntity.dscTpLogra,
			hospitalEntity.endereco,
			hospitalEntity.numero,
			hospitalEntity.bairro,
			hospitalEntity.estado,
			hospitalEntity.cidade,
			hospitalEntity.regiao,
			hospitalEntity.regiaoNova,
			hospitalEntity.controle,
			hospitalEntity.controle2,
			hospitalEntity.cityId,
			hospitalEntity.stateId,
			hospitalEntity.codigoInterno,
			hospitalEntity.handlePrestador,
			hospitalEntity.codDrg,
		);
	}
	/* istanbul ignore next */
	public static toHospitalDomain(hospitalEntity: HospitalEntity): Hospital {
		return new Hospital(
			hospitalEntity.id,
			hospitalEntity.coUnidade,
			hospitalEntity.coCnes,
			hospitalEntity.created,
			hospitalEntity.updated,
			hospitalEntity.nuCnpjMantenedora,
			hospitalEntity.noRazaoSocial,
			hospitalEntity.noFantasia,
			hospitalEntity.noLogradouro,
			hospitalEntity.nuEndereco,
			hospitalEntity.noComplemento,
			hospitalEntity.noBairro,
			hospitalEntity.coCep,
			hospitalEntity.nuTelefone,
			hospitalEntity.nuFax,
			hospitalEntity.noEmail,
			hospitalEntity.nuCpf,
			hospitalEntity.nuCnpj,
			hospitalEntity.coAtividade,
			hospitalEntity.coClientela,
			hospitalEntity.tpUnidade,
			hospitalEntity.coTurnoAtendimento,
			hospitalEntity.coEstadoGestor,
			hospitalEntity.coMunicipioGestor,
			hospitalEntity.dtAtualizacao,
			hospitalEntity.noUrl,
			hospitalEntity.nuLatitude,
			hospitalEntity.nuLongitude,
			hospitalEntity.tpEstabSempreAberto,
			hospitalEntity.stConexaoInternet,
			hospitalEntity.tpGestao,
			hospitalEntity.tpLogradouroCarefy,
			hospitalEntity.isUnimed,
		);
	}

	public static toHospitalEntity(
		hospital: Hospital | Partial<Hospital>,
	): HospitalEntity {
		const entity = new HospitalEntity();
		entity.id = hospital.id;
		entity.coUnidade = hospital.coUnidade;
		entity.coCnes = hospital.coCnes;
		entity.created = hospital.created;
		entity.updated = hospital.updated;
		entity.nuCnpjMantenedora = hospital.nuCnpjMantenedora;
		entity.noRazaoSocial = hospital.noRazaoSocial;
		entity.noFantasia = hospital.noFantasia;
		entity.noLogradouro = hospital.noLogradouro;
		entity.nuEndereco = hospital.nuEndereco;
		entity.noComplemento = hospital.noComplemento;
		entity.noBairro = hospital.noBairro;
		entity.coCep = hospital.coCep;
		entity.nuTelefone = hospital.nuTelefone;
		entity.nuFax = hospital.nuFax;
		entity.noEmail = hospital.noEmail;
		entity.nuCpf = hospital.nuCpf;
		entity.nuCnpj = hospital.nuCnpj;
		entity.coAtividade = hospital.coAtividade;
		entity.coClientela = hospital.coClientela;
		entity.tpUnidade = hospital.tpUnidade;
		entity.coTurnoAtendimento = hospital.coTurnoAtendimento;
		entity.coEstadoGestor = hospital.coEstadoGestor;
		entity.coMunicipioGestor = hospital.coMunicipioGestor;
		entity.dtAtualizacao = hospital.dtAtualizacao;
		entity.noUrl = hospital.noUrl;
		entity.nuLatitude = hospital.nuLatitude;
		entity.nuLongitude = hospital.nuLongitude;
		entity.tpEstabSempreAberto = hospital.tpEstabSempreAberto;
		entity.stConexaoInternet = hospital.stConexaoInternet;
		entity.tpGestao = hospital.tpGestao;
		entity.tpLogradouroCarefy = hospital.tpLogradouroCarefy;
		entity.isUnimed = hospital.isUnimed;
		return entity;
	}
}
