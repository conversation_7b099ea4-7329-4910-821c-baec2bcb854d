import { EmpresaMapper } from 'src/shared/mapper/empresa/empresa.mapper';
import { Empresa } from 'src/core/domain/Empresa';
import { EmpresaEntity } from 'src/gateway/entities/empresa.entity';

describe('EmpresaMapper', () => {
	describe('toEmpresaDomain', () => {
		it('deve mapear um EmpresaEntity para um Empresa corretamente', () => {
			// Arrange
			const dummyEmpresaEntity: EmpresaEntity = {
				id: 1,
				name: 'Empresa Teste',
				codInterno: 'EMP-001',
				cnpj: '12345678901234',
				created: new Date('2023-01-01T00:00:00Z'),
				updated: new Date('2023-01-02T00:00:00Z'),
				companyId: 100,
				responsaveisAtendimento: 'Responsável Teste',
				codGrupEmp: 'GRP-001',
				tipoEmpresa: 'Tipo Teste',
				descAtivd: 'Descrição Teste',
				grupoAjustado: 'Grupo Teste',
				porteDoGrupo: 'Grande',
				status: 'Ativo',
				enabled: true,
				lifesNumber: 1000,
			} as EmpresaEntity;

			// Act
			const result: Empresa = EmpresaMapper.toEmpresaDomain(dummyEmpresaEntity);

			// Assert
			expect(result).toBeInstanceOf(Empresa);
			expect(result.id).toBe(dummyEmpresaEntity.id);
			expect(result.nome).toBe(dummyEmpresaEntity.name);
			expect(result.codInterno).toBe(dummyEmpresaEntity.codInterno);
			expect(result.cnpj).toBe(dummyEmpresaEntity.cnpj);
		});
	});
});
