import { Censo } from 'src/core/domain/Censo';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';
import { CensoEntity } from 'src/gateway/entities/censo.entity';

export class CensoMapper {
	static toCensoDomain(censo: CensoEntity): Censo {
		return new Censo(
			censo.id,
			censo.dataCriacao,
			censo.companyId,
			censo.totalLinhas,
			censo.userId,
			censo.status,
			{ name: censo.operadora.name },
			{ name: censo.user.name },
			censo.diretorioSalvo,
			censo.nomeArquivo,
		);
	}

	static toCensoListagemDomain(listagem: CensoListagem): CensoListagem {
		return new CensoListagem(
			listagem.pagina,
			listagem.quantidadeTotal,
			listagem.censos,
		);
	}

	static toProcessaCenso(censo: Censo): ProcessaCenso {
		return new ProcessaCenso(
			Number(censo.id),
			censo.diretorioSalvo,
			Number(censo.companyId),
			Number(censo.userId),
			null,
			null,
			null,
			censo.totalLinhas,
			null,
		);
	}
}
