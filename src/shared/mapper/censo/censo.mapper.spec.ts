import { Censo } from 'src/core/domain/Censo';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { CensoMapper } from './censo.mapper';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';
import { CensoEntity } from 'src/gateway/entities/censo.entity';

describe('CensoMapper', () => {
	let censoMock: CensoEntity;
	beforeEach(() => {
		censoMock = new Censo(
			'1',
			new Date(),
			'1',
			100,
			'1',
			{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
			{ name: 'Operadora A' },
			{ name: 'User A' },
			'path/to/file',
			'bananinha nanica',
		) as CensoEntity;
	});

	describe('toCensoDomain', () => {
		it('deve mapear corretamente um Censo para o domínio Censo', () => {
			const censoMapped = CensoMapper.toCensoDomain(censoMock);

			expect(censoMapped).toBeInstanceOf(Censo);
			expect(censoMapped).toStrictEqual(censoMock);
		});
	});

	describe('toCensoListagemDomain', () => {
		it('deve mapear corretamente um CensoListagem para o domínio CensoListagem', () => {
			const censosMock = [censoMock];
			const listagemMock = new CensoListagem(1, 10, censosMock);

			const listagemMapped = CensoMapper.toCensoListagemDomain(listagemMock);

			expect(listagemMapped).toBeInstanceOf(CensoListagem);
			expect(listagemMapped.pagina).toBe(listagemMock.pagina);
			expect(listagemMapped.quantidadeTotal).toBe(listagemMock.quantidadeTotal);
			expect(listagemMapped.censos).toEqual(listagemMock.censos);
		});
	});

	describe('toProcessaCenso', () => {
		it('deve mapear corretamente um ProcessaCensoDto para o domínio ProcessaCenso', () => {
			const processaCensoMock = new ProcessaCenso(
				1,
				'path/to/file',
				1,
				1,
				null,
				null,
				null,
				100,
				null,
			);

			const processaCensoMapped = CensoMapper.toProcessaCenso(censoMock);

			expect(processaCensoMapped).toEqual(processaCensoMock);
		});
	});
});
