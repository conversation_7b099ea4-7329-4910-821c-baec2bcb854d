import { Hospitalization } from 'src/core/domain/Hospitalization';

export class HospitalizationValidation {
	static validateConflict(
		hospitalization: Hospitalization,
		newAdmissionDate: Date,
		newDischargeDate: Date | null,
		newAccommodation?: string,
	): boolean {
		const isSameDate =
			hospitalization.admissionIn.getTime() === newAdmissionDate.getTime();

		if (isSameDate && newDischargeDate !== null) {
			return true;
		}

		const bedHospital = hospitalization.bedHospitals?.find(
			(bed) => bed.admissionIn.getTime() === newAdmissionDate.getTime(),
		);

		const accommodation = bedHospital?.accommodation;

		if (
			isSameDate &&
			accommodation?.toLowerCase() !== newAccommodation?.toLowerCase()
		) {
			return true;
		}

		return false;
	}
}
