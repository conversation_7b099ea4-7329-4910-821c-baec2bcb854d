import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { FindPermissionsGatewayImpl } from './findPermissions.gateway.impl';
import { PermissionsEntity } from '../entities/permissions.entity';

describe('FindPermissionsGatewayImpl', () => {
	let findPermissionsGatewayImpl: FindPermissionsGatewayImpl;
	let permissionsRepository: Repository<PermissionsEntity>;

	const mockPermissionsEntity1 = {
		id: 1,
		userId: 100,
		permissionFunction: { id: 1, function: 'function1', enabled: true },
		view: true,
		insert: true,
		edit: true,
		delete: false,
		enabled: true,
	} as unknown as PermissionsEntity;

	const mockPermissionsEntity2 = {
		id: 2,
		userId: 100,
		permissionFunction: { id: 2, function: 'function2', enabled: true },
		view: true,
		insert: false,
		edit: true,
		delete: false,
		enabled: true,
	} as unknown as PermissionsEntity;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'PERMISSIONS_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: 'FindPermissionsGateway',
					useFactory: (permissionsRepository: Repository<PermissionsEntity>) =>
						new FindPermissionsGatewayImpl(permissionsRepository),
					inject: ['PERMISSIONS_REPOSITORY'],
				},
			],
		}).compile();

		findPermissionsGatewayImpl = module.get<FindPermissionsGatewayImpl>(
			'FindPermissionsGateway',
		);
		permissionsRepository = module.get<Repository<PermissionsEntity>>(
			'PERMISSIONS_REPOSITORY',
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('findPermissionsByUserId', () => {
		it('deve encontrar permissões pelo userId', async () => {
			const userId = 100;
			const mockEntities = [mockPermissionsEntity1, mockPermissionsEntity2];

			jest.spyOn(permissionsRepository, 'find').mockResolvedValue(mockEntities);

			const result =
				await findPermissionsGatewayImpl.findPermissionsByUserId(userId);

			expect(permissionsRepository.find).toHaveBeenCalledWith({
				where: {
					userId,
					enabled: true,
				},
			});

			expect(result).toBeDefined();
			expect(result['function1']).toBeDefined();
			expect(result['function2']).toBeDefined();
			expect(result['function1']).toEqual({
				view: true,
				insert: true,
				edit: true,
				delete: false,
			});
			expect(result['function2']).toEqual({
				view: true,
				insert: false,
				edit: true,
				delete: false,
			});
		});

		it('deve retornar um objeto vazio quando não encontrar permissões', async () => {
			const userId = 999;

			jest.spyOn(permissionsRepository, 'find').mockResolvedValue([]);

			const result =
				await findPermissionsGatewayImpl.findPermissionsByUserId(userId);

			expect(permissionsRepository.find).toHaveBeenCalledWith({
				where: {
					userId,
					enabled: true,
				},
			});

			expect(result).toBeDefined();
			expect(Object.keys(result).length).toBe(0);
		});

		it('deve lidar com erros do banco de dados', async () => {
			const userId = 100;
			const errorMessage = 'Database error';

			jest
				.spyOn(permissionsRepository, 'find')
				.mockRejectedValue(new Error(errorMessage));

			await expect(
				findPermissionsGatewayImpl.findPermissionsByUserId(userId),
			).rejects.toThrow(errorMessage);

			expect(permissionsRepository.find).toHaveBeenCalledWith({
				where: {
					userId,
					enabled: true,
				},
			});
		});

		it('deve retornar um objeto vazio quando userId for null', async () => {
			const userId: number = null;

			jest.spyOn(permissionsRepository, 'find').mockResolvedValue([]);

			const result =
				await findPermissionsGatewayImpl.findPermissionsByUserId(userId);

			expect(permissionsRepository.find).toHaveBeenCalledWith({
				where: {
					userId,
					enabled: true,
				},
			});

			expect(result).toBeDefined();
			expect(Object.keys(result).length).toBe(0);
		});
	});
});
