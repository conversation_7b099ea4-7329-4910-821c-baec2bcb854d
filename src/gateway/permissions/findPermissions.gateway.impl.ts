import { FindPermissionsGateway } from 'src/core/application/gateway/permissions/findPermissions.gateway';
import { Repository } from 'typeorm';
import { PermissionsEntity } from '../entities/permissions.entity';
import { Permissions } from 'src/core/domain/Permissions';

export class FindPermissionsGatewayImpl implements FindPermissionsGateway {
	constructor(
		private readonly permissionsRepository: Repository<PermissionsEntity>,
	) {}

	async findPermissionsByUserId(userId: number): Promise<Permissions> {
		const permissions = await this.permissionsRepository.find({
			where: {
				userId,
				enabled: true,
			},
		});

		return permissions.reduce((acc, permission) => {
			const permissionFunction = permission.permissionFunction.function;

			const permissionData = {
				view: permission.view,
				insert: permission.insert,
				edit: permission.edit,
				delete: permission.delete,
			};

			acc[permissionFunction] = permissionData;
			return acc;
		}, {} as Permissions);
	}
}
