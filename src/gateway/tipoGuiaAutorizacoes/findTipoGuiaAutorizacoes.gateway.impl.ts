import { ILike, Repository } from 'typeorm';
import { FindTipoGuiaAutorizacoesGateway } from 'src/core/application/gateway/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoesEntity } from '../entities/tipoGuiaAutorizacoes.entity';
import { TipoGuiaMapper } from 'src/shared/mapper/tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.mapper';

export class FindTipoGuiaAutorizacoesGatewayImpl
	implements FindTipoGuiaAutorizacoesGateway
{
	constructor(
		private tipoGuiaAutorizacoesRepository: Repository<TipoGuiaAutorizacoesEntity>,
	) {}

	async searchTipoGuiaAutorizacoes(
		name: string,
		companyId: number,
		limit: number,
	): Promise<TipoGuiaAutorizacoes[]> {
		const tiposGuia = await this.tipoGuiaAutorizacoesRepository.find({
			where: {
				tipo: ILike(`%${name || ''}%`),
				companyId,
				enabled: true,
			},
			take: limit,
		});

		if (!tiposGuia) return null;

		return tiposGuia.map((tipoGuia) =>
			TipoGuiaMapper.toTipoGuiaAutorizacoesDomain(tipoGuia),
		);
	}
}
