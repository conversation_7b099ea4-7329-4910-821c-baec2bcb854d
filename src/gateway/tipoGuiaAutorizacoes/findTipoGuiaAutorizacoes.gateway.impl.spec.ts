import { Test, TestingModule } from '@nestjs/testing';
import { ILike, Repository } from 'typeorm';
import { TipoGuiaAutorizacoesEntity } from '../entities/tipoGuiaAutorizacoes.entity';
import { FindTipoGuiaAutorizacoesGatewayImpl } from './findTipoGuiaAutorizacoes.gateway.impl';
import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';
import { TipoGuiaMapper } from 'src/shared/mapper/tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.mapper';

describe('FindTipoGuiaAutorizacoesGatewayImpl', () => {
	let findTipoGuiaAutorizacoesGatewayImpl: FindTipoGuiaAutorizacoesGatewayImpl;
	let tipoGuiaAutorizacoesRepository: Repository<TipoGuiaAutorizacoesEntity>;

	const mockTipoGuiaAutorizacoesEntity = new TipoGuiaAutorizacoesEntity();
	mockTipoGuiaAutorizacoesEntity.id = 1;
	mockTipoGuiaAutorizacoesEntity.tipo = 'Tipo Teste';
	mockTipoGuiaAutorizacoesEntity.companyId = 123;
	mockTipoGuiaAutorizacoesEntity.enabled = true;

	const mockTipoGuiaAutorizacoes = new TipoGuiaAutorizacoes(
		1,
		'Tipo Teste',
		123,
		true,
	);

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'TIPO_GUIA_AUTORIZACOES_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: 'FindTipoGuiaAutorizacoesGateway',
					useFactory: (
						tipoGuiaAutorizacoesRepository: Repository<TipoGuiaAutorizacoesEntity>,
					) =>
						new FindTipoGuiaAutorizacoesGatewayImpl(
							tipoGuiaAutorizacoesRepository,
						),
					inject: ['TIPO_GUIA_AUTORIZACOES_REPOSITORY'],
				},
			],
		}).compile();

		findTipoGuiaAutorizacoesGatewayImpl =
			module.get<FindTipoGuiaAutorizacoesGatewayImpl>(
				'FindTipoGuiaAutorizacoesGateway',
			);
		tipoGuiaAutorizacoesRepository = module.get<
			Repository<TipoGuiaAutorizacoesEntity>
		>('TIPO_GUIA_AUTORIZACOES_REPOSITORY');
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('searchTipoGuiaAutorizacoes', () => {
		it('deve encontrar tipos de guia de autorização com os parâmetros corretos', async () => {
			const name = 'Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(tipoGuiaAutorizacoesRepository, 'find')
				.mockResolvedValue([mockTipoGuiaAutorizacoesEntity]);

			jest
				.spyOn(TipoGuiaMapper, 'toTipoGuiaAutorizacoesDomain')
				.mockReturnValue(mockTipoGuiaAutorizacoes);

			const result =
				await findTipoGuiaAutorizacoesGatewayImpl.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(tipoGuiaAutorizacoesRepository.find).toHaveBeenCalledWith({
				where: {
					tipo: ILike(`%${name || ''}%`),
					companyId,
					enabled: true,
				},
				take: limit,
			});
			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(TipoGuiaAutorizacoes);
			expect(result[0].id).toBe(mockTipoGuiaAutorizacoes.id);
			expect(result[0].tipo).toBe(mockTipoGuiaAutorizacoes.tipo);
			expect(result[0].companyId).toBe(mockTipoGuiaAutorizacoes.companyId);
			expect(result[0].enabled).toBe(mockTipoGuiaAutorizacoes.enabled);
		});

		it('deve encontrar tipos de guia de autorização com os parâmetros corretos quando o search for vazio', async () => {
			const name: string = null;
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(tipoGuiaAutorizacoesRepository, 'find')
				.mockResolvedValue([mockTipoGuiaAutorizacoesEntity]);

			jest
				.spyOn(TipoGuiaMapper, 'toTipoGuiaAutorizacoesDomain')
				.mockReturnValue(mockTipoGuiaAutorizacoes);

			const result =
				await findTipoGuiaAutorizacoesGatewayImpl.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(tipoGuiaAutorizacoesRepository.find).toHaveBeenCalledWith({
				where: {
					tipo: ILike(`%%`),
					companyId,
					enabled: true,
				},
				take: limit,
			});
			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(TipoGuiaAutorizacoes);
			expect(result[0].id).toBe(mockTipoGuiaAutorizacoes.id);
			expect(result[0].tipo).toBe(mockTipoGuiaAutorizacoes.tipo);
			expect(result[0].companyId).toBe(mockTipoGuiaAutorizacoes.companyId);
			expect(result[0].enabled).toBe(mockTipoGuiaAutorizacoes.enabled);
		});

		it('deve retornar um array vazio quando não encontrar tipos de guia', async () => {
			const name = 'Tipo Inexistente';
			const companyId = 123;
			const limit = 10;

			jest.spyOn(tipoGuiaAutorizacoesRepository, 'find').mockResolvedValue([]);

			const result =
				await findTipoGuiaAutorizacoesGatewayImpl.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(tipoGuiaAutorizacoesRepository.find).toHaveBeenCalledWith({
				where: {
					tipo: ILike(`%${name || ''}%`),
					companyId,
					enabled: true,
				},
				take: limit,
			});
			expect(result).toBeDefined();
			expect(result).toHaveLength(0);
		});

		it('deve retornar null quando o resultado do repositório for null', async () => {
			const name = 'Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(tipoGuiaAutorizacoesRepository, 'find')
				.mockResolvedValue(null);

			const result =
				await findTipoGuiaAutorizacoesGatewayImpl.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(tipoGuiaAutorizacoesRepository.find).toHaveBeenCalledWith({
				where: {
					tipo: ILike(`%${name || ''}%`),
					companyId,
					enabled: true,
				},
				take: limit,
			});
			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			const name = 'Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(tipoGuiaAutorizacoesRepository, 'find')
				.mockRejectedValue(new Error('DB Error'));

			await expect(
				findTipoGuiaAutorizacoesGatewayImpl.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				),
			).rejects.toThrow('DB Error');

			expect(tipoGuiaAutorizacoesRepository.find).toHaveBeenCalledWith({
				where: {
					tipo: ILike(`%${name || ''}%`),
					companyId,
					enabled: true,
				},
				take: limit,
			});
		});
	});
});
