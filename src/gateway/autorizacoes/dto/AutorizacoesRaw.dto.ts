export type AutorizacoesRawDto = {
	ag_id: number;
	ag_created: Date;
	ag_pac: string;
	ag_regimeInternacao: string;
	ag_cidPrincipal: string;
	ag_tipoGuia: string;
	ag_tipoGuia2: string;
	ag_dataVencimento: Date;
	ag_isFavorito: boolean;
	ag_sugestao: string;
	patient_name: string;
	cod_beneficiario: string;
	carater: string;
	data_pedido: string;
	limit_date: Date;
	prestador: string;
	plan: string;
	plan_status: string;
	plan_regulamentado: boolean;
	status_ia: string;
	motivo_ia: string;
	description_ia: string;
	authorization_id: number;
	patient_id: number;
	company_id: number;
	numero_guia: string;
	audit_status: string;
	numero_reanalises: number;
	transaction_number: string;
	tags: string;
	empresa: string;
	status: string;
	status_descricao: string;
	status_cor: string;
	unimed_name: string;
	pendency: number;
	observacoes: string;
	motivo_negativa: string;
	isFavorito2: number;
	ultimo_auditou: string;
	data_ultimo_auditou: Date;
};
