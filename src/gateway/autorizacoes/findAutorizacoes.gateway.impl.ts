import { Repository, SelectQueryBuilder } from 'typeorm';
import { FindAutorizacoesGateway } from 'src/core/application/gateway/autorizacoes/findAutorizacoes.gateway';
import { AutorizacoesEntity } from '../entities/autorizacoes.entity';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { FiltrosAutorizacoesListagemDto } from 'src/core/application/dto/FiltrosAutorizacoesListagem';
import { AutorizacoesListagemDto } from 'src/core/application/dto/AutorizacoesListagem';
import { AutorizacoesMapper } from 'src/shared/mapper/autorizacoes/autorizacoes.mapper';
import { AutorizacoesRawDto } from './dto/AutorizacoesRaw.dto';
import { PERIODO_CONDICOES } from './conditions/periodo.condition';
import { PRAZO_LIMITE_CONDICOES } from './conditions/prazoLimite.condition';

export class FindAutorizacoesGatewayImpl implements FindAutorizacoesGateway {
	constructor(private autorizacoesRepository: Repository<AutorizacoesEntity>) {}

	public async findListagem(
		companyId: number,
		filtros: Paginacao & FiltrosAutorizacoesListagemDto,
	): Promise<AutorizacoesListagemDto> {
		const skip = (filtros.page - 1) * filtros.limit;
		const autorizacoesEntity = this.buildListagemQuery(
			companyId,
			skip,
			filtros.limit,
			filtros,
		);
		const autorizacoes: AutorizacoesRawDto[] =
			await autorizacoesEntity.getRawMany();
		const quantidadeTotal = await this.buildCountQuery(
			companyId,
			filtros,
		).getCount();

		const autorizacoesMap = autorizacoes.map((autorizacao) =>
			AutorizacoesMapper.toAutorizacoesViewModel(autorizacao),
		);

		return {
			pagina: filtros.page,
			quantidadeTotal,
			autorizacoes: autorizacoesMap,
		};
	}

	private buildListagemQuery(
		companyId: number,
		skip: number,
		limit: number,
		filtros: FiltrosAutorizacoesListagemDto,
	): SelectQueryBuilder<AutorizacoesEntity> {
		const query = this.autorizacoesRepository
			.createQueryBuilder('ag')
			.select([
				'ag.id',
				'p.name as patient_name',
				'p.cod_beneficiario',
				'ag.caraterInternacao as carater',
				`CASE
					WHEN ag.dataSolicitacao IS NOT NULL THEN DATE_FORMAT(ag.dataSolicitacao, '%d/%m/%Y')
					ELSE DATE_FORMAT(ag.created, '%d/%m/%Y')
				END as data_pedido`,
				`CASE
					WHEN ag.limit_date IS NOT NULL THEN DATE_FORMAT(ag.limit_date, '%d/%m/%Y')
					ELSE DATE_FORMAT(ag.limit_date, '%d/%m/%Y')
				END as limit_date`,
				'ag.pac',
				'ag.nomePrestador as prestador',
				'ag.regimeInternacao',
				'pl.name as plan',
				'p.status as plan_status',
				'p.plan_regulamentado',
				'ag.authorization_status_ia as status_ia',
				'ag.authorization_motivo_ia as motivo_ia',
				'ag.authorization_description_ia as description_ia',
				'ag.id as authorization_id',
				'ag.patient_id',
				'ag.company_id',
				'ag.numeroGuia as numero_guia',
				'ag.cidPrincipal',
				'ag.tipoGuia',
				'ag.audit_status',
				'ag.numero_reanalises',
				'ag.transaction_number',
				'ag.tipoGuia2',
				'at.tags as tags',
				`CASE
					WHEN ag.dataVencimento IS NOT NULL THEN DATE_FORMAT(ag.dataVencimento, '%d/%m/%Y')
					ELSE DATE_FORMAT(ag.dataVencimento, '%d/%m/%Y')
				END as ag_dataVencimento`,
				'corp.name as empresa',
				'aus.id as status',
				'aus.descricao as status_descricao',
				'aus.cor as status_cor',
				`CASE
					WHEN ag.created IS NOT NULL THEN DATE_FORMAT(ag.created, '%d/%m/%Y')
					ELSE DATE_FORMAT(ag.created, '%d/%m/%Y')
				END as ag_created`,
				'hc.name as unimed_name',
				'pd.id as pendency',
				'aa.observacoes',
				'mn.description as motivo_negativa',
				'ag.isFavorito',
				'CASE WHEN ag.isFavorito IS NOT NULL THEN 1 ELSE 0 END as isFavorito2',
				'ag.sugestao',
				`CASE
					WHEN ultimo_auditou.name IS NOT NULL THEN DATE_FORMAT(ultimo_auditou.name, '%d/%m/%Y')
					ELSE DATE_FORMAT(ultimo_auditou.name, '%d/%m/%Y')
				END as ultimo_auditou`,
				`CASE
				WHEN aa.created IS NOT NULL THEN DATE_FORMAT(aa.created, '%d/%m/%Y')
				ELSE DATE_FORMAT(aa.created, '%d/%m/%Y')
				END as data_ultimo_auditou`,
			])
			.leftJoin('ag.patient', 'p')
			.leftJoin('p.plan', 'pl', 'pl.company_id = p.company_id')
			.leftJoin('pendency', 'pd', 'pd.authorization_id = ag.id')
			.leftJoin('p.corporation', 'corp')
			.leftJoin('ag.hospitalCompany', 'hc')
			.leftJoin('hc.hospital', 'h')
			.leftJoin('ag.autorizacaoStatus', 'aus')
			.leftJoin('ag.motivoNegativa', 'mn')
			.leftJoin(
				`(SELECT
					auth_tag.authorization_id,
					GROUP_CONCAT(t.tag SEPARATOR ", ") as tags
				FROM
					authorizations_tags auth_tag
					LEFT JOIN tags t ON t.id = auth_tag.tag_id
				WHERE
					(auth_tag.enabled = 1 OR auth_tag.enabled IS NULL)
					AND t.enabled = 1
				GROUP BY
					auth_tag.authorization_id)`,
				'at',
				'ag.id = at.authorization_id',
			)
			.leftJoin(
				`(SELECT
					MAX(id) as last_id,
					user_id,
					created,
					authorization_id,
					status,
					observacoes
				FROM
					authorization_audit
				WHERE
					enabled = 1
				GROUP BY
					authorization_id)`,
				'aa',
				'aa.authorization_id = ag.id',
			)
			.leftJoin('users', 'ultimo_auditou', 'ultimo_auditou.id = aa.user_id')
			.where('ag.company_id = :companyId', { companyId })
			.andWhere('ag.enabled = 1')
			.andWhere('p.enabled = 1')
			.groupBy('ag.id')
			.offset(skip)
			.limit(limit);

		this.buildFilters(query, filtros);

		return query;
	}

	private buildCountQuery(
		companyId: number,
		filtros: FiltrosAutorizacoesListagemDto,
	): SelectQueryBuilder<AutorizacoesEntity> {
		const query = this.autorizacoesRepository
			.createQueryBuilder('ag')
			.leftJoin('ag.patient', 'p')
			.leftJoin('ag.hospitalCompany', 'hc')
			.leftJoin('hc.hospital', 'h')
			.leftJoin('p.corporation', 'corp')
			.where('ag.companyId = :companyId', { companyId })
			.andWhere('ag.enabled = 1')
			.andWhere('p.enabled = 1');

		this.buildFilters(query, filtros);
		return query;
	}

	private buildFilters(
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	): SelectQueryBuilder<AutorizacoesEntity> {
		if (filtros.periodo) {
			const condicao = PERIODO_CONDICOES[filtros.periodo];
			if (condicao) {
				condicao(query, filtros);
			}
		}

		if (filtros.prazoLimite && filtros.prazoLimite !== 'todos') {
			const condicao = PRAZO_LIMITE_CONDICOES[filtros.prazoLimite];
			if (condicao) {
				condicao(query, filtros);
			}
		}

		if (
			filtros.tags &&
			filtros.tags.length > 0 &&
			!filtros.tags.includes('todos')
		) {
			query.leftJoin('ag.tags', 'tag');
			query.andWhere('tag.tagId IN (:...tags)', { tags: filtros.tags });
		}

		if (
			filtros.status &&
			filtros.status.length > 0 &&
			!filtros.status.includes('todos')
		) {
			query.andWhere('ag.autorizacaoStatus.id IN (:...status)', {
				status: filtros.status,
			});
		}

		if (
			filtros.tipoGuia &&
			filtros.tipoGuia.length > 0 &&
			!filtros.tipoGuia.includes('todos')
		) {
			query.andWhere('ag.tipoGuia IN (:...tipoGuia)', {
				tipoGuia: filtros.tipoGuia,
			});
		}

		if (
			filtros.plano &&
			filtros.plano.length > 0 &&
			!filtros.plano.includes('todos')
		) {
			query.andWhere('p.plan.id IN (:...plano)', {
				plano: filtros.plano,
			});
		}

		if (
			filtros.sugestaoAcao &&
			filtros.sugestaoAcao.length > 0 &&
			!filtros.sugestaoAcao.includes('todos')
		) {
			query.andWhere('ag.sugestao IN (:...sugestao)', {
				sugestao: filtros.sugestaoAcao,
			});
		}

		if (
			filtros.prestador &&
			filtros.prestador.length > 0 &&
			!filtros.prestador.includes('todos')
		) {
			query.andWhere('ag.hospital_company_id IN (:...prestador)', {
				prestador: filtros.prestador,
			});
		}

		if (
			filtros.unimed &&
			filtros.unimed.length > 0 &&
			!filtros.unimed.includes('todos')
		) {
			query.andWhere('h.id IN (:...unimed)', {
				unimed: filtros.unimed,
			});
		}

		if (
			filtros.empresa &&
			filtros.empresa.length > 0 &&
			!filtros.empresa.includes('todos')
		) {
			query.andWhere('corp.id IN (:...empresa)', {
				empresa: filtros.empresa,
			});
		}

		if (
			filtros.carater &&
			filtros.carater.length > 0 &&
			!filtros.carater.includes('todos')
		) {
			query.andWhere('ag.caraterInternacao IN (:...carater)', {
				carater: filtros.carater,
			});
		}

		if (filtros.pendencia && filtros.pendencia !== 'todos') {
			const where =
				filtros.pendencia === '1' ? 'pd.id IS NOT NULL' : 'pd.id IS NULL';

			query.andWhere(where);
		}

		if (filtros.regime && filtros.regime !== 'todos') {
			query.andWhere('ag.regimeInternacao = :regime', {
				regime: filtros.regime,
			});
		}

		if (filtros.codigoItem && filtros.codigoItem !== 'todos') {
			query.leftJoin('ag.itens', 'itens');
			query.andWhere('itens.codigo_servico = :codigoItem', {
				codigoItem: filtros.codigoItem,
			});
		}

		return query;
	}
}
