import { Test, TestingModule } from '@nestjs/testing';
import { Repository, SelectQueryBuilder } from 'typeorm';
import { FindAutorizacoesGatewayImpl } from './findAutorizacoes.gateway.impl';
import { AutorizacoesEntity } from '../entities/autorizacoes.entity';
import { AutorizacoesMapper } from 'src/shared/mapper/autorizacoes/autorizacoes.mapper';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { FiltrosAutorizacoesListagemDto } from 'src/core/application/dto/FiltrosAutorizacoesListagem';
import { AutorizacoesRawDto } from './dto/AutorizacoesRaw.dto';
import { AutorizacoesViewModelDto } from '../../core/application/dto/AutorizacoesViewModel';
import { PeriodoFiltroEnum } from 'src/core/application/enums/autorizacoes/periodoFiltro.enum';

describe('FindAutorizacoesGatewayImpl', () => {
	let findAutorizacoesGatewayImpl: FindAutorizacoesGatewayImpl;
	let queryBuilderMock: SelectQueryBuilder<AutorizacoesEntity>;

	const mockAutorizacoesRaw: AutorizacoesRawDto = {
		ag_id: 1,
		ag_created: new Date('2024-01-01'),
		ag_pac: '789',
		ag_regimeInternacao: 'AMBULATORIAL',
		ag_cidPrincipal: 'A123',
		ag_tipoGuia: 'SADT',
		ag_tipoGuia2: 'SADT',
		ag_dataVencimento: new Date('2024-02-01'),
		ag_isFavorito: false,
		ag_sugestao: 'APROVAR',
		patient_name: 'João Silva',
		cod_beneficiario: '123456',
		carater: 'ELETIVO',
		data_pedido: '01/01/2024',
		limit_date: new Date('2024-02-01'),
		prestador: 'Hospital ABC',
		plan: 'Plano Premium',
		plan_status: 'ATIVO',
		plan_regulamentado: true,
		status_ia: 'APROVADO',
		motivo_ia: null,
		description_ia: null,
		authorization_id: 1,
		patient_id: 123,
		company_id: 1,
		numero_guia: 'G123456',
		audit_status: 'PENDENTE',
		numero_reanalises: 0,
		transaction_number: 'T789',
		tags: 'urgente, prioritário',
		empresa: 'HOSPITAL SÃO LUCAS LTDA',
		status: 'PENDENTE',
		status_descricao: 'Aguardando análise médica',
		status_cor: 'amarelo',
		unimed_name: 'UNIMED VITORIA',
		pendency: 1,
		observacoes: 'Paciente com histórico de asma',
		motivo_negativa: null,
		isFavorito2: 0,
		ultimo_auditou: 'Dra. Maria Santos',
		data_ultimo_auditou: new Date('2024-01-15'),
	};

	const mockAutorizacoesViewModel = new AutorizacoesViewModelDto(
		1, // id
		'João Silva', // nomePaciente
		'123456', // codBeneficiario
		'ELETIVO', // carater
		'01/01/2024', // dataPedido
		new Date('2024-02-01'), // dataLimite
		'789', // pac
		'Hospital ABC', // prestador
		'AMBULATORIAL', // regimeInternacao
		'Plano Premium', // plano
		'ATIVO', // statusPlano
		true, // planoRegulamentado
		'APROVADO', // statusIa
		null, // motivoIa
		null, // descricaoIa
		1, // idAutorizacao
		123, // idPaciente
		1, // idEmpresa
		'G123456', // numeroGuia
		'A123', // cidPrincipal
		'SADT', // tipoGuia
		'PENDENTE', // statusAuditoria
		0, // numeroReanalises
		'T789', // numeroTransacao
		'SADT', // tipoGuia2
		'urgente, prioritário', // tags
		new Date('2024-02-01'), // dataVencimento
		'HOSPITAL SÃO LUCAS LTDA', // empresa
		'PENDENTE', // status
		'Aguardando análise médica', // statusDescricao
		'amarelo', // statusCor
		new Date('2024-01-01'), // dataCriacao
		'UNIMED VITORIA', // nomeUnimed
		1, // pendencia
		'Paciente com histórico de asma', // observacoes
		null, // motivoNegativa
		false, // isFavorito
		0, // isFavorito2
		'APROVAR', // sugestao
		'Dra. Maria Santos', // ultimoAuditou
		new Date('2024-01-15'), // dataUltimoAuditou
	);

	beforeEach(async () => {
		queryBuilderMock = {
			select: jest.fn().mockReturnThis(),
			leftJoin: jest.fn().mockReturnThis(),
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			groupBy: jest.fn().mockReturnThis(),
			offset: jest.fn().mockReturnThis(),
			limit: jest.fn().mockReturnThis(),
			getRawMany: jest.fn().mockResolvedValue([]),
			getCount: jest.fn().mockResolvedValue(0),
		} as unknown as SelectQueryBuilder<AutorizacoesEntity>;

		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'AUTORIZACOES_REPOSITORY',
					useValue: {
						createQueryBuilder: jest.fn().mockReturnValue(queryBuilderMock),
					},
				},
				{
					provide: FindAutorizacoesGatewayImpl,
					useFactory: (
						autorizacoesRepository: Repository<AutorizacoesEntity>,
					) => new FindAutorizacoesGatewayImpl(autorizacoesRepository),
					inject: ['AUTORIZACOES_REPOSITORY'],
				},
			],
		}).compile();

		findAutorizacoesGatewayImpl = module.get<FindAutorizacoesGatewayImpl>(
			FindAutorizacoesGatewayImpl,
		);
	});

	describe('findListagem', () => {
		it('deve retornar listagem paginada corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 2,
				limit: 10,
			};

			(queryBuilderMock.getRawMany as jest.Mock).mockResolvedValue([
				mockAutorizacoesRaw,
			]);
			(queryBuilderMock.getCount as jest.Mock).mockResolvedValue(15);
			jest
				.spyOn(AutorizacoesMapper, 'toAutorizacoesViewModel')
				.mockReturnValue(mockAutorizacoesViewModel);

			const result = await findAutorizacoesGatewayImpl.findListagem(
				companyId,
				filtros,
			);

			expect(result).toEqual({
				pagina: 2,
				quantidadeTotal: 15,
				autorizacoes: [mockAutorizacoesViewModel],
			});
			expect(queryBuilderMock.offset).toHaveBeenCalledWith(10);
			expect(queryBuilderMock.limit).toHaveBeenCalledWith(10);
		});

		it('deve aplicar filtro de tags corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				tags: ['tag1', 'tag2'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.leftJoin).toHaveBeenCalledWith('ag.tags', 'tag');
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'tag.tagId IN (:...tags)',
				{ tags: filtros.tags },
			);
		});

		it('não deve aplicar filtro de tags quando incluir "todos"', async () => {
			const companyId = 1;
			const filtrosDto = new FiltrosAutorizacoesListagemDto();
			filtrosDto.tags = ['todos', 'tag1'];

			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				tags: filtrosDto.tags,
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.leftJoin).not.toHaveBeenCalledWith(
				'ag.tags',
				'tag',
			);
		});

		it('deve aplicar filtro de status corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				status: ['pendente', 'aprovado'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.autorizacaoStatus.id IN (:...status)',
				{ status: filtros.status },
			);
		});

		it('deve aplicar filtro de tipo de guia corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				tipoGuia: ['SADT', 'INTERNACAO'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.tipoGuia IN (:...tipoGuia)',
				{ tipoGuia: filtros.tipoGuia },
			);
		});

		it('deve aplicar filtro de plano corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				plano: ['1', '2'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'p.plan.id IN (:...plano)',
				{ plano: filtros.plano },
			);
		});

		it('deve aplicar filtro de sugestão de ação corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				sugestaoAcao: ['APROVAR', 'NEGAR'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.sugestao IN (:...sugestao)',
				{ sugestao: filtros.sugestaoAcao },
			);
		});

		it('deve aplicar filtro de prestador corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				prestador: ['1', '2'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.hospital_company_id IN (:...prestador)',
				{ prestador: filtros.prestador },
			);
		});

		it('deve aplicar filtro de unimed corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				unimed: ['1', '2'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'h.id IN (:...unimed)',
				{ unimed: filtros.unimed },
			);
		});

		it('deve aplicar filtro de empresa corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				empresa: ['1', '2'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'corp.id IN (:...empresa)',
				{ empresa: filtros.empresa },
			);
		});

		it('deve aplicar filtro de caráter corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				carater: ['ELETIVO', 'URGENCIA'],
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.caraterInternacao IN (:...carater)',
				{ carater: filtros.carater },
			);
		});

		it('deve aplicar filtro de pendência corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				pendencia: '1',
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'pd.id IS NOT NULL',
			);
		});

		it('deve aplicar filtro de regime corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				regime: 'HOSPITALAR',
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.regimeInternacao = :regime',
				{ regime: filtros.regime },
			);
		});

		it('deve aplicar filtro de código de item corretamente', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				codigoItem: '12345',
			};

			await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

			expect(queryBuilderMock.leftJoin).toHaveBeenCalledWith(
				'ag.itens',
				'itens',
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'itens.codigo_servico = :codigoItem',
				{ codigoItem: filtros.codigoItem },
			);
		});

		it('deve lidar com erro na consulta', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
			};

			(queryBuilderMock.getRawMany as jest.Mock).mockRejectedValue(
				new Error('Database error'),
			);

			await expect(
				findAutorizacoesGatewayImpl.findListagem(companyId, filtros),
			).rejects.toThrow('Database error');
		});
	});

	it('deve aplicar filtro de período corretamente', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			periodo: PeriodoFiltroEnum.PEDIDOS_HOJE,
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
			expect.stringContaining(
				'ag.dataSolicitacao BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)',
			),
		);
	});

	it('não deve aplicar filtro de período quando não especificado', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith(
			'ag.created >= :startDate AND ag.created <= :endDate',
			expect.any(Object),
		);
	});

	it('deve aplicar filtro de prazo limite corretamente', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			prazoLimite: 'todos',
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledTimes(4);
		expect(queryBuilderMock.andWhere).toHaveBeenNthCalledWith(
			1,
			'ag.enabled = 1',
		);
		expect(queryBuilderMock.andWhere).toHaveBeenNthCalledWith(
			2,
			'p.enabled = 1',
		);
		expect(queryBuilderMock.andWhere).toHaveBeenNthCalledWith(
			3,
			'ag.enabled = 1',
		);
	});

	it('não deve aplicar filtro de prazo limite quando for "todos"', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			prazoLimite: 'todos',
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith(
			'ag.limit_date < :currentDate',
			expect.any(Object),
		);
	});
	it('deve aplicar filtro de prazoLimiteEm corretamente', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			prazoLimite: 'prazoLimiteEm',
			prazoInicial: new Date('2024-01-01'),
			prazoFinal: new Date('2024-01-31'),
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
			'ag.limit_date BETWEEN :dataInicio AND :dataFim',
			{
				dataInicio: filtros.prazoInicial,
				dataFim: filtros.prazoFinal,
			},
		);
	});

	it('não deve aplicar filtro quando prazoLimite for "todos"', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			prazoLimite: 'todos',
			prazoInicial: new Date('2024-01-01'),
			prazoFinal: new Date('2024-01-31'),
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith(
			'ag.limit_date BETWEEN :dataInicio AND :dataFim',
			expect.any(Object),
		);
	});

	it('não deve aplicar filtro quando prazoLimite não for informado', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith(
			'ag.limit_date BETWEEN :dataInicio AND :dataFim',
			expect.any(Object),
		);
	});

	it('deve aplicar filtro de pendência quando valor for "1"', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			pendencia: '1',
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('pd.id IS NOT NULL');
	});

	it('deve aplicar filtro de pendência quando valor for diferente de "1"', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			pendencia: '0',
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith('pd.id IS NULL');
	});

	it('não deve aplicar filtro de pendência quando valor for "todos"', async () => {
		const companyId = 1;
		const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
			page: 1,
			limit: 10,
			pendencia: 'todos',
		};

		await findAutorizacoesGatewayImpl.findListagem(companyId, filtros);

		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith(
			'pd.id IS NOT NULL',
		);
		expect(queryBuilderMock.andWhere).not.toHaveBeenCalledWith('pd.id IS NULL');
	});
});
