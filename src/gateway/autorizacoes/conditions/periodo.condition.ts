import { SelectQueryBuilder } from 'typeorm';
import { PeriodoFiltroEnum } from '../../../core/application/enums/autorizacoes/periodoFiltro.enum';
import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';
import { FiltrosAutorizacoesListagemDto } from '../../../core/application/dto/FiltrosAutorizacoesListagem';

type PeriodoCondicoesType = {
	[key in PeriodoFiltroEnum]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	) => SelectQueryBuilder<AutorizacoesEntity>;
};

export const PERIODO_CONDICOES: PeriodoCondicoesType = {
	[PeriodoFiltroEnum.PEDIDOS_EM]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	) =>
		query.andWhere('ag.dataSolicitacao BETWEEN :dataInicio AND :dataFim', {
			dataInicio: filtros.dataInicio,
			dataFim: filtros.dataFim,
		}),

	[PeriodoFiltroEnum.PEDIDOS_ESTE_MES]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
	) =>
		query.andWhere(
			'ag.dataSolicitacao BETWEEN DATE_FORMAT(NOW(), "%Y-%m-01") AND LAST_DAY(NOW())',
		),

	[PeriodoFiltroEnum.PEDIDOS_HOJE]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
	) =>
		query.andWhere(
			'ag.dataSolicitacao BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)',
		),

	[PeriodoFiltroEnum.AUTORIZADOS_EM]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	) => {
		query.leftJoin('ag.autorizacoesAudit', 'aa2');
		query.andWhere('aa2.created BETWEEN :dataInicio AND :dataFim', {
			dataInicio: filtros.dataInicio,
			dataFim: filtros.dataFim,
		});
		query.andWhere('aa2.status = "autorizado"');
		return query;
	},

	[PeriodoFiltroEnum.AUTORIZADOS_ESTE_MES]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
	) => {
		query.leftJoin('ag.autorizacoesAudit', 'aa2');
		query.andWhere(
			'aa2.created BETWEEN DATE_FORMAT(NOW(), "%Y-%m-01") AND LAST_DAY(NOW())',
		);
		query.andWhere('aa2.status = "autorizado"');
		return query;
	},

	[PeriodoFiltroEnum.NEGADOS_EM]: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	) => {
		query.leftJoin('ag.autorizacoesAudit', 'aa2');
		query.andWhere('aa2.created BETWEEN :dataInicio AND :dataFim', {
			dataInicio: filtros.dataInicio,
			dataFim: filtros.dataFim,
		});
		query.andWhere('aa2.status = "negado"');
		return query;
	},
};
