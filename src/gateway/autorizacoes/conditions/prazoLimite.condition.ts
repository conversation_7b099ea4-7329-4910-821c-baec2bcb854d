import { SelectQueryBuilder } from 'typeorm';
import { AutorizacoesEntity } from '../../entities/autorizacoes.entity';
import { FiltrosAutorizacoesListagemDto } from '../../../core/application/dto/FiltrosAutorizacoesListagem';

type PrazoLimiteCondicoesType = {
	prazoLimiteEm: (
		query: SelectQueryBuilder<AutorizacoesEntity>,
		filtros: FiltrosAutorizacoesListagemDto,
	) => SelectQueryBuilder<AutorizacoesEntity>;
};

export const PRAZO_LIMITE_CONDICOES: PrazoLimiteCondicoesType = {
	prazoLimiteEm: (query, filtros) =>
		query.andWhere('ag.limit_date BETWEEN :dataInicio AND :dataFim', {
			dataInicio: filtros.prazoInicial,
			dataFim: filtros.prazoFinal,
		}),
};
