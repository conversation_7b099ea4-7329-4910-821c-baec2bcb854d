import { SelectQueryBuilder } from 'typeorm';
import { AutorizacoesEntity } from '../../entities/autorizacoes.entity';
import { PeriodoFiltroEnum } from '../../../core/application/enums/autorizacoes/periodoFiltro.enum';
import { PERIODO_CONDICOES } from './periodo.condition';
import { FiltrosAutorizacoesListagemDto } from '../../../core/application/dto/FiltrosAutorizacoesListagem';

describe('PERIODO_CONDICOES', () => {
	let queryBuilderMock: jest.Mocked<SelectQueryBuilder<AutorizacoesEntity>>;
	const filtros: FiltrosAutorizacoesListagemDto = {
		dataInicio: new Date('2024-01-01'),
		dataFim: new Date('2024-01-31'),
	};
	beforeEach(() => {
		queryBuilderMock = {
			andWhere: jest.fn().mockReturnThis(),
			leftJoin: jest.fn().mockReturnThis(),
		} as unknown as jest.Mocked<SelectQueryBuilder<AutorizacoesEntity>>;
	});

	describe('PEDIDOS_EM', () => {
		it('deve aplicar filtro de período com datas específicas', () => {
			const filtros: FiltrosAutorizacoesListagemDto = {
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
			};

			PERIODO_CONDICOES[PeriodoFiltroEnum.PEDIDOS_EM](
				queryBuilderMock,
				filtros,
			);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.dataSolicitacao BETWEEN :dataInicio AND :dataFim',
				{
					dataInicio: filtros.dataInicio,
					dataFim: filtros.dataFim,
				},
			);
		});
	});

	describe('PEDIDOS_ESTE_MES', () => {
		it('deve aplicar filtro para pedidos do mês atual', () => {
			PERIODO_CONDICOES[PeriodoFiltroEnum.PEDIDOS_ESTE_MES](queryBuilderMock, {
				dataFim: new Date(),
				dataInicio: new Date(),
			});

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.dataSolicitacao BETWEEN DATE_FORMAT(NOW(), "%Y-%m-01") AND LAST_DAY(NOW())',
			);
		});
	});

	describe('PEDIDOS_HOJE', () => {
		it('deve aplicar filtro para pedidos do dia atual', () => {
			PERIODO_CONDICOES[PeriodoFiltroEnum.PEDIDOS_HOJE](
				queryBuilderMock,
				filtros,
			);

			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'ag.dataSolicitacao BETWEEN CURDATE() AND DATE_ADD(CURDATE(), INTERVAL 1 DAY)',
			);
		});
	});

	describe('AUTORIZADOS_EM', () => {
		it('deve aplicar filtro para autorizações em período específico', () => {
			const filtros: FiltrosAutorizacoesListagemDto = {
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
			};

			PERIODO_CONDICOES[PeriodoFiltroEnum.AUTORIZADOS_EM](
				queryBuilderMock,
				filtros,
			);

			expect(queryBuilderMock.leftJoin).toHaveBeenCalledWith(
				'ag.autorizacoesAudit',
				'aa2',
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.created BETWEEN :dataInicio AND :dataFim',
				{
					dataInicio: filtros.dataInicio,
					dataFim: filtros.dataFim,
				},
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.status = "autorizado"',
			);
		});
	});

	describe('AUTORIZADOS_ESTE_MES', () => {
		it('deve aplicar filtro para autorizações do mês atual', () => {
			PERIODO_CONDICOES[PeriodoFiltroEnum.AUTORIZADOS_ESTE_MES](
				queryBuilderMock,
				filtros,
			);

			expect(queryBuilderMock.leftJoin).toHaveBeenCalledWith(
				'ag.autorizacoesAudit',
				'aa2',
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.created BETWEEN DATE_FORMAT(NOW(), "%Y-%m-01") AND LAST_DAY(NOW())',
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.status = "autorizado"',
			);
		});
	});

	describe('NEGADOS_EM', () => {
		it('deve aplicar filtro para negações em período específico', () => {
			PERIODO_CONDICOES[PeriodoFiltroEnum.NEGADOS_EM](
				queryBuilderMock,
				filtros,
			);

			expect(queryBuilderMock.leftJoin).toHaveBeenCalledWith(
				'ag.autorizacoesAudit',
				'aa2',
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.created BETWEEN :dataInicio AND :dataFim',
				{
					dataInicio: filtros.dataInicio,
					dataFim: filtros.dataFim,
				},
			);
			expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
				'aa2.status = "negado"',
			);
		});
	});

	it('deve conter todas as condições definidas no PeriodoFiltroEnum', () => {
		const enumValues = Object.values(PeriodoFiltroEnum);
		const condicoesKeys = Object.keys(PERIODO_CONDICOES);

		expect(condicoesKeys).toHaveLength(enumValues.length);
		enumValues.forEach((enumValue) => {
			expect(PERIODO_CONDICOES).toHaveProperty(enumValue);
		});
	});
});
