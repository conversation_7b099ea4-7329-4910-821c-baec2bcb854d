import { ILike, Repository } from 'typeorm';
import { FindEmpresaGateway } from '../../core/application/gateway/empresa/findEmpresa.gateway';
import { EmpresaEntity } from '../entities/empresa.entity';
import { Empresa } from '../../core/domain/Empresa';
import { EmpresaMapper } from '../../shared/mapper/empresa/empresa.mapper';

export class FindEmpresaGatewayImpl implements FindEmpresaGateway {
	constructor(private readonly empresaRepository: Repository<EmpresaEntity>) {}

	async searchEmpresa(
		name: string,
		companyId: number,
		limit: number,
	): Promise<Empresa[]> {
		const result = await this.empresaRepository.find({
			where: {
				name: ILike(`%${name || ''}%`),
				companyId,
				enabled: true,
			},
			take: limit,
		});

		if (!result) return null;
		return result.map((empresa) => EmpresaMapper.toEmpresaDomain(empresa));
	}
}
