import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { EmpresaEntity } from '../entities/empresa.entity';
import { FindEmpresaGatewayImpl } from './findEmpresa.gateway.impl';
import { Empresa } from 'src/core/domain/Empresa';
import { EmpresaMapper } from 'src/shared/mapper/empresa/empresa.mapper';

describe('FindEmpresaGatewayImpl', () => {
	let findEmpresaGatewayImpl: FindEmpresaGatewayImpl;
	let empresaRepository: Repository<EmpresaEntity>;

	// Mock EmpresaEntity
	const mockEmpresaEntity = new EmpresaEntity();
	mockEmpresaEntity.id = 1;
	mockEmpresaEntity.name = 'Empresa Test';
	mockEmpresaEntity.companyId = 100;
	mockEmpresaEntity.codInterno = 'E001';
	mockEmpresaEntity.cnpj = '12345678901234';
	mockEmpresaEntity.enabled = true;
	mockEmpresaEntity.created = new Date();
	mockEmpresaEntity.updated = new Date();

	// Mock Empresa domain model
	const mockEmpresa = new Empresa(1, 'Empresa Test', 'E001', '12345678901234');

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'EMPRESA_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: 'FindEmpresaGateway',
					useFactory: (empresaRepository: Repository<EmpresaEntity>) =>
						new FindEmpresaGatewayImpl(empresaRepository),
					inject: ['EMPRESA_REPOSITORY'],
				},
			],
		}).compile();

		findEmpresaGatewayImpl =
			module.get<FindEmpresaGatewayImpl>('FindEmpresaGateway');
		empresaRepository =
			module.get<Repository<EmpresaEntity>>('EMPRESA_REPOSITORY');
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('searchEmpresa', () => {
		it('deve encontrar empresas pelo nome', async () => {
			const name = 'Empresa';
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(empresaRepository, 'find')
				.mockResolvedValue([mockEmpresaEntity]);

			jest.spyOn(EmpresaMapper, 'toEmpresaDomain').mockReturnValue(mockEmpresa);

			const result = await findEmpresaGatewayImpl.searchEmpresa(
				name,
				companyId,
				limit,
			);

			expect(empresaRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(), // ILike is hard to test directly
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(Empresa);
			expect(result[0].id).toBe(mockEmpresa.id);
			expect(result[0].nome).toBe(mockEmpresa.nome);
			expect(result[0].codInterno).toBe(mockEmpresa.codInterno);
			expect(result[0].cnpj).toBe(mockEmpresa.cnpj);
		});

		it('deve encontrar empresas pelo nome mesmo se for null', async () => {
			const name: string = null;
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(empresaRepository, 'find')
				.mockResolvedValue([mockEmpresaEntity]);

			jest.spyOn(EmpresaMapper, 'toEmpresaDomain').mockReturnValue(mockEmpresa);

			const result = await findEmpresaGatewayImpl.searchEmpresa(
				name,
				companyId,
				limit,
			);

			expect(empresaRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(), // ILike is hard to test directly
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(Empresa);
			expect(result[0].id).toBe(mockEmpresa.id);
			expect(result[0].nome).toBe(mockEmpresa.nome);
			expect(result[0].codInterno).toBe(mockEmpresa.codInterno);
			expect(result[0].cnpj).toBe(mockEmpresa.cnpj);
		});

		it('deve retornar uma lista vazia quando não encontrar empresas', async () => {
			const name = 'Empresa Inexistente';
			const companyId = 100;
			const limit = 10;

			jest.spyOn(empresaRepository, 'find').mockResolvedValue([]);

			const result = await findEmpresaGatewayImpl.searchEmpresa(
				name,
				companyId,
				limit,
			);

			expect(empresaRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(0);
		});

		it('deve retornar null quando o resultado for null', async () => {
			const name = 'Empresa';
			const companyId = 100;
			const limit = 10;

			jest.spyOn(empresaRepository, 'find').mockResolvedValue(null);

			const result = await findEmpresaGatewayImpl.searchEmpresa(
				name,
				companyId,
				limit,
			);

			expect(empresaRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			const name = 'Empresa';
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(empresaRepository, 'find')
				.mockRejectedValue(new Error('DB Error'));

			await expect(
				findEmpresaGatewayImpl.searchEmpresa(name, companyId, limit),
			).rejects.toThrow('DB Error');

			expect(empresaRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});
		});
	});
});
