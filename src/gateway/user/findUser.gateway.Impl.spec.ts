import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { User } from 'src/core/domain/User';
import { UserMapper } from 'src/shared/mapper/user/user.mapper';
import { FindUserGatewayImpl } from './findUser.gateway.Impl';
import { NotFoundException } from '@nestjs/common';
import { GetUserDto } from 'src/entrypoint/dto/user/get-user.dto';
import { UserWithPasswordMapper } from 'src/shared/mapper/user/userWithPassword.mapper';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';

describe('FindUserGatewayImpl', () => {
	let findUserGateway: FindUserGatewayImpl;
	let userRepository: Repository<UserEntity>;

	const mockGetUserDto: GetUserDto = {
		id: 1,
		name: '<PERSON>',
		email: '<EMAIL>',
		companyId: '3',
		role: UserRoleEnum.ADMIN,
	};

	const mockUserEntity: UserEntity = {
		id: 1,
		name: '<PERSON> Doe',
		email: '<EMAIL>',
		companyId: '3',
		password: 'password',
		enabled: 1,
		role: UserRoleEnum.ADMIN,
	};

	const mockUserDomain: User = {
		id: 1,
		name: 'John Doe',
		email: '<EMAIL>',
		companyId: '3',
	};

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'USERS_REPOSITORY',
					useValue: {
						findOneBy: jest.fn(),
						findOneOrFail: jest.fn(),
					},
				},
				{
					provide: 'FindUserGateway',
					useFactory: (userRepository: Repository<UserEntity>) =>
						new FindUserGatewayImpl(userRepository),
					inject: ['USERS_REPOSITORY'],
				},
			],
		}).compile();

		findUserGateway = module.get<FindUserGatewayImpl>('FindUserGateway');
		userRepository = module.get<Repository<UserEntity>>('USERS_REPOSITORY');
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should call userRepository.findOneBy with the correct id and return a User', async () => {
		(userRepository.findOneBy as jest.Mock).mockResolvedValueOnce(
			mockGetUserDto,
		);

		jest.spyOn(UserMapper, 'toUserDomain').mockReturnValueOnce(mockUserDomain);

		const result = await findUserGateway.findById(1);

		expect(userRepository.findOneBy).toHaveBeenCalledWith({ id: 1 });
		expect(UserMapper.toUserDomain).toHaveBeenCalledWith(mockGetUserDto);
		expect(result).toEqual(mockUserDomain);
	});

	it('should return null if user is not found', async () => {
		jest.spyOn(userRepository, 'findOneBy').mockResolvedValueOnce(undefined);

		await expect(findUserGateway.findById(1)).rejects.toThrow(
			NotFoundException,
		);

		expect(userRepository.findOneBy).toHaveBeenCalledWith({ id: 1 });
	});

	it('should call userRepository.findOneOrFail with the correct options and return a User', async () => {
		jest
			.spyOn(userRepository, 'findOneOrFail')
			.mockResolvedValueOnce(mockUserEntity);

		jest
			.spyOn(UserWithPasswordMapper, 'toUserDomain')
			.mockReturnValueOnce(mockUserEntity);

		const result = await findUserGateway.findOneOrFail({ where: { id: 1 } });

		expect(userRepository.findOneOrFail).toHaveBeenCalledWith({
			where: { id: 1 },
		});
		expect(UserWithPasswordMapper.toUserDomain).toHaveBeenCalledWith(
			mockUserEntity,
		);
		expect(result).toEqual(mockUserEntity);
	});
});
