import { User } from 'src/core/domain/User';
import { FindOneOptions, Repository } from 'typeorm';
import { UserEntity } from '../entities/user.entity';
import { FindUserGateway } from 'src/core/application/gateway/user/findUser.gateway';
import { UserMapper } from 'src/shared/mapper/user/user.mapper';
import { NotFoundException } from '@nestjs/common';
import { UserWithPasswordMapper } from 'src/shared/mapper/user/userWithPassword.mapper';

export class FindUserGatewayImpl implements FindUserGateway {
	constructor(private userRepository: Repository<UserEntity>) {}

	async findById(id: number): Promise<User> {
		const userEntity = await this.userRepository.findOneBy({ id });
		if (!userEntity) throw new NotFoundException('User not found');

		return UserMapper.toUserDomain(userEntity);
	}

	async findOneOrFail(options: FindOneOptions<User>): Promise<User> {
		const user = await this.userRepository.findOneOrFail(options);
		return UserWithPasswordMapper.toUserDomain(user);
	}
}
