import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { HospitalEntity } from '../entities/hospital.entity';
import { FindHospitalGatewayImpl } from './findHospital.gateway.impl';
import { Hospital } from 'src/core/domain/Hospital';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';

describe('FindHospitalGatewayImpl', () => {
	let findHospitalGatewayImpl: FindHospitalGatewayImpl;
	let hospitalRepository: Repository<HospitalEntity>;

	const mockHospitalEntity = new HospitalEntity();
	mockHospitalEntity.id = 1;
	mockHospitalEntity.noFantasia = 'Hospital Test';
	mockHospitalEntity.noRazaoSocial = 'Hospital Test LTDA';
	mockHospitalEntity.nuCnpj = '12345678901234';
	mockHospitalEntity.noUrl = 'http://test.com';
	mockHospitalEntity.nuLatitude = '-23.5505';
	mockHospitalEntity.nuLongitude = '-46.6333';
	mockHospitalEntity.tpEstabSempreAberto = 'S';
	mockHospitalEntity.stConexaoInternet = 'S';
	mockHospitalEntity.tpGestao = 'Municipal';
	mockHospitalEntity.tpLogradouroCarefy = 'Rua';
	mockHospitalEntity.isUnimed = true;
	mockHospitalEntity.created = new Date();
	mockHospitalEntity.updated = new Date();

	const mockHospital = new Hospital(
		1, // id
		'CO_UNIDADE_TEST', // coUnidade
		'CO_CNES_TEST', // coCnes
		new Date(), // created
		new Date(), // updated
		'NU_CNPJ_MANT_TEST', // nuCnpjMantenedora
		'Hospital Test LTDA', // noRazaoSocial
		'Hospital Test', // noFantasia
		'NO_LOGRADOURO_TEST', // noLogradouro
		'NU_ENDERECO_TEST', // nuEndereco
		'NO_COMPLEMENTO_TEST', // noComplemento
		'NO_BAIRRO_TEST', // noBairro
		'CO_CEP_TEST', // coCep
		'NU_TELEFONE_TEST', // nuTelefone
		'NU_FAX_TEST', // nuFax
		'NO_EMAIL_TEST', // noEmail
		'NU_CPF_TEST', // nuCpf
		'12345678901234', // nuCnpj
		'CO_ATIVIDADE_TEST', // coAtividade
		'CO_CLIENTELA_TEST', // coClientela
		1, // tpUnidade
		'CO_TURNO_TEST', // coTurnoAtendimento
		2, // coEstadoGestor
		3, // coMunicipioGestor
		'2023-01-01', // dtAtualizacao
		'http://test.com', // noUrl
		'-23.5505', // nuLatitude
		'-46.6333', // nuLongitude
		'S', // tpEstabSempreAberto
		'S', // stConexaoInternet
		'Municipal', // tpGestao
		'Rua', // tpLogradouroCarefy
		true, // isUnimed
	);

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'HOSPITAL_REPOSITORY',
					useValue: {
						findOne: jest.fn(),
					},
				},
				{
					provide: 'FindHospitalGateway',
					useFactory: (hospitalRepository: Repository<HospitalEntity>) =>
						new FindHospitalGatewayImpl(hospitalRepository),
					inject: ['HOSPITAL_REPOSITORY'],
				},
			],
		}).compile();

		findHospitalGatewayImpl = module.get<FindHospitalGatewayImpl>(
			'FindHospitalGateway',
		);
		hospitalRepository = module.get<Repository<HospitalEntity>>(
			'HOSPITAL_REPOSITORY',
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('findOneByName', () => {
		it('deve encontrar um hospital pelo nome fantasia', async () => {
			const hospitalName = 'Hospital Test';
			jest
				.spyOn(hospitalRepository, 'findOne')
				.mockResolvedValue(mockHospitalEntity);
			jest
				.spyOn(HospitalMapper, 'toHospitalDomain')
				.mockReturnValue(mockHospital);

			const result = await findHospitalGatewayImpl.findOneByName(hospitalName);

			expect(hospitalRepository.findOne).toHaveBeenCalledWith({
				where: [{ noFantasia: hospitalName }, { noRazaoSocial: hospitalName }],
			});
			expect(result).toBeDefined();
			expect(result).toBeInstanceOf(Hospital);
			expect(result.id).toBe(mockHospital.id);
			expect(result.noFantasia).toBe(mockHospital.noFantasia);
			expect(result.noRazaoSocial).toBe(mockHospital.noRazaoSocial);
		});

		it('deve encontrar um hospital pelo quando o nome é undefined', async () => {
			const hospitalName: string = undefined;
			jest
				.spyOn(hospitalRepository, 'findOne')
				.mockResolvedValue(mockHospitalEntity);
			jest
				.spyOn(HospitalMapper, 'toHospitalDomain')
				.mockReturnValue(mockHospital);

			const result = await findHospitalGatewayImpl.findOneByName(hospitalName);

			expect(hospitalRepository.findOne).toHaveBeenCalledWith({
				where: [{ noFantasia: hospitalName }, { noRazaoSocial: hospitalName }],
			});
			expect(result).toBeDefined();
			expect(result).toBeInstanceOf(Hospital);
			expect(result.id).toBe(mockHospital.id);
			expect(result.noFantasia).toBe(mockHospital.noFantasia);
			expect(result.noRazaoSocial).toBe(mockHospital.noRazaoSocial);
		});

		it('deve encontrar um hospital pela razão social', async () => {
			const hospitalName = 'Hospital Test LTDA';
			jest
				.spyOn(hospitalRepository, 'findOne')
				.mockResolvedValue(mockHospitalEntity);
			jest
				.spyOn(HospitalMapper, 'toHospitalDomain')
				.mockReturnValue(mockHospital);

			const result = await findHospitalGatewayImpl.findOneByName(hospitalName);

			expect(hospitalRepository.findOne).toHaveBeenCalledWith({
				where: [{ noFantasia: hospitalName }, { noRazaoSocial: hospitalName }],
			});
			expect(result).toBeDefined();
			expect(result).toBeInstanceOf(Hospital);
			expect(result.id).toBe(mockHospital.id);
			expect(result.noFantasia).toBe(mockHospital.noFantasia);
			expect(result.noRazaoSocial).toBe(mockHospital.noRazaoSocial);
		});

		it('deve retornar null quando não encontrar o hospital', async () => {
			const hospitalName = 'Hospital Inexistente';
			jest.spyOn(hospitalRepository, 'findOne').mockResolvedValue(null);

			const result = await findHospitalGatewayImpl.findOneByName(hospitalName);

			expect(hospitalRepository.findOne).toHaveBeenCalledWith({
				where: [{ noFantasia: hospitalName }, { noRazaoSocial: hospitalName }],
			});
			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			const hospitalName = 'Hospital Test';
			jest
				.spyOn(hospitalRepository, 'findOne')
				.mockRejectedValue(new Error('DB Error'));

			await expect(
				findHospitalGatewayImpl.findOneByName(hospitalName),
			).rejects.toThrow('DB Error');

			expect(hospitalRepository.findOne).toHaveBeenCalledWith({
				where: [{ noFantasia: hospitalName }, { noRazaoSocial: hospitalName }],
			});
		});
	});
});
