import { FindHospitalGateway } from 'src/core/application/gateway/hospital/findHospital.gateway';
import { Hospital } from 'src/core/domain/Hospital';
import { Repository } from 'typeorm';
import { HospitalEntity } from '../entities/hospital.entity';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';

export class FindHospitalGatewayImpl implements FindHospitalGateway {
	constructor(private hospitalRepository: Repository<HospitalEntity>) {}

	async findOneByName(name: string): Promise<Hospital> {
		const hospital = await this.hospitalRepository.findOne({
			where: [{ noFantasia: name }, { noRazaoSocial: name }],
		});
		if (!hospital) return null;

		return HospitalMapper.toHospitalDomain(hospital);
	}
}
