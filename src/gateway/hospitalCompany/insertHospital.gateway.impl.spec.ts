import { Repository, DeepPartial } from 'typeorm';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';
import { HospitalEntity } from '../entities/hospital.entity';
import { Hospital } from 'src/core/domain/Hospital';
import { InsertHospitalGateway } from 'src/core/application/gateway/hospital/insertHospitalGateway';
import { InsertHospitalGatewayImpl } from './insertHospital.gateway.impl';
import { CityEntity } from '../entities/city.entity';
import { CnesMunicipioOldEntity } from '../entities/cnesMunicipioOld.entity';

describe('InsertHospitalGatewayImpl', () => {
	let hospitalCompanyRepository: Repository<HospitalsCompanyEntity>;
	let hospitalRepository: Repository<HospitalEntity>;
	let cityRepository: Repository<CityEntity>;
	let cnesMunicipioOldRepository: Repository<CnesMunicipioOldEntity>;
	let gateway: InsertHospitalGateway;
	let fakeHospital: Hospital;
	let fakeCity: CityEntity;

	beforeEach(() => {
		hospitalCompanyRepository = {
			create: jest.fn(),
			save: jest.fn(),
		} as unknown as Repository<HospitalsCompanyEntity>;

		hospitalRepository = {
			create: jest.fn(),
			insert: jest.fn(),
		} as unknown as Repository<HospitalEntity>;

		cnesMunicipioOldRepository = {
			findOneBy: jest.fn(),
		} as unknown as Repository<CnesMunicipioOldEntity>;

		cityRepository = {
			findOneBy: jest.fn(),
		} as unknown as Repository<CityEntity>;

		gateway = new InsertHospitalGatewayImpl(
			hospitalCompanyRepository,
			hospitalRepository,
			cnesMunicipioOldRepository,
			cityRepository,
		);

		fakeHospital = new Hospital(
			1, // id
			'CO_UNIDADE_TEST', // coUnidade
			'CO_CNES_TEST', // coCnes
			null, // created
			null, // updated
			'NU_CNPJ_MANT_TEST', // nuCnpjMantenedora
			'NO_RAZAO_SOCIAL_TEST', // noRazaoSocial
			'NO_FANTASIA_TEST', // noFantasia
			'NO_LOGRADOURO_TEST', // noLogradouro
			'NU_ENDERECO_TEST', // nuEndereco
			'NO_COMPLEMENTO_TEST', // noComplemento
			'NO_BAIRRO_TEST', // noBairro
			'CO_CEP_TEST', // coCep
			'NU_TELEFONE_TEST', // nuTelefone
			'NU_FAX_TEST', // nuFax
			'NO_EMAIL_TEST', // noEmail
			'NU_CPF_TEST', // nuCpf
			'NU_CNPJ_TEST', // nuCnpj
			'CO_ATIVIDADE_TEST', // coAtividade
			'CO_CLIENTELA_TEST', // coClientela
			2, // tpUnidade
			'CO_TURNO_TEST', // coTurnoAtendimento
			3, // coEstadoGestor
			4, // coMunicipioGestor
			'DT_ATUALIZACAO_TEST', // dtAtualizacao
			'NO_URL_TEST', // noUrl
			'NU_LATITUDE_TEST', // nuLatitude
			'NU_LONGITUDE_TEST', // nuLongitude
			'TP_ESTAB_TEST', // tpEstabSempreAberto
			'ST_CONEXAO_TEST', // stConexaoInternet
			'TP_GESTAO_TEST', // tpGestao
			'TP_LOGRADOURO_TEST', // tpLogradouroCarefy
			true, // isUnimed
		);

		fakeCity = { id: null, name: null, state: null };
	});

	describe('insertHospital', () => {
		it('deve inserir um hospital e retornar o id inserido', async () => {
			const createdHospitalEntity = { ...fakeHospital };
			(hospitalRepository.create as jest.Mock).mockReturnValue(
				createdHospitalEntity,
			);
			(hospitalRepository.insert as jest.Mock).mockResolvedValue({
				identifiers: [{ id: 100 }],
			});
			(hospitalCompanyRepository.save as jest.Mock).mockResolvedValue({
				id: 100,
				hospital: { id: 100 },
			});
			const result = await gateway.insertHospitalCompany(fakeHospital, 10);

			expect(hospitalRepository.create).toHaveBeenCalledWith({
				id: fakeHospital.id,
				coUnidade: fakeHospital.coUnidade,
				coCnes: fakeHospital.coCnes,
				nuCnpjMantenedora: fakeHospital.nuCnpjMantenedora,
				noRazaoSocial: fakeHospital.noRazaoSocial,
				noFantasia: fakeHospital.noFantasia,
				noLogradouro: fakeHospital.noLogradouro,
				nuEndereco: fakeHospital.nuEndereco,
				noComplemento: fakeHospital.noComplemento,
				noBairro: fakeHospital.noBairro,
				coCep: fakeHospital.coCep,
				nuTelefone: fakeHospital.nuTelefone,
				nuFax: fakeHospital.nuFax,
				noEmail: fakeHospital.noEmail,
				nuCpf: fakeHospital.nuCpf,
				nuCnpj: fakeHospital.nuCnpj,
				coAtividade: fakeHospital.coAtividade,
				coClientela: fakeHospital.coClientela,
				tpUnidade: fakeHospital.tpUnidade,
				coTurnoAtendimento: fakeHospital.coTurnoAtendimento,
				coEstadoGestor: fakeHospital.coEstadoGestor,
				coMunicipioGestor: fakeHospital.coMunicipioGestor,
				dtAtualizacao: fakeHospital.dtAtualizacao,
				noUrl: fakeHospital.noUrl,
				nuLatitude: fakeHospital.nuLatitude,
				nuLongitude: fakeHospital.nuLongitude,
				tpEstabSempreAberto: fakeHospital.tpEstabSempreAberto,
				stConexaoInternet: fakeHospital.stConexaoInternet,
				tpGestao: fakeHospital.tpGestao,
				tpLogradouroCarefy: fakeHospital.tpLogradouroCarefy,
				isUnimed: fakeHospital.isUnimed,
			});
			expect(hospitalCompanyRepository.save).toHaveBeenCalled();
			expect(result.hospitalCompanyId).toBe(100);
			expect(result.hospitalId).toBe(1);
		});
	});

	describe('insertHospitalCompany', () => {
		it('deve inserir um hospitalCompany e retornar o id inserido, inserindo o hospital primeiro se não houver id', async () => {
			const hospitalWithoutId = { ...fakeHospital, id: null } as Hospital;
			const city = { ...fakeCity } as CityEntity;

			const createdHospitalEntity = { ...hospitalWithoutId };
			(hospitalRepository.create as jest.Mock).mockReturnValue(
				createdHospitalEntity,
			);
			(hospitalRepository.insert as jest.Mock).mockResolvedValue({
				identifiers: [{ id: 200 }],
			});

			(cityRepository.findOneBy as jest.Mock).mockReturnValue(city);

			const fakeHospitalCompanyEntity: DeepPartial<HospitalsCompanyEntity> = {
				id: hospitalWithoutId.id,
				companyId: 10,
				hospital: createdHospitalEntity,
				created: hospitalWithoutId.created,
				updated: hospitalWithoutId.updated,
				name: hospitalWithoutId.noFantasia,
				name2: hospitalWithoutId.noFantasia,
				name3: hospitalWithoutId.noFantasia,
				tel: hospitalWithoutId.nuTelefone,
				tel2: hospitalWithoutId.nuTelefone,
				dscTpLogra: hospitalWithoutId.noLogradouro,
				endereco: hospitalWithoutId.noLogradouro,
				numero: hospitalWithoutId.nuEndereco,
				bairro: hospitalWithoutId.noBairro,
				controle: null,
				controle2: null,
				cityId: city.id,
				stateId: city.state,
				codigoInterno: hospitalWithoutId.coUnidade,
				handlePrestador: hospitalWithoutId.nuCnpj ?? hospitalWithoutId.nuCpf,
				codDrg: null,
			};
			(hospitalCompanyRepository.create as jest.Mock).mockReturnValue(
				fakeHospitalCompanyEntity,
			);
			(hospitalCompanyRepository.save as jest.Mock).mockResolvedValue({
				id: 100,
				hospital: { id: 100 },
			});

			const result = await gateway.insertHospitalCompany(hospitalWithoutId, 10);

			expect(hospitalRepository.insert).toHaveBeenCalled();
			expect(hospitalCompanyRepository.create).toHaveBeenCalledWith({
				id: createdHospitalEntity.id,
				companyId: 10,
				hospital: createdHospitalEntity,
				created: hospitalWithoutId.created,
				updated: hospitalWithoutId.updated,
				name: hospitalWithoutId.noFantasia,
				name2: hospitalWithoutId.noFantasia,
				name3: hospitalWithoutId.noFantasia,
				tel: hospitalWithoutId.nuTelefone,
				tel2: hospitalWithoutId.nuTelefone,
				dscTpLogra: hospitalWithoutId.noLogradouro,
				endereco: hospitalWithoutId.noLogradouro,
				numero: hospitalWithoutId.nuEndereco,
				bairro: hospitalWithoutId.noBairro,
				cityId: city.id,
				stateId: city.state,
				codigoInterno: hospitalWithoutId.coUnidade,
				handlePrestador: hospitalWithoutId.nuCnpj ?? hospitalWithoutId.nuCpf,
			});
			expect(hospitalCompanyRepository.save).toHaveBeenCalledWith(
				fakeHospitalCompanyEntity,
			);
			expect(result.hospitalCompanyId).toBe(100);
			expect(result.hospitalId).toBe(200);
		});
	});

	describe('findCityAndStateByNoMunicipio', () => {
		it('deve retornar null quando não encontrar o município', async () => {
			jest
				.spyOn(cnesMunicipioOldRepository, 'findOneBy')
				.mockResolvedValue(null);

			const result = await gateway.findCityAndStateByNoMunicipio(123);
			expect(result).toBeNull();
			expect(cnesMunicipioOldRepository.findOneBy).toHaveBeenCalledWith({
				coMunicipio: 123,
			});
		});

		it('deve retornar null quando o município não tiver nome', async () => {
			jest.spyOn(cnesMunicipioOldRepository, 'findOneBy').mockResolvedValue({
				coMunicipio: 123,
				noMunicipio: null,
			});

			const result = await gateway.findCityAndStateByNoMunicipio(123);
			expect(result).toBeNull();
		});

		it('deve retornar a cidade quando encontrada', async () => {
			const mockMunicipio = {
				coMunicipio: 123,
				noMunicipio: 'São Paulo',
			};

			const mockCity = {
				id: 1,
				name: 'São Paulo',
				state: 1,
			} as CityEntity;

			jest
				.spyOn(cnesMunicipioOldRepository, 'findOneBy')
				.mockResolvedValue(mockMunicipio);
			jest.spyOn(cityRepository, 'findOneBy').mockResolvedValue(mockCity);

			const result = await gateway.findCityAndStateByNoMunicipio(123);
			expect(result).toEqual(mockCity);
			expect(cnesMunicipioOldRepository.findOneBy).toHaveBeenCalledWith({
				coMunicipio: 123,
			});
			expect(cityRepository.findOneBy).toHaveBeenCalledWith({
				name: 'São Paulo',
			});
		});

		it('deve retornar null quando a cidade não for encontrada', async () => {
			const mockMunicipio = {
				coMunicipio: 123,
				noMunicipio: 'Cidade Inexistente',
			};

			jest
				.spyOn(cnesMunicipioOldRepository, 'findOneBy')
				.mockResolvedValue(mockMunicipio);
			jest.spyOn(cityRepository, 'findOneBy').mockResolvedValue(null);

			const result = await gateway.findCityAndStateByNoMunicipio(123);
			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			jest
				.spyOn(cnesMunicipioOldRepository, 'findOneBy')
				.mockRejectedValue(new Error('DB Error'));

			await expect(gateway.findCityAndStateByNoMunicipio(123)).rejects.toThrow(
				'DB Error',
			);
		});
	});
});
