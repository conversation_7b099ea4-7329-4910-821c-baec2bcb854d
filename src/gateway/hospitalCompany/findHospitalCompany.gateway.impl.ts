import { FindHospitalCompanyGateway } from 'src/core/application/gateway/hospital/findHospitalCompany.gateway';
import { ILike, Repository } from 'typeorm';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';

export class FindHospitalCompanyGatewayImpl
	implements FindHospitalCompanyGateway
{
	constructor(
		private readonly hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
	) {}

	public async findHospitalCompanyByName(
		name: string,
		companyId: number,
	): Promise<HospitalsCompany> {
		const result = await this.hospitalCompanyRepository
			.createQueryBuilder('hc')
			.leftJoinAndSelect('hc.hospital', 'hospital')
			.where('hc.enabled = :enabled', { enabled: 1 })
			.andWhere('hc.company_id = :companyId', { companyId })
			.andWhere('(hc.name = :name OR hc.name2 = :name OR hc.name3 = :name)', {
				name,
			})
			.getOne();
		if (!result) {
			return null;
		}
		return HospitalMapper.toHospitalCompanyDomain(result);
	}

	async searchHospitalCompany(
		name: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]> {
		const result = await this.hospitalCompanyRepository.find({
			where: {
				name: ILike(`%${name || ''}%`),
				companyId,
				enabled: 1,
			},
			take: limit,
		});

		if (!result) return null;

		return result.map((hospital) =>
			HospitalMapper.toHospitalCompanyDomain(hospital),
		);
	}

	async searchUnimed(
		name: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]> {
		const result = await this.hospitalCompanyRepository.find({
			where: {
				name: ILike(`%${name || ''}%`),
				companyId,
				hospital: {
					isUnimed: true,
				},
				enabled: 1,
			},
			take: limit,
		});

		if (!result) return null;

		return result.map((hospital) =>
			HospitalMapper.toHospitalCompanyDomain(hospital),
		);
	}
}
