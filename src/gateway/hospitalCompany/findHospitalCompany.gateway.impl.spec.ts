import { Repository } from 'typeorm';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';
import { FindHospitalCompanyGatewayImpl } from './findHospitalCompany.gateway.impl';
import { HospitalMapper } from 'src/shared/mapper/hospital/HospitalMapper';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';

const createQueryBuilderMock = {
	leftJoinAndSelect: jest.fn().mockReturnThis(),
	where: jest.fn().mockReturnThis(),
	andWhere: jest.fn().mockReturnThis(),
	getOne: jest.fn(),
};

describe('FindHospitalCompanyGatewayImpl', () => {
	let gateway: FindHospitalCompanyGatewayImpl;
	let hospitalCompanyRepository: Repository<HospitalsCompanyEntity>;

	beforeEach(() => {
		hospitalCompanyRepository = {
			createQueryBuilder: jest.fn().mockReturnValue(createQueryBuilderMock),
			find: jest.fn(),
		} as unknown as Repository<HospitalsCompanyEntity>;

		gateway = new FindHospitalCompanyGatewayImpl(hospitalCompanyRepository);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('deve buscar hospitais pelo nome e retornar o domínio mapeado corretamente', async () => {
		// Arrange
		const name = 'Hospital Teste';
		const companyId = 1;

		const fakeEntity: HospitalsCompanyEntity = {
			id: 1,
			valorMedioDia: 100,
			email: '<EMAIL>',
			enabled: 1,
			tipo: 'Rede Credenciada',
			valorAlertaMes: 200,
			enfClinica: 1,
			enfCirurgica: 2,
			enfObstetrica: 3,
			enfPediatrica: 4,
			enfPsiquiatrica: 5,
			utiaClinica: 6,
			utiaCirurgica: 7,
			utiaObstetrica: 8,
			utiaPediatrica: 9,
			utiaPsiquiatrica: 10,
			utipClinica: 11,
			utipCirurgica: 12,
			utipObstetrica: 13,
			utipPediatrica: 14,
			utipPsiquiatrica: 15,
			utinClinica: 16,
			utinCirurgica: 17,
			utinObstetrica: 18,
			utinPediatrica: 19,
			utinPsiquiatrica: 20,
			tsiClinica: 21,
			tsiCirurgica: 22,
			tsiObstetrica: 23,
			tsiPediatrica: 24,
			tsiPsiquiatrica: 25,
			ucoClinica: 26,
			ucoCirurgica: 27,
			ucoObstetrica: 28,
			ucoPediatrica: 29,
			ucoPsiquiatrica: 30,
			uceClinica: 31,
			uceCirurgica: 32,
			uceObstetrica: 33,
			ucePediatrica: 34,
			ucePsiquiatrica: 35,
			bernClinica: 36,
			bernCirurgica: 37,
			bernObstetrica: 38,
			bernPediatrica: 39,
			bernPsiquiatrica: 40,
			berppClinica: 41,
			berppCirurgica: 42,
			berppObstetrica: 43,
			berppPediatrica: 44,
			berppPsiquiatrica: 45,
			apartClinica: 46,
			apartCirurgica: 47,
			apartObstetrica: 48,
			apartPediatrica: 49,
			domiTransicao: 50,
			domiMulti: 51,
			domiMedicamento: 52,
			domiCurativo: 53,
			domi6h: 54,
			domi12h: 55,
			domi24AV: 56,
			domi24VM: 57,
			onedayPsiquiatrica: 58,
			onedayPediatrica: 59,
			onedayObstetrica: 60,
			onedayCirurgica: 61,
			onedayClinica: 62,
			apartPsiquiatrica: 63,
			permanenciaEnf: 64,
			permanenciaUtia: 65,
			utiPsiquiatrica: 66,
			utiPediatrica: 67,
			utiObstetrica: 68,
			utiCirurgica: 69,
			utiClinica: 70,
			utiiPsiquiatrica: 71,
			utiiPediatrica: 72,
			utiiObstetrica: 73,
			utiiCirurgica: 74,
			utiiClinica: 75,
			utiniPsiquiatrica: 76,
			utiniPediatrica: 77,
			utiniObstetrica: 78,
			utiniCirurgica: 79,
			utiniClinica: 80,
			ucoiPsiquiatrica: 81,
			ucoiPediatrica: 82,
			ucoiObstetrica: 83,
			ucoiCirurgica: 84,
			ucoiClinica: 85,
			semiutiiPsiquiatrica: 86,
			semiutiiPediatrica: 87,
			semiutiiObstetrica: 88,
			ssemiutiiCirurgica: 89,
			semiutiiClinica: 90,
			semiutiPsiquiatrica: 91,
			semiutiPediatrica: 92,
			semiutiObstetrica: 93,
			semiutiCirurgica: 94,
			semiutiClinica: 95,
			utmoPsiquiatrica: 96,
			utmoPediatrica: 97,
			utmoObstetrica: 98,
			utmoCirurgica: 99,
			utmoClinica: 100,
			ucgPsiquiatrica: 101,
			ucgPediatrica: 102,
			ucgObstetrica: 103,
			ucgCirurgica: 104,
			ucgClinica: 105,
			ucgiPsiquiatrica: 106,
			ucgiPediatrica: 107,
			ucgiObstetrica: 108,
			ucgiCirurgica: 109,
			ucgiClinica: 110,
			apartiPsiquiatrica: 111,
			apartiPediatrica: 112,
			apartiObstetrica: 113,
			apartiCirurgica: 114,
			apartiClinica: 115,
			complexity: 'Media',
			homecareEquipamento: 116,
			homecareMaterial: 117,
			assistenciaDomiciliar: 118,
			internacaoDomiciliar: 119,
			homecareFisioterapia: 120,
			homecareInternado24: 121,
			homecareAntibiotic: 122,
			companyId,
			hospital: undefined,
			created: new Date('2022-01-01T00:00:00Z'),
			updated: new Date('2022-01-02T00:00:00Z'),
			name,
			name2: 'Teste Name2',
			name3: 'Teste Name3',
			tel: '*********',
			tel2: '*********',
			dscTpLogra: 'Rua',
			endereco: 'Endereço Teste',
			numero: '123',
			bairro: 'Bairro Teste',
			estado: 'Estado Teste',
			cidade: 'Cidade Teste',
			regiao: 'Região Teste',
			regiaoNova: 'Nova Região Teste',
			controle: 'Controle Teste',
			controle2: 'Controle2 Teste',
			cityId: 10,
			stateId: 20,
			codigoInterno: 'ABC123',
			handlePrestador: 'handleTeste',
			codDrg: 999,
			autorizacoes: [],
		};

		createQueryBuilderMock.getOne.mockResolvedValue(fakeEntity);

		// Act
		const result = await gateway.findHospitalCompanyByName(name, companyId);

		// Assert
		expect(hospitalCompanyRepository.createQueryBuilder).toHaveBeenCalledWith(
			'hc',
		);
		expect(createQueryBuilderMock.where).toHaveBeenCalledWith(
			'hc.enabled = :enabled',
			{ enabled: 1 },
		);
		expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
			'hc.company_id = :companyId',
			{ companyId },
		);
		expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
			'(hc.name = :name OR hc.name2 = :name OR hc.name3 = :name)',
			{ name },
		);
		expect(createQueryBuilderMock.getOne).toHaveBeenCalled();

		expect(result).toMatchObject({
			id: fakeEntity.id,
		});
	});

	it('deve buscar hospitais pelo nome e retornar null ao nao encontrar hospital', async () => {
		// Arrange
		const name = 'Hospital Teste';
		const companyId = 1;

		createQueryBuilderMock.getOne.mockResolvedValue(null);

		// Act
		const result = await gateway.findHospitalCompanyByName(name, companyId);

		// Assert
		expect(hospitalCompanyRepository.createQueryBuilder).toHaveBeenCalledWith(
			'hc',
		);
		expect(createQueryBuilderMock.where).toHaveBeenCalledWith(
			'hc.enabled = :enabled',
			{ enabled: 1 },
		);
		expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
			'hc.company_id = :companyId',
			{ companyId },
		);
		expect(createQueryBuilderMock.andWhere).toHaveBeenCalledWith(
			'(hc.name = :name OR hc.name2 = :name OR hc.name3 = :name)',
			{ name },
		);
		expect(createQueryBuilderMock.getOne).toHaveBeenCalled();

		expect(result).toBe(null);
	});

	describe('searchHospitalCompany', () => {
		it('deve buscar hospitais pelo nome e retornar a lista de domínios mapeados', async () => {
			// Arrange
			const name = 'Hospital Teste';
			const companyId = 1;
			const limit = 10;

			const fakeEntities: HospitalsCompanyEntity[] = [
				{
					id: 1,
					name: 'Hospital Teste 1',
					companyId,
					enabled: 1,
					hospital: { isUnimed: false },
				} as HospitalsCompanyEntity,
				{
					id: 2,
					name: 'Hospital Teste 2',
					companyId,
					enabled: 1,
					hospital: { isUnimed: false },
				} as HospitalsCompanyEntity,
			];

			const fakeDomains = [
				{ id: 1, name: 'Hospital Teste 1' },
				{ id: 2, name: 'Hospital Teste 2' },
			] as HospitalsCompany[];

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockResolvedValue(fakeEntities);
			jest
				.spyOn(HospitalMapper, 'toHospitalCompanyDomain')
				.mockReturnValueOnce(fakeDomains[0])
				.mockReturnValueOnce(fakeDomains[1]);

			// Act
			const result = await gateway.searchHospitalCompany(
				name,
				companyId,
				limit,
			);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(2);
			expect(result[0].id).toBe(fakeDomains[0].id);
			expect(result[1].id).toBe(fakeDomains[1].id);
		});

		it('deve buscar hospitais e retornar a lista de domínios mapeados mesmo se o nome for undefined', async () => {
			// Arrange
			const name: string = undefined;
			const companyId = 1;
			const limit = 10;

			const fakeEntities: HospitalsCompanyEntity[] = [
				{
					id: 1,
					name: 'Hospital Teste 1',
					companyId,
					enabled: 1,
					hospital: { isUnimed: false },
				} as HospitalsCompanyEntity,
				{
					id: 2,
					name: 'Hospital Teste 2',
					companyId,
					enabled: 1,
					hospital: { isUnimed: false },
				} as HospitalsCompanyEntity,
			];

			const fakeDomains = [
				{ id: 1, name: 'Hospital Teste 1' },
				{ id: 2, name: 'Hospital Teste 2' },
			] as HospitalsCompany[];

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockResolvedValue(fakeEntities);
			jest
				.spyOn(HospitalMapper, 'toHospitalCompanyDomain')
				.mockReturnValueOnce(fakeDomains[0])
				.mockReturnValueOnce(fakeDomains[1]);

			// Act
			const result = await gateway.searchHospitalCompany(
				name,
				companyId,
				limit,
			);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(2);
			expect(result[0].id).toBe(fakeDomains[0].id);
			expect(result[1].id).toBe(fakeDomains[1].id);
		});
		it('deve retornar uma lista vazia quando não encontrar hospitais', async () => {
			// Arrange
			const name = 'Hospital Inexistente';
			const companyId = 1;
			const limit = 10;

			jest.spyOn(hospitalCompanyRepository, 'find').mockResolvedValue([]);

			// Act
			const result = await gateway.searchHospitalCompany(
				name,
				companyId,
				limit,
			);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(0);
		});

		it('deve retornar null quando o resultado for null', async () => {
			// Arrange
			const name = 'Hospital Teste';
			const companyId = 1;
			const limit = 10;

			jest.spyOn(hospitalCompanyRepository, 'find').mockResolvedValue(null);

			// Act
			const result = await gateway.searchHospitalCompany(
				name,
				companyId,
				limit,
			);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			// Arrange
			const name = 'Hospital Teste';
			const companyId = 1;
			const limit = 10;

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockRejectedValue(new Error('DB Error'));

			// Act & Assert
			await expect(
				gateway.searchHospitalCompany(name, companyId, limit),
			).rejects.toThrow('DB Error');

			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: 1,
				},
				take: limit,
			});
		});
	});

	describe('searchUnimed', () => {
		it('deve buscar hospitais Unimed pelo nome e retornar a lista de domínios mapeados', async () => {
			// Arrange
			const name = 'Unimed';
			const companyId = 1;
			const limit = 10;

			const fakeEntities: HospitalsCompanyEntity[] = [
				{
					id: 1,
					name: 'Unimed 1',
					companyId,
					enabled: 1,
					hospital: { isUnimed: true },
				} as HospitalsCompanyEntity,
				{
					id: 2,
					name: 'Unimed 2',
					companyId,
					enabled: 1,
					hospital: { isUnimed: true },
				} as HospitalsCompanyEntity,
			];

			const fakeDomains = [
				{ id: 1, name: 'Unimed 1' },
				{ id: 2, name: 'Unimed 2' },
			] as HospitalsCompany[];

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockResolvedValue(fakeEntities);
			jest
				.spyOn(HospitalMapper, 'toHospitalCompanyDomain')
				.mockReturnValueOnce(fakeDomains[0])
				.mockReturnValueOnce(fakeDomains[1]);

			// Act
			const result = await gateway.searchUnimed(name, companyId, limit);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					hospital: {
						isUnimed: true,
					},
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(2);
			expect(result[0].id).toBe(fakeDomains[0].id);
			expect(result[1].id).toBe(fakeDomains[1].id);
		});

		it('deve buscar hospitais Unimed e retornar a lista de domínios mapeados mesmo se o nome for undefined', async () => {
			// Arrange
			const name: string = undefined;
			const companyId = 1;
			const limit = 10;

			const fakeEntities: HospitalsCompanyEntity[] = [
				{
					id: 1,
					name: 'Unimed 1',
					companyId,
					enabled: 1,
					hospital: { isUnimed: true },
				} as HospitalsCompanyEntity,
				{
					id: 2,
					name: 'Unimed 2',
					companyId,
					enabled: 1,
					hospital: { isUnimed: true },
				} as HospitalsCompanyEntity,
			];

			const fakeDomains = [
				{ id: 1, name: 'Unimed 1' },
				{ id: 2, name: 'Unimed 2' },
			] as HospitalsCompany[];

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockResolvedValue(fakeEntities);
			jest
				.spyOn(HospitalMapper, 'toHospitalCompanyDomain')
				.mockReturnValueOnce(fakeDomains[0])
				.mockReturnValueOnce(fakeDomains[1]);

			// Act
			const result = await gateway.searchUnimed(name, companyId, limit);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					hospital: {
						isUnimed: true,
					},
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(2);
			expect(result[0].id).toBe(fakeDomains[0].id);
			expect(result[1].id).toBe(fakeDomains[1].id);
		});

		it('deve retornar uma lista vazia quando não encontrar hospitais Unimed', async () => {
			// Arrange
			const name = 'Unimed Inexistente';
			const companyId = 1;
			const limit = 10;

			jest.spyOn(hospitalCompanyRepository, 'find').mockResolvedValue([]);

			// Act
			const result = await gateway.searchUnimed(name, companyId, limit);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					hospital: {
						isUnimed: true,
					},
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(0);
		});

		it('deve retornar null quando o resultado for null', async () => {
			// Arrange
			const name = 'Unimed';
			const companyId = 1;
			const limit = 10;

			jest.spyOn(hospitalCompanyRepository, 'find').mockResolvedValue(null);

			// Act
			const result = await gateway.searchUnimed(name, companyId, limit);

			// Assert
			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					hospital: {
						isUnimed: true,
					},
					enabled: 1,
				},
				take: limit,
			});

			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			// Arrange
			const name = 'Unimed';
			const companyId = 1;
			const limit = 10;

			jest
				.spyOn(hospitalCompanyRepository, 'find')
				.mockRejectedValue(new Error('DB Error'));

			// Act & Assert
			await expect(
				gateway.searchUnimed(name, companyId, limit),
			).rejects.toThrow('DB Error');

			expect(hospitalCompanyRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					hospital: {
						isUnimed: true,
					},
					enabled: 1,
				},
				take: limit,
			});
		});
	});
});
