import { InsertHospitalGateway } from 'src/core/application/gateway/hospital/insertHospitalGateway';
import { DeepPartial, Repository } from 'typeorm';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';
import { HospitalEntity } from '../entities/hospital.entity';
import { Hospital } from 'src/core/domain/Hospital';
import { CnesMunicipioOldEntity } from '../entities/cnesMunicipioOld.entity';
import { CityEntity } from '../entities/city.entity';

export class InsertHospitalGatewayImpl implements InsertHospitalGateway {
	constructor(
		private readonly hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
		private readonly hospitalRepository: Repository<HospitalEntity>,
		private readonly cnesMunicipioOldRepository: Repository<CnesMunicipioOldEntity>,
		private readonly cityRepository: Repository<CityEntity>,
	) {}

	public async insertHospitalCompany(
		hospital: Hospital,
		companyId: number,
	): Promise<{ hospitalId: number; hospitalCompanyId: number }> {
		let hospId = hospital.id;
		if (!hospital.id) {
			hospId = await this.insertHospital(hospital);
		}
		hospital.id = hospId;
		const hospitalEntity = await this.createHospitalCompanyEntity(
			hospital,
			companyId,
		);
		const result = await this.hospitalCompanyRepository.save(hospitalEntity);

		return { hospitalId: hospId, hospitalCompanyId: result.id };
	}

	public async insertHospital(hospital: Hospital): Promise<number> {
		const hospitalEntity = this.createHospitalEntity(hospital);
		return (await this.hospitalRepository.insert(hospitalEntity)).identifiers[0]
			.id;
	}

	/* istanbul ignore next */
	private async createHospitalCompanyEntity(
		hospital: Hospital,
		companyId: number,
	): Promise<HospitalsCompanyEntity> {
		const city = await this.findCityAndStateByNoMunicipio(
			hospital.coMunicipioGestor,
		);
		const hospitalEntity: DeepPartial<HospitalsCompanyEntity> = {
			id: null,
			companyId: companyId,
			hospital: this.createHospitalEntity(hospital),
			created: hospital.created,
			updated: hospital.updated,
			name: hospital.noFantasia,
			name2: hospital.noFantasia,
			name3: hospital.noFantasia,
			tel: hospital.nuTelefone,
			tel2: hospital.nuTelefone,
			dscTpLogra: hospital.noLogradouro,
			endereco: hospital.noLogradouro,
			numero: hospital.nuEndereco,
			bairro: hospital.noBairro,
			cityId: city ? city.id : null,
			stateId: city ? city.state : null,
			codigoInterno: hospital.coUnidade,
			handlePrestador: hospital.nuCnpj ?? hospital.nuCpf,
		};
		return this.hospitalCompanyRepository.create(hospitalEntity);
	}

	private createHospitalEntity(hospital: Hospital): HospitalEntity {
		return this.hospitalRepository.create({
			id: hospital.id,
			coUnidade: hospital.coUnidade,
			coCnes: hospital.coCnes,
			nuCnpjMantenedora: hospital.nuCnpjMantenedora,
			noRazaoSocial: hospital.noRazaoSocial,
			noFantasia: hospital.noFantasia,
			noLogradouro: hospital.noLogradouro,
			nuEndereco: hospital.nuEndereco,
			noComplemento: hospital.noComplemento,
			noBairro: hospital.noBairro,
			coCep: hospital.coCep,
			nuTelefone: hospital.nuTelefone,
			nuFax: hospital.nuFax,
			noEmail: hospital.noEmail,
			nuCpf: hospital.nuCpf,
			nuCnpj: hospital.nuCnpj,
			coAtividade: hospital.coAtividade,
			coClientela: hospital.coClientela,
			tpUnidade: hospital.tpUnidade,
			coTurnoAtendimento: hospital.coTurnoAtendimento,
			coEstadoGestor: hospital.coEstadoGestor,
			coMunicipioGestor: hospital.coMunicipioGestor,
			dtAtualizacao: hospital.dtAtualizacao,
			noUrl: hospital.noUrl,
			nuLatitude: hospital.nuLatitude,
			nuLongitude: hospital.nuLongitude,
			tpEstabSempreAberto: hospital.tpEstabSempreAberto,
			stConexaoInternet: hospital.stConexaoInternet,
			tpGestao: hospital.tpGestao,
			tpLogradouroCarefy: hospital.tpLogradouroCarefy,
			isUnimed: hospital.isUnimed,
		});
	}

	public async findCityAndStateByNoMunicipio(
		coMunicipioGestor: number,
	): Promise<CityEntity> {
		const municipioOld = await this.cnesMunicipioOldRepository.findOneBy({
			coMunicipio: coMunicipioGestor,
		});

		if (!municipioOld || !municipioOld.noMunicipio) return null;

		return await this.cityRepository.findOneBy({
			name: municipioOld.noMunicipio,
		});
	}
}
