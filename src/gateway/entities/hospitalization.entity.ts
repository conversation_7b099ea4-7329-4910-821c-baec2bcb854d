import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyToOne,
	JoinColumn,
	OneToMany,
	Relation,
} from 'typeorm';
import { PatientEntity } from './patient.entity';
import { BedHospitalEntity } from './bedHospital.entity';

@Entity('patient_way')
export class HospitalizationEntity {
	@PrimaryGeneratedColumn({ name: 'id', type: 'int', unsigned: true })
	id: number;

	@Column({ name: 'created', type: 'datetime' })
	created: Date;

	@Column({ name: 'updated', type: 'timestamp', nullable: true })
	updated: Date;

	@Column({ name: 'admission_in', type: 'datetime' })
	admissionIn: Date;

	@Column({ name: 'admission_out', type: 'datetime', nullable: true })
	admissionOut?: Date;

	@Column({ name: 'admission_out_created', type: 'timestamp', nullable: true })
	admissionOutCreated?: Date;

	@Column({ name: 'admission_out_user_id', type: 'int', nullable: true })
	admissionOutUserId?: number;

	@Column({
		name: 'admission_out_created_first_time',
		type: 'datetime',
		nullable: true,
	})
	admissionOutCreatedFirstTime?: Date;

	@Column({ name: 'is_admission_out_censo', type: 'int', default: 0 })
	isAdmissionOutCenso: number;

	@ManyToOne(() => PatientEntity, { nullable: true })
	@JoinColumn({ name: 'patient_id' })
	patient?: PatientEntity;

	@Column({ name: 'out_pat', type: 'varchar', length: 200, nullable: true })
	outPat?: string;

	@Column({ name: 'alta_prevista', type: 'timestamp', nullable: true })
	altaPrevista?: Date;

	@Column({
		name: 'alta_prevista_IA',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	altaPrevistaIA?: string;

	@Column({
		name: 'alta_prevista_IA_min',
		type: 'decimal',
		precision: 6,
		scale: 2,
		nullable: true,
	})
	altaPrevistaIAMin?: number;

	@Column({
		name: 'alta_prevista_IA_max',
		type: 'decimal',
		precision: 6,
		scale: 2,
		nullable: true,
	})
	altaPrevistaIAMax?: number;

	@Column({ name: 'alta_prevista_MI4U', type: 'int', nullable: true })
	altaPrevistaMI4U?: number;

	@Column({ name: 'obs', type: 'text', nullable: true })
	obs?: string;

	@Column({
		name: 'nomeMedicoResponsavel',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	nomeMedicoResponsavel?: string;

	@Column({
		name: 'contatoMedicoResponsavel',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	contatoMedicoResponsavel?: string;

	@Column({
		name: 'crmMedicoResponsavel',
		type: 'varchar',
		length: 20,
		nullable: true,
	})
	crmMedicoResponsavel?: string;

	@Column({
		name: 'ufcrmMedicoResponsavel',
		type: 'varchar',
		length: 2,
		nullable: true,
	})
	ufcrmMedicoResponsavel?: string;

	@Column({
		name: 'especialidadeMedicoResponsavel',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	especialidadeMedicoResponsavel?: string;

	@Column({ name: 'numeroGuia', type: 'varchar', length: 100, default: '' })
	numeroGuia: string;

	@Column({ name: 'user_id', type: 'int', unsigned: true, nullable: true })
	userId?: number;

	@Column({ name: 'is_eventoAdverso', type: 'tinyint', nullable: true })
	isEventoAdverso?: number;

	@Column({ name: 'eventoAdversoObs', type: 'text', nullable: true })
	eventoAdversoObs?: string;

	@Column({ name: 'is_internacaoDiaAnterior', type: 'tinyint', nullable: true })
	isInternacaoDiaAnterior?: number;

	@Column({ name: 'carater', type: 'varchar', length: 200, default: '' })
	carater: string;

	@Column({ name: 'caraterTipo', type: 'varchar', length: 200, default: '' })
	caraterTipo: string;

	@Column({ name: 'has_prenatal', type: 'tinyint', nullable: true })
	hasPrenatal?: number;

	@Column({ name: 'is_pregnant', type: 'tinyint', nullable: true })
	isPregnant?: number;

	@Column({ name: 'is_puerperium', type: 'tinyint', nullable: true })
	isPuerperium?: number;

	@Column({ name: 'is_newborn', type: 'tinyint', default: 0 })
	isNewborn: number;

	@Column({ name: 'enabled', type: 'tinyint', default: 1 })
	enabled: number;

	@Column({ name: 'is_censo', type: 'int', default: 0 })
	isCenso: number;

	@Column({ name: 'is_bill', type: 'tinyint', default: 0 })
	isBill: number;

	@Column({ name: 'numeroSenha', type: 'varchar', length: 40, default: '' })
	numeroSenha: string;

	@Column({ name: 'clinical_history', type: 'text', nullable: true })
	clinicalHistory?: string;

	@Column({ name: 'dataEmissaoGuia', type: 'timestamp', nullable: true })
	dataEmissaoGuia?: Date;

	@Column({ name: 'supervisor_id', type: 'int', nullable: true })
	supervisorId?: number;

	@Column({
		name: 'regime',
		type: 'varchar',
		length: 100,
		default: 'Hospitalar',
	})
	regime: string;

	@Column({ name: 'palliative_care', type: 'tinyint', nullable: true })
	palliativeCare?: number;

	@Column({ name: 'codigoMedicoResponsavel', type: 'int', nullable: true })
	codigoMedicoResponsavel?: number;

	@Column({
		name: 'conselhoMedicoResponsavel',
		type: 'varchar',
		length: 15,
		nullable: true,
	})
	conselhoMedicoResponsavel?: string;

	@Column({ name: 'dataHoraSolicitacao', type: 'datetime', nullable: true })
	dataHoraSolicitacao?: Date;

	@Column({
		name: 'codIntegration',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	codIntegration?: string;

	@Column({
		name: 'data_authorization_guide',
		type: 'timestamp',
		nullable: true,
	})
	dataAuthorizationGuide?: Date;

	@Column({
		name: 'real_cost',
		type: 'decimal',
		precision: 12,
		scale: 2,
		nullable: true,
	})
	realCost?: number;

	@Column({
		name: 'codigoSituacaoTiss',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	codigoSituacaoTiss?: string;

	@Column({
		name: 'descricaoSituacaoTiss',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	descricaoSituacaoTiss?: string;

	@Column({ name: 'longa_decisao', type: 'tinyint', nullable: true })
	longaDecisao?: number;

	@Column({ name: 'prob', type: 'float', nullable: true })
	prob?: number;

	@Column({ name: 'is_editable_real_cost', type: 'tinyint', default: 0 })
	isEditableRealCost: number;

	@Column({ name: 'curta_permanencia', type: 'tinyint', default: 0 })
	curtaPermanencia: number;

	@Column({
		name: 'out_doctor_name',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	outDoctorName?: string;

	@Column({
		name: 'out_doctor_uf_crm',
		type: 'varchar',
		length: 2,
		nullable: true,
	})
	outDoctorUfCrm?: string;

	@Column({
		name: 'out_doctor_crm',
		type: 'varchar',
		length: 10,
		nullable: true,
	})
	outDoctorCrm?: string;

	@Column({
		name: 'out_doctor_speciality',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	outDoctorSpeciality?: string;

	@Column({
		name: 'out_doctor_is_responsible',
		type: 'tinyint',
		nullable: true,
	})
	outDoctorIsResponsible?: number;

	@Column({
		name: 'numeroRegistro',
		type: 'varchar',
		length: 25,
		nullable: true,
	})
	numeroRegistro?: string;

	@Column({ name: 'procedencia', type: 'varchar', length: 1, nullable: true })
	procedencia?: string;

	@Column({ name: 'curta_permanencia_date', type: 'datetime', nullable: true })
	curtaPermanenciaDate?: Date;

	@Column({ name: 'curta_permanencia_user', type: 'int', nullable: true })
	curtaPermanenciaUser?: number;

	@OneToMany(() => BedHospitalEntity, (bed) => bed.hospitalization)
	bedHospitals: Relation<BedHospitalEntity>[];
}
