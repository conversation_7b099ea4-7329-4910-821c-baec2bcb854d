import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyTo<PERSON>ne,
	Jo<PERSON><PERSON><PERSON>umn,
} from 'typeorm';
import { GuideEntity } from './guide.entity';

@Entity('guide_daily')
export class GuideDailyEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true })
	id: number;

	@Column({
		type: 'timestamp',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created: Date;

	@ManyToOne(() => GuideEntity, { nullable: true })
	@JoinColumn({ name: 'guide_id' })
	guide: GuideEntity;

	@Column({ type: 'varchar', length: 200, nullable: true })
	accommodation: string;

	@Column({
		type: 'int',
		unsigned: true,
		nullable: true,
		name: 'qtde_days_requested',
	})
	qtdeDaysRequested: number;

	@Column({
		type: 'int',
		unsigned: true,
		nullable: true,
		name: 'qtde_days_authorized',
	})
	qtdeDaysAuthorized: number;

	@Column({
		type: 'int',
		unsigned: true,
		nullable: true,
		name: 'qtde_days_denied',
	})
	qtdeDaysDenied: number;

	@Column({ type: 'tinyint', width: 1, default: 0, name: 'is_censo' })
	isCenso: number;

	@Column({ type: 'int', unsigned: true, nullable: true, name: 'user_id' })
	userId: number;

	@Column({ type: 'decimal', precision: 8, scale: 2, default: '0.00' })
	price: number;

	@Column({
		type: 'int',
		unsigned: true,
		nullable: true,
		name: 'guide_request_id',
	})
	guideRequest: number;

	@Column({ type: 'tinyint', default: 1 })
	enabled: number;

	@Column({ type: 'timestamp', nullable: true, name: 'date_start' })
	dateStart: Date;

	@Column({ type: 'timestamp', nullable: true, name: 'date_finish' })
	dateFinish: Date;

	@Column({ type: 'int', nullable: true, name: 'qtde_days' })
	qtdeDays: number;

	@Column({
		type: 'int',
		default: 0,
		comment: '0 - old / 1 - transformado / 2 - novo',
	})
	aux: number;

	@Column({ type: 'int', nullable: true, name: 'aux_id' })
	auxId: number;

	@Column({ type: 'varchar', length: 100, nullable: true })
	codIntegration: string;

	@Column({ type: 'timestamp', nullable: true, onUpdate: 'CURRENT_TIMESTAMP' })
	updated: Date;
}
