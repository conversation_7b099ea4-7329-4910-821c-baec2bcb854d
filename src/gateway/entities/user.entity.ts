import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('users')
export class UserEntity {
	@PrimaryGeneratedColumn('increment')
	id: number;

	@Column({ type: 'varchar', length: 100 })
	name: string;

	@Column({ type: 'varchar', length: 200 })
	email: string;

	@Column({ type: 'varchar', length: 100 })
	password: string;

	@Column({ type: 'tinyint' })
	enabled: number;

	@Column({ name: 'company_id', type: 'varchar', length: 200 })
	companyId: string;

	@Column({ name: 'type', type: 'varchar' })
	role: UserRoleEnum;
}
