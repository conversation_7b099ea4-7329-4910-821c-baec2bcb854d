import {
	<PERSON>umn,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>To<PERSON>ne,
	PrimaryGeneratedColumn,
} from 'typeorm';
import { CensoStatusEntity } from './censoStatus.entity';
import { UserEntity } from './user.entity';
import { OperadoraEntity } from './operadora.entity';

@Entity('censo_rework')
export class CensoEntity {
	@PrimaryGeneratedColumn('increment')
	id: string;

	@Column({ name: 'data_criacao', type: 'varchar', length: 100 })
	dataCriacao: Date;

	@Column({ name: 'data_exclusao', type: 'varchar', length: 100 })
	dataExclusao: Date;

	@Column({ name: 'company_id', type: 'int' })
	companyId: string;

	@ManyToOne(() => OperadoraEntity, (operadora) => operadora.id, {
		eager: true,
	})
	@JoinColumn({ name: 'company_id' })
	operadora: OperadoraEntity;

	@Column({ name: 'total_linhas', type: 'int' })
	totalLinhas: number;

	@Column({ name: 'user_id', type: 'int' })
	userId: string;

	@Column({ name: 'diretorio_salvo', type: 'varchar', length: 100 })
	diretorioSalvo: string;

	@Column({ name: 'nome_arquivo', type: 'varchar', length: 255 })
	nomeArquivo: string;

	@Column({ name: 'censo_status_id', type: 'varchar' })
	censoStatusId: string;

	@ManyToOne(() => CensoStatusEntity, (status) => status.censos, {
		eager: true,
	})
	@JoinColumn({ name: 'censo_status_id' })
	status: CensoStatusEntity;

	@ManyToOne(() => UserEntity, (user) => user.id)
	@JoinColumn({ name: 'user_id' })
	user: UserEntity;

	@Column({ name: 'hash_arquivo', type: 'varchar', length: 100 })
	hashArquivo: string;
}
