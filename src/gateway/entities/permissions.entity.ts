import {
	<PERSON>um<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>o<PERSON><PERSON>,
	PrimaryGeneratedColumn,
} from 'typeorm';
import { PermissionFunctionEntity } from './permissionFunction.entity';

@Entity('permissions')
export class PermissionsEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		name: 'id',
	})
	id: number;

	@Column({ name: 'user_id', type: 'int' })
	userId: number;

	@ManyToOne(
		() => PermissionFunctionEntity,
		(functionEntity) => functionEntity.id,
		{
			nullable: true,
			eager: true,
		},
	)
	@JoinColumn({ name: 'permission_function_id' })
	permissionFunction: PermissionFunctionEntity;

	@Column({
		type: 'tinyint',
		name: 'view',
	})
	view: boolean;

	@Column({
		type: 'tinyint',
		name: 'insert',
	})
	insert: boolean;

	@Column({
		type: 'tinyint',
		name: 'edit',
	})
	edit: boolean;

	@Column({
		type: 'tinyint',
		name: 'delete',
	})
	delete: boolean;

	@Column({
		type: 'tinyint',
		name: 'enabled',
	})
	enabled: boolean;
}
