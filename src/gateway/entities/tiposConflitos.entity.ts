import {
	Column,
	Entity,
	OneToMany,
	PrimaryGeneratedColumn,
	Relation,
} from 'typeorm';
import { LinhasConflitadasCensoEntity } from './linhasConflitadasCenso.entity';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

@Entity('tipos_conflitos')
export class TiposConflitosEntity {
	@PrimaryGeneratedColumn('increment')
	id: number;

	@Column({ name: 'descricao', type: 'varchar', length: '255' })
	descricao: string;

	@Column({
		name: 'gravidade',
		type: 'enum',
		enum: ['warning', 'error'],
		default: 'error',
	})
	gravidade: SeverityCensoConflicts;

	@OneToMany(
		() => LinhasConflitadasCensoEntity,
		(linhasConflitadas) => linhasConflitadas.tipoConflito,
	)
	linhasConflitadas: Relation<LinhasConflitadasCensoEntity>[];
}
