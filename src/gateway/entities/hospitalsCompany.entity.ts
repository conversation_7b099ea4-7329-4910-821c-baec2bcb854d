import {
	Column,
	Entity,
	PrimaryGeneratedColumn,
	ManyToOne,
	JoinColumn,
	Relation,
	OneToMany,
} from 'typeorm';
import { HospitalEntity } from './hospital.entity';
import { AutorizacoesEntity } from './autorizacoes.entity';

@Entity('hospitals_companys')
export class HospitalsCompanyEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true })
	id: number;

	@Column({ name: 'company_id', type: 'int', unsigned: true, nullable: true })
	companyId?: number;

	@ManyToOne(() => HospitalEntity, (hospital) => hospital.id, {
		nullable: true,
		eager: true,
	})
	@JoinColumn({ name: 'hospital_id' })
	hospital?: Relation<HospitalEntity>;

	@Column({
		name: 'created',
		type: 'timestamp',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created?: Date;

	@Column({
		name: 'updated',
		type: 'timestamp',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated?: Date;

	@Column({ name: 'name', type: 'varchar', length: 200, nullable: true })
	name?: string;

	@Column({ name: 'name2', type: 'varchar', length: 200, nullable: true })
	name2?: string;

	@Column({ name: 'name3', type: 'varchar', length: 200, nullable: true })
	name3?: string;

	@Column({
		name: 'valor_medio_dia',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	valorMedioDia: number;

	@Column({ name: 'tel', type: 'varchar', length: 30, nullable: true })
	tel?: string;

	@Column({ name: 'tel2', type: 'varchar', length: 30, nullable: true })
	tel2?: string;

	@Column({ name: 'dsc_tp_logra', type: 'varchar', length: 11, nullable: true })
	dscTpLogra?: string;

	@Column({ name: 'endereco', type: 'varchar', length: 100, nullable: true })
	endereco?: string;

	@Column({ name: 'numero', type: 'varchar', length: 100, nullable: true })
	numero?: string;

	@Column({ name: 'bairro', type: 'varchar', length: 30, nullable: true })
	bairro?: string;

	@Column({ name: 'email', type: 'text' })
	email: string;

	@Column({ name: 'enabled', type: 'tinyint', default: 1 })
	enabled: number;

	@Column({ name: 'estado', type: 'varchar', length: 11, nullable: true })
	estado?: string;

	@Column({ name: 'cidade', type: 'varchar', length: 100, nullable: true })
	cidade?: string;

	@Column({ name: 'regiao', type: 'varchar', length: 200, nullable: true })
	regiao?: string;

	@Column({ name: 'regiao_nova', type: 'varchar', length: 200, nullable: true })
	regiaoNova?: string;

	@Column({ name: 'controle', type: 'varchar', length: 100, nullable: true })
	controle?: string;

	@Column({ name: 'controle2', type: 'varchar', length: 100, nullable: true })
	controle2?: string;

	@Column({
		name: 'tipo',
		type: 'varchar',
		length: 100,
		default: 'Rede Credenciada',
	})
	tipo: string;

	@Column({
		name: 'valor_alerta_mes',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	valorAlertaMes: number;

	@Column({
		name: 'enf_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	enfClinica: number;

	@Column({
		name: 'enf_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	enfCirurgica: number;

	@Column({
		name: 'enf_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	enfObstetrica: number;

	@Column({
		name: 'enf_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	enfPediatrica: number;

	@Column({
		name: 'enf_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	enfPsiquiatrica: number;

	@Column({
		name: 'utia_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utiaClinica: number;

	@Column({
		name: 'utia_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utiaCirurgica: number;

	@Column({
		name: 'utia_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utiaObstetrica: number;

	@Column({
		name: 'utia_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utiaPediatrica: number;

	@Column({
		name: 'utia_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utiaPsiquiatrica: number;

	@Column({
		name: 'utip_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utipClinica: number;

	@Column({
		name: 'utip_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utipCirurgica: number;

	@Column({
		name: 'utip_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utipObstetrica: number;

	@Column({
		name: 'utip_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utipPediatrica: number;

	@Column({
		name: 'utip_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utipPsiquiatrica: number;

	@Column({
		name: 'utin_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utinClinica: number;

	@Column({
		name: 'utin_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utinCirurgica: number;

	@Column({
		name: 'utin_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utinObstetrica: number;

	@Column({
		name: 'utin_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utinPediatrica: number;

	@Column({
		name: 'utin_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	utinPsiquiatrica: number;

	@Column({
		name: 'tsi_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	tsiClinica: number;

	@Column({
		name: 'tsi_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	tsiCirurgica: number;

	@Column({
		name: 'tsi_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	tsiObstetrica: number;

	@Column({
		name: 'tsi_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	tsiPediatrica: number;

	@Column({
		name: 'tsi_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	tsiPsiquiatrica: number;

	@Column({
		name: 'uco_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucoClinica: number;

	@Column({
		name: 'uco_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucoCirurgica: number;

	@Column({
		name: 'uco_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucoObstetrica: number;

	@Column({
		name: 'uco_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucoPediatrica: number;

	@Column({
		name: 'uco_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucoPsiquiatrica: number;

	@Column({
		name: 'uce_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	uceClinica: number;

	@Column({
		name: 'uce_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	uceCirurgica: number;

	@Column({
		name: 'uce_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	uceObstetrica: number;

	@Column({
		name: 'uce_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucePediatrica: number;

	@Column({
		name: 'uce_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	ucePsiquiatrica: number;

	@Column({
		name: 'bern_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	bernClinica: number;

	@Column({
		name: 'bern_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	bernCirurgica: number;

	@Column({
		name: 'bern_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	bernObstetrica: number;

	@Column({
		name: 'bern_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	bernPediatrica: number;

	@Column({
		name: 'bern_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	bernPsiquiatrica: number;

	@Column({
		name: 'berpp_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	berppClinica: number;

	@Column({
		name: 'berpp_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	berppCirurgica: number;

	@Column({
		name: 'berpp_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	berppObstetrica: number;

	@Column({
		name: 'berpp_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	berppPediatrica: number;

	@Column({
		name: 'berpp_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	berppPsiquiatrica: number;

	@Column({
		name: 'apart_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	apartClinica: number;

	@Column({
		name: 'apart_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	apartCirurgica: number;

	@Column({
		name: 'apart_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	apartObstetrica: number;

	@Column({
		name: 'apart_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	apartPediatrica: number;

	@Column({
		name: 'domi_transicao',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domiTransicao: number;

	@Column({
		name: 'domi_multi',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domiMulti: number;

	@Column({
		name: 'domi_medicamento',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domiMedicamento: number;

	@Column({
		name: 'domi_curativo',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domiCurativo: number;

	@Column({
		name: 'domi_6h',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domi6h: number;

	@Column({
		name: 'domi_12h',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domi12h: number;

	@Column({
		name: 'domi_24AV',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domi24AV: number;

	@Column({
		name: 'domi_24VM',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	domi24VM: number;

	@Column({
		name: 'oneday_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	onedayPsiquiatrica: number;

	@Column({
		name: 'oneday_pediatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	onedayPediatrica: number;

	@Column({
		name: 'oneday_obstetrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	onedayObstetrica: number;

	@Column({
		name: 'oneday_cirurgica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	onedayCirurgica: number;

	@Column({
		name: 'oneday_clinica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	onedayClinica: number;

	@Column({
		name: 'apart_psiquiatrica',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	apartPsiquiatrica: number;

	@Column({
		name: 'permanencia_enf',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	permanenciaEnf: number;

	@Column({
		name: 'permanencia_utia',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	permanenciaUtia: number;

	@Column({
		name: 'uti_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiPsiquiatrica: number;

	@Column({
		name: 'uti_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiPediatrica: number;

	@Column({
		name: 'uti_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiObstetrica: number;

	@Column({
		name: 'uti_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiCirurgica: number;

	@Column({
		name: 'uti_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiClinica: number;

	@Column({
		name: 'utii_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiiPsiquiatrica: number;

	@Column({
		name: 'utii_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiiPediatrica: number;

	@Column({
		name: 'utii_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiiObstetrica: number;

	@Column({
		name: 'utii_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiiCirurgica: number;

	@Column({
		name: 'utii_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiiClinica: number;

	@Column({
		name: 'utini_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiniPsiquiatrica: number;

	@Column({
		name: 'utini_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiniPediatrica: number;

	@Column({
		name: 'utini_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiniObstetrica: number;

	@Column({
		name: 'utini_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiniCirurgica: number;

	@Column({
		name: 'utini_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utiniClinica: number;

	@Column({
		name: 'ucoi_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucoiPsiquiatrica: number;

	@Column({
		name: 'ucoi_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucoiPediatrica: number;

	@Column({
		name: 'ucoi_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucoiObstetrica: number;

	@Column({
		name: 'ucoi_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucoiCirurgica: number;

	@Column({
		name: 'ucoi_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucoiClinica: number;

	@Column({
		name: 'semiutii_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiiPsiquiatrica: number;

	@Column({
		name: 'semiutii_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiiPediatrica: number;

	@Column({
		name: 'semiutii_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiiObstetrica: number;

	@Column({
		name: 'ssemiutii_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ssemiutiiCirurgica: number;

	@Column({
		name: 'semiutii_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiiClinica: number;

	@Column({
		name: 'semiuti_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiPsiquiatrica: number;

	@Column({
		name: 'semiuti_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiPediatrica: number;

	@Column({
		name: 'semiuti_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiObstetrica: number;

	@Column({
		name: 'semiuti_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiCirurgica: number;

	@Column({
		name: 'semiuti_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	semiutiClinica: number;

	@Column({
		name: 'utmo_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utmoPsiquiatrica: number;

	@Column({
		name: 'utmo_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utmoPediatrica: number;

	@Column({
		name: 'utmo_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utmoObstetrica: number;

	@Column({
		name: 'utmo_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utmoCirurgica: number;

	@Column({
		name: 'utmo_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	utmoClinica: number;

	@Column({
		name: 'ucg_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgPsiquiatrica: number;

	@Column({
		name: 'ucg_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgPediatrica: number;

	@Column({
		name: 'ucg_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgObstetrica: number;

	@Column({
		name: 'ucg_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgCirurgica: number;

	@Column({
		name: 'ucg_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgClinica: number;

	@Column({
		name: 'ucgi_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgiPsiquiatrica: number;

	@Column({
		name: 'ucgi_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgiPediatrica: number;

	@Column({
		name: 'ucgi_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgiObstetrica: number;

	@Column({
		name: 'ucgi_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgiCirurgica: number;

	@Column({
		name: 'ucgi_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	ucgiClinica: number;

	@Column({
		name: 'aparti_psiquiatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	apartiPsiquiatrica: number;

	@Column({
		name: 'aparti_pediatrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	apartiPediatrica: number;

	@Column({
		name: 'aparti_obstetrica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	apartiObstetrica: number;

	@Column({
		name: 'aparti_cirurgica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	apartiCirurgica: number;

	@Column({
		name: 'aparti_clinica',
		type: 'decimal',
		precision: 8,
		scale: 2,
		default: '0.00',
	})
	apartiClinica: number;

	@Column({
		name: 'complexity',
		type: 'varchar',
		length: 100,
		default: 'Media',
	})
	complexity: string;

	@Column({ name: 'city_id', type: 'int', unsigned: true, nullable: true })
	cityId?: number;

	@Column({ name: 'state_id', type: 'int', nullable: true })
	stateId?: number;

	@Column({
		name: 'codigo_interno',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	codigoInterno?: string;

	@Column({
		name: 'handlePrestador',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	handlePrestador?: string;

	@Column({
		name: 'homecare_equipamento',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	homecareEquipamento: number;

	@Column({
		name: 'homecare_material',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	homecareMaterial: number;

	@Column({
		name: 'assistencia_domiciliar',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	assistenciaDomiciliar: number;

	@Column({
		name: 'internacao_domiciliar',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	internacaoDomiciliar: number;

	@Column({
		name: 'homecare_fisioterapia',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	homecareFisioterapia: number;

	@Column({
		name: 'homecare_internado_24',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	homecareInternado24: number;

	@Column({
		name: 'homecare_antibiotic',
		type: 'decimal',
		precision: 10,
		scale: 2,
		default: '0.00',
	})
	homecareAntibiotic: number;

	@Column({
		name: 'cod_drg',
		type: 'int',
		nullable: true,
		comment: 'codigo do prestador para ser informado ao DRG',
	})
	codDrg?: number;

	@OneToMany(
		() => AutorizacoesEntity,
		(autorizacoes) => autorizacoes.hospitalCompany,
	)
	autorizacoes: Relation<AutorizacoesEntity[]>;
}
