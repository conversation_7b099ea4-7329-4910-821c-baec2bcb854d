import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyToOne,
	Join<PERSON><PERSON>umn,
} from 'typeorm';
import { HospitalizationEntity } from './hospitalization.entity';

@Entity('form_cids')
export class FormCidsEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true, name: 'id' })
	id: number;

	@Column({
		type: 'timestamp',
		name: 'created',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created: Date;

	@Column({
		type: 'timestamp',
		name: 'updated',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated?: Date;

	@Column({ type: 'tinyint', name: 'type', default: 0 })
	type: number;

	@Column({ type: 'tinyint', name: 'status', default: 0 })
	status: number;

	@ManyToOne(() => HospitalizationEntity, { nullable: true })
	@JoinColumn({ name: 'patient_way_id' })
	patientWay?: HospitalizationEntity;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'patient_way_id',
		nullable: true,
	})
	patientWayId?: number;

	@Column({ type: 'int', unsigned: true, name: 'user_id' })
	userId: number;

	@Column({ type: 'tinyint', name: 'enabled', width: 1, default: 1 })
	enabled: boolean;

	@Column({ type: 'tinyint', name: 'is_censo', width: 1, default: 0 })
	isCenso: boolean;

	@Column({
		name: 'cid_sub_categoria_id',
		type: 'varchar',
		length: 20,
		nullable: true,
	})
	cidSubCategoriaId?: string;

	@Column({
		name: 'cid_categoria_id',
		type: 'varchar',
		length: 20,
		nullable: true,
	})
	cidCategoriaId?: string;

	@Column({
		name: 'codIntegration',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codIntegration?: string;
}
