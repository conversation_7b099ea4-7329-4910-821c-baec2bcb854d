import { Entity, PrimaryGeneratedColumn, Column, OneToMany } from 'typeorm';
import { CensoEntity } from './censo.entity';

@Entity('censo_status')
export class CensoStatusEntity {
	@PrimaryGeneratedColumn('increment')
	id: string;

	@Column({ type: 'varchar', length: 100 })
	descricao: string;

	@Column({ type: 'varchar', length: 7 })
	cor: string;

	@Column({ name: 'design_status', type: 'varchar', length: 100 })
	designStatus: string;

	@OneToMany(() => CensoEntity, (censo) => censo.censoStatusId)
	censos: CensoEntity[];
}
