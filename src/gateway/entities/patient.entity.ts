import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	OneToMany,
	Relation,
	ManyToOne,
	JoinColumn,
} from 'typeorm';
import { AutorizacoesEntity } from './autorizacoes.entity';
import { PlanosEntity } from './planos.entity';
import { EmpresaEntity } from './empresa.entity';

@Entity('patients')
export class PatientEntity {
	@PrimaryGeneratedColumn({ name: 'id', type: 'int', unsigned: true })
	id: number;

	@Column({ name: 'enabled', type: 'tinyint', default: 1 })
	enabled: number;

	@Column({ name: 'created', type: 'timestamp', nullable: true })
	created: Date;

	@Column({ name: 'updated', type: 'timestamp', nullable: true })
	updated: Date;

	@Column({ name: 'user_id', type: 'int', unsigned: true, nullable: true })
	userId: number;

	@Column({ name: 'name', type: 'varchar', length: 200, nullable: true })
	name: string;

	@Column({ name: 'email', type: 'varchar', length: 60, nullable: true })
	email: string;

	@Column({ name: 'mother_name', type: 'varchar', length: 200, nullable: true })
	motherName: string;

	@Column({ name: 'company_id', type: 'int', unsigned: true, nullable: true })
	companyId: number;

	@Column({ name: 'gender', type: 'varchar', length: 11, nullable: true })
	gender: string;

	@Column({
		name: 'cod_beneficiario',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	codBeneficiario: string;

	@Column({ name: 'birthday', type: 'date', nullable: true })
	birthday: Date;

	@Column({ name: 'plan_id', type: 'int', nullable: true })
	planId: number;

	@Column({ name: 'plan_start_date', type: 'datetime', nullable: true })
	planStartDate: Date;

	@Column({ name: 'plan_end_date', type: 'datetime', nullable: true })
	planEndDate: Date;

	@Column({ name: 'group', type: 'varchar', length: 200, nullable: true })
	group: string;

	@Column({ name: 'occupation', type: 'varchar', length: 200, nullable: true })
	occupation: string;

	@Column({
		name: 'pregnant_add_info',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	pregnantAddInfo: string;

	@Column({
		name: 'lactating_add_info',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	lactatingAddInfo: string;

	@Column({
		name: 'permission_app_max_date',
		type: 'timestamp',
		nullable: true,
	})
	permissionAppMaxDate: Date;

	@Column({ name: 'start_date', type: 'timestamp', nullable: true })
	startDate: Date;

	@Column({
		name: 'image',
		type: 'varchar',
		length: 200,
		default: 'user_default.png',
	})
	image: string;

	@Column({ name: 'tel', type: 'varchar', length: 200, nullable: true })
	tel: string;

	@Column({ name: 'patientCity', type: 'varchar', length: 200, nullable: true })
	patientCity: string;

	@Column({
		name: 'codCidadeIbge',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	codCidadeIbge: string;

	@Column({ name: 'reg_hosp', type: 'varchar', length: 200, nullable: true })
	regHosp: string;

	@Column({ name: 'color', type: 'varchar', length: 200, nullable: true })
	color: string;

	@Column({ name: 'cpf', type: 'varchar', length: 50, nullable: true })
	cpf: string;

	@Column({
		name: 'internado_repasse',
		type: 'tinyint',
		default: 0,
		comment:
			"0 == 'INDIVIDUAL'\n1 == 'REPASSADA'\n2 == 'PRESTACAO DE SERVICOS'\n3 == 'EMPRESA'\n4 == 'EMPRESA /PRESTACAO DE SERVICOS'",
	})
	internadoRepasse: number;

	@Column({ name: 'is_censo', type: 'tinyint', default: 0 })
	isCenso: number;

	@Column({ name: 'replicate_flag', type: 'tinyint', default: 1 })
	replicateFlag: number;

	@Column({ name: 'city_id', type: 'int', unsigned: true, nullable: true })
	cityId: number;

	@Column({ name: 'street', type: 'varchar', length: 200, nullable: true })
	street: string;

	@Column({ name: 'home_number', type: 'varchar', length: 11, nullable: true })
	homeNumber: string;

	@Column({
		name: 'neighborhood',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	neighborhood: string;

	@Column({ name: 'zipcode', type: 'varchar', length: 10, nullable: true })
	zipcode: string;

	@Column({ name: 'complement', type: 'varchar', length: 200, nullable: true })
	complement: string;

	@Column({
		name: 'corporation_id',
		type: 'int',
		unsigned: true,
		nullable: true,
	})
	corporationId: number;

	@Column({ name: 'status', type: 'varchar', length: 45, nullable: true })
	status: string;

	@Column({ name: 'error', type: 'int', nullable: true })
	error: number;

	@Column({
		name: 'codIntegration',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codIntegration: string;

	@Column({ name: 'plan_regulamentado', type: 'tinyint', default: 1 })
	planRegulamentado: number;

	@Column({ name: 'plano_tipo', type: 'varchar', length: 100, nullable: true })
	planoTipo: string;

	@Column({
		name: 'plano_abrangencia',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	planoAbrangencia: string;

	@Column({
		name: 'plano_tipo_rede',
		type: 'varchar',
		length: 100,
		nullable: true,
	})
	planoTipoRede: string;

	@Column({ name: 'plano_carencia', type: 'text', nullable: true })
	planoCarencia: string;

	@Column({ name: 'grupos_cidades_ibge', type: 'text', nullable: true })
	gruposCidadesIbge: string;

	@Column({
		name: 'is_rn',
		type: 'tinyint',
		nullable: true,
		comment:
			'0 ou NULL caso não seja RN, caso seja 1, indica que é o primeiro RN. 2, o segundo, etc.',
	})
	isRn: number;

	@Column({
		name: 'carteiraNacionalSaude',
		type: 'varchar',
		length: 15,
		nullable: true,
	})
	carteiraNacionalSaude: string;

	@Column({ name: 'idade', type: 'int', nullable: true })
	idade: number;

	@Column({ name: 'sexo', type: 'varchar', length: 1, nullable: true })
	sexo: string;

	@Column({
		name: 'massa',
		type: 'decimal',
		precision: 6,
		scale: 2,
		nullable: true,
	})
	massa: number;

	@Column({
		name: 'altura',
		type: 'decimal',
		precision: 6,
		scale: 2,
		nullable: true,
	})
	altura: number;

	@Column({
		name: 'superficie_corporal',
		type: 'decimal',
		precision: 4,
		scale: 2,
		nullable: true,
	})
	superficieCorporal: number;

	@OneToMany(
		() => AutorizacoesEntity,
		(authorizationGuides) => authorizationGuides.patient,
	)
	autorizacoes: Relation<AutorizacoesEntity>[];

	@ManyToOne(() => PlanosEntity, (planosEntity) => planosEntity.id)
	@JoinColumn({ name: 'plan_id' })
	plan: Relation<PlanosEntity>;

	@ManyToOne(() => EmpresaEntity, (corporationsEntity) => corporationsEntity.id)
	@JoinColumn({ name: 'corporation_id' })
	corporation: Relation<EmpresaEntity>;
}
