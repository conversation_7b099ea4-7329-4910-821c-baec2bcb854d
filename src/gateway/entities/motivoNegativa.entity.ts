import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
} from 'typeorm';

@Entity('motivo_negativa')
export class MotivoNegativaEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		unsigned: true,
		name: 'id',
	})
	id: number;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'code_tas',
		nullable: false,
	})
	codeTas: number;

	@Column({
		type: 'varchar',
		length: 140,
		name: 'Category',
		nullable: true,
	})
	category: string | null;

	@Column({
		type: 'varchar',
		length: 240,
		name: 'description',
		nullable: true,
	})
	description: string | null;

	@Column({
		type: 'tinyint',
		name: 'enabled',
		default: 1,
		nullable: true,
	})
	enabled: number | boolean;

	@CreateDateColumn({
		type: 'datetime',
		name: 'created',
		default: () => 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	created: Date;

	@UpdateDateColumn({
		type: 'datetime',
		name: 'updated',
		nullable: true,
	})
	updated: Date | null;
}
