import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyTo<PERSON>ne,
	Jo<PERSON><PERSON><PERSON><PERSON><PERSON>,
	OneToMany,
	Relation,
} from 'typeorm';
import { PatientEntity } from './patient.entity';
import { OperadoraEntity } from './operadora.entity';
import { HospitalsCompanyEntity } from './hospitalsCompany.entity';
import { MotivoNegativaEntity } from './motivoNegativa.entity';
import { AutorizacoesAuditStatusEntity } from './autorizacoesAuditStatus.entity';
import { AutorizacoesAuditEntity } from './autorizacoesAudit.entity';
import { AutorizacoesTagsEntity } from './autorizacoesTags.entity';
import { AutorizacoesItensEntity } from './autorizacoesItens.entity';

@Entity('authorization_guides')
export class AutorizacoesEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true, name: 'id' })
	id: number;

	@Column({
		type: 'datetime',
		name: 'created',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created: Date;

	@Column({ type: 'varchar', name: 'caraterInternacao', nullable: true })
	caraterInternacao: string;

	@Column({ type: 'datetime', name: 'dataSolicitacao', nullable: true })
	dataSolicitacao: Date;

	@Column({ type: 'datetime', name: 'limit_date', nullable: true })
	limitDate: Date;

	@Column({ type: 'varchar', name: 'pac', nullable: true })
	pac: string;

	@Column({ type: 'varchar', name: 'nomePrestador', nullable: true })
	nomePrestador: string;

	@Column({ type: 'varchar', name: 'regimeInternacao', nullable: true })
	regimeInternacao: string;

	@Column({ type: 'varchar', name: 'authorization_status_ia', nullable: true })
	authorizationStatusIa: string;

	@Column({ type: 'varchar', name: 'authorization_motivo_ia', nullable: true })
	authorizationMotivoIa: string;

	@Column({
		type: 'text',
		name: 'authorization_description_ia',
		nullable: true,
	})
	authorizationDescriptionIa: string;

	@ManyToOne(() => PatientEntity, (patient) => patient.autorizacoes)
	@JoinColumn({ name: 'patient_id' })
	patient: PatientEntity;

	@ManyToOne(() => OperadoraEntity)
	@JoinColumn({ name: 'company_id' })
	company: OperadoraEntity;

	@Column({ name: 'company_id' })
	companyId: number;

	@Column({ type: 'varchar', name: 'numeroGuia', nullable: true })
	numeroGuia: string;

	@Column({ type: 'varchar', name: 'cidPrincipal', nullable: true })
	cidPrincipal: string;

	@Column({ type: 'varchar', name: 'tipoGuia', nullable: true })
	tipoGuia: string;

	@Column({ type: 'int', name: 'numero_reanalises', nullable: true })
	numeroReanalises: number;

	@Column({ type: 'varchar', name: 'transaction_number', nullable: true })
	transactionNumber: string;

	@Column({ type: 'varchar', name: 'tipoGuia2', nullable: true })
	tipoGuia2: string;

	@Column({ type: 'datetime', name: 'dataVencimento', nullable: true })
	dataVencimento: Date;

	@Column({ type: 'boolean', name: 'isFavorito', nullable: true })
	isFavorito: boolean;

	@Column({ type: 'text', name: 'sugestao', nullable: true })
	sugestao: string;

	@ManyToOne(
		() => HospitalsCompanyEntity,
		(hospitalCompany) => hospitalCompany.id,
	)
	@JoinColumn({ name: 'hospital_company_id' })
	hospitalCompany: HospitalsCompanyEntity;

	@ManyToOne(() => MotivoNegativaEntity)
	@JoinColumn({ name: 'motivo_negativa_id' })
	motivoNegativa: MotivoNegativaEntity;

	@Column({ type: 'boolean', name: 'enabled', default: true })
	enabled: boolean;

	@OneToMany(
		() => AutorizacoesAuditEntity,
		(autorizacoesAudit) => autorizacoesAudit.autorizacoes,
	)
	autorizacoesAudit: Relation<AutorizacoesEntity>[];

	@OneToMany(() => AutorizacoesTagsEntity, (tags) => tags.autorizacoes)
	tags: Relation<AutorizacoesTagsEntity>[];

	@ManyToOne(() => AutorizacoesAuditStatusEntity)
	@JoinColumn({ name: 'audit_status' })
	autorizacaoStatus: AutorizacoesAuditStatusEntity;

	@OneToMany(() => AutorizacoesItensEntity, (itens) => itens.autorizacao)
	itens: Relation<AutorizacoesItensEntity[]>;
}
