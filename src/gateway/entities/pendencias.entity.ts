import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
} from 'typeorm';

@Entity('pendency')
export class PendenciasEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		unsigned: true,
		name: 'id',
	})
	id: number;

	@CreateDateColumn({
		type: 'timestamp',
		name: 'created',
		default: () => 'CURRENT_TIMESTAMP',
		nullable: false,
	})
	created: Date;

	@UpdateDateColumn({
		type: 'timestamp',
		name: 'updated',
		onUpdate: 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	updated: Date | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'user_id',
		nullable: true,
	})
	userId: number | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'patient_id',
		nullable: true,
	})
	patientId: number | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'patient_way_id',
		nullable: true,
	})
	patientWayId: number | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'bill_capeante_id',
		nullable: true,
	})
	billCapeanteId: number | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'authorization_id',
		nullable: true,
	})
	authorizationId: number | null;

	@Column({
		type: 'text',
		name: 'title',
		nullable: true,
	})
	title: string | null;

	@Column({
		type: 'int',
		name: 'priority',
		nullable: true,
	})
	priority: number | null;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'category',
		nullable: true,
	})
	category: number | null;

	@Column({
		type: 'text',
		name: 'description',
		nullable: true,
	})
	description: string | null;

	@Column({
		type: 'tinyint',
		name: 'enabled',
		default: 1,
		nullable: true,
	})
	enabled: number | boolean;

	@Column({
		type: 'int',
		name: 'is_viewed',
		default: 1,
		nullable: true,
	})
	isViewed: number | boolean;

	@Column({
		type: 'timestamp',
		name: 'created_by_integration',
		nullable: true,
	})
	createdByIntegration: Date | null;

	@Column({
		type: 'int',
		name: 'cod_integration',
		nullable: true,
	})
	codIntegration: number | null;

	@Column({
		type: 'text',
		name: 'especialidade_destino_integra',
		nullable: true,
	})
	especialidadeDestinoIntegra: string | null;

	@Column({
		type: 'timestamp',
		name: 'limit_date',
		nullable: true,
	})
	limitDate: Date | null;
}
