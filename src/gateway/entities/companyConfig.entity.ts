import {
	<PERSON>um<PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON>To<PERSON>ne,
	PrimaryGeneratedColumn,
} from 'typeorm';
import { CompanyConfigFunctionsEntity } from './companyConfigFunctions.entity';
import { OperadoraEntity } from './operadora.entity';

@Entity('company_configs')
export class CompanyConfigEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		name: 'id',
	})
	id: number;

	@ManyToOne(() => OperadoraEntity, (operadora) => operadora.id)
	@JoinColumn({ name: 'company_id' })
	company: OperadoraEntity;

	@ManyToOne(
		() => CompanyConfigFunctionsEntity,
		(functionEntity) => functionEntity.id,
		{
			nullable: true,
			eager: true,
		},
	)
	@JoinColumn({ name: 'config_function_id' })
	configFunction: CompanyConfigFunctionsEntity;

	@Column({
		type: 'varchar',
		name: 'value',
	})
	value: string;

	@Column({
		type: 'tinyint',
		name: 'enabled',
	})
	enabled: boolean;
}
