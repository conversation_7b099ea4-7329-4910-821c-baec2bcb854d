import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyTo<PERSON>ne,
	Join<PERSON><PERSON>umn,
} from 'typeorm';
import { GuideEntity } from './guide.entity';

@Entity('guide_status')
export class GuideStatusEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true, name: 'id' })
	id: number;

	@ManyToOne(() => GuideEntity, { nullable: true })
	@JoinColumn({ name: 'guide_id' })
	guide: GuideEntity;

	@Column({ type: 'int', name: 'guide_id', unsigned: true, nullable: true })
	guideId?: number;

	@Column({ type: 'int', name: 'approved_by', nullable: true })
	approvedBy: number;

	@Column({
		type: 'int',
		width: 1,
		name: 'status',
		default: 0,
		comment:
			'0 - aberto, 1 - analise, 2 - aprovado, 3 - recusado, 4 - administrativo, 5 - reanalise',
	})
	status: number;

	@Column({ type: 'int', width: 4, name: 'justificativa', nullable: true })
	justificativa: number;

	@Column({ type: 'text', name: 'obs', nullable: true })
	obs: string;

	@Column({
		type: 'timestamp',
		name: 'created',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created: Date;

	@Column({
		type: 'timestamp',
		name: 'updated',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated: Date;

	@Column({ type: 'int', width: 1, name: 'enabled', default: 1 })
	enabled: number;

	@Column({ type: 'int', unsigned: true, name: 'user_id', nullable: true })
	userId: number;
}
