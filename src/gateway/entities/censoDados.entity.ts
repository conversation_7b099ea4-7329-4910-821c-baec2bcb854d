import {
	<PERSON>umn,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>um<PERSON>,
	ManyToOne,
	OneToMany,
	PrimaryGeneratedColumn,
	Relation,
} from 'typeorm';
import { CensoEntity } from './censo.entity';
import { LinhasConflitadasCensoEntity } from './linhasConflitadasCenso.entity';

@Entity('censo_dados')
export class CensoDadosEntity {
	@PrimaryGeneratedColumn('increment')
	id: number;

	@Column({ name: 'data_criacao', type: 'timestamp' })
	dataCriacao: Date;

	@Column({ name: 'data_exclusao', type: 'timestamp' })
	dataExclusao: Date;

	@Column({ name: 'data_edicao', type: 'timestamp' })
	dataEdicao: Date;

	@Column({ name: 'data_expiracao', type: 'timestamp' })
	dataExpiracao: Date;

	@Column({ name: 'censo_id', type: 'int' })
	censoId: number;

	@Column({ name: 'company_id', type: 'int' })
	companyId: number;

	@Column({ name: 'user_id', type: 'int' })
	userId: number;

	@Column({ name: 'conflito', type: 'tinyint', nullable: true })
	conflito: number;

	@Column({ name: 'data', type: 'timestamp', nullable: true })
	data: Date;

	@Column({ name: 'municipio', type: 'varchar', length: 255, nullable: true })
	municipio: string;

	@Column({
		name: 'hospital_credenciado',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	hospitalCredenciado: string;

	@Column({ name: 'controle', type: 'varchar', length: 255, nullable: true })
	controle: string;

	@Column({ name: 'dt_nascimento', type: 'date', nullable: true })
	dtNascimento: Date;

	@Column({ name: 'data_internacao', type: 'date', nullable: true })
	dataInternacao: Date;

	@Column({ name: 'data_alta', type: 'timestamp', nullable: true })
	dataAlta: Date;

	@Column({ name: 'motivo_alta', type: 'varchar', length: 255, nullable: true })
	motivoAlta: string;

	@Column({ name: 'diagnostico', type: 'varchar', length: 255, nullable: true })
	diagnostico: string;

	@Column({
		name: 'diagnostico_secundario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	diagnosticoSecundario: string;

	@Column({ name: 'previsao_alta', type: 'timestamp', nullable: true })
	previsaoAlta: Date;

	@Column({
		name: 'carater_internacao',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	caraterInternacao: string;

	@Column({
		name: 'tipo_internacao',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	tipoInternacao: string;

	@Column({ name: 'codigo_guia', type: 'varchar', length: 255, nullable: true })
	codigoGuia: string;

	@Column({
		name: 'alto_custo_status',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	altoCustoStatus: string;

	@Column({
		name: 'nome_beneficiario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	nomeBeneficiario: string;

	@Column({
		name: 'cod_beneficiario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codBeneficiario: string;

	@Column({
		name: 'cidade_beneficiario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	cidadeBeneficiario: string;

	@Column({
		name: 'estado_beneficiario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	estadoBeneficiario: string;

	@Column({ name: 'recem_nascido', type: 'tinyint', nullable: true })
	recemNascido: boolean;

	@Column({ name: 'tipo_cliente', type: 'char', length: 1, nullable: true })
	tipoCliente: string;

	@Column({ name: 'valor_diaria', type: 'varchar', length: 50, nullable: true })
	valorDiaria: string;

	@Column({
		name: 'regional_beneficiario',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	regionalBeneficiario: string;

	@Column({
		name: 'tipo_controle',
		type: 'varchar',
		length: 50,
		nullable: true,
	})
	tipoControle: string;

	@Column({ name: 'diarias_autorizadas', type: 'int', nullable: true })
	diariasAutorizadas: number;

	@Column({
		name: 'codigo_hospital',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codigoHospital: string;

	@Column({
		name: 'codigo_plano',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codigoPlano: string;

	@Column({ name: 'nome_plano', type: 'varchar', length: 255, nullable: true })
	nomePlano: string;

	@Column({
		name: 'codigo_empresa',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	codigoEmpresa: string;

	@Column({
		name: 'nome_empresa',
		type: 'varchar',
		length: 255,
		nullable: true,
	})
	nomeEmpresa: string;

	@Column({ name: 'status_plano', type: 'varchar', length: 50, nullable: true })
	statusPlano: string;

	@Column({ name: 'data_plano_desde', type: 'timestamp', nullable: true })
	dataPlanoDesde: Date;

	@ManyToOne(() => CensoEntity, (censo) => censo.id, {
		eager: false,
	})
	@JoinColumn({ name: 'censo_id' })
	censo: CensoEntity;

	@OneToMany(
		() => LinhasConflitadasCensoEntity,
		(linhasConflitadas) => linhasConflitadas.censoDados,
	)
	linhasConflitadas: Relation<LinhasConflitadasCensoEntity>[];

	@Column({ name: 'acomodacao', type: 'varchar', length: 255, nullable: true })
	acomodacao: string;
}
