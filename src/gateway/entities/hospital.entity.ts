import {
	Column,
	Entity,
	OneToMany,
	PrimaryGeneratedColumn,
	Relation,
} from 'typeorm';
import { HospitalsCompanyEntity } from './hospitalsCompany.entity';

@Entity('hospitals')
export class HospitalEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true })
	id: number;

	@Column({
		name: 'created',
		type: 'timestamp',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created?: Date;

	@Column({
		name: 'updated',
		type: 'timestamp',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated?: Date;

	@Column({ name: 'CO_UNIDADE', type: 'varchar', length: 100 })
	coUnidade: string;

	@Column({ name: 'CO_CNES', type: 'varchar', length: 200 })
	coCnes: string;

	@Column({
		name: 'NU_CNPJ_MANTENEDORA',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	nuCnpjMantenedora?: string;

	@Column({
		name: 'NO_RAZAO_SOCIAL',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	noRazaoSocial?: string;

	@Column({ name: 'NO_FANTASIA', type: 'varchar', length: 200, nullable: true })
	noFantasia?: string;

	@Column({
		name: 'NO_LOGRADOURO',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	noLogradouro?: string;

	@Column({ name: 'NU_ENDERECO', type: 'varchar', length: 200, nullable: true })
	nuEndereco?: string;

	@Column({
		name: 'NO_COMPLEMENTO',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	noComplemento?: string;

	@Column({ name: 'NO_BAIRRO', type: 'varchar', length: 200, nullable: true })
	noBairro?: string;

	@Column({ name: 'CO_CEP', type: 'varchar', length: 200, nullable: true })
	coCep?: string;

	@Column({ name: 'NU_TELEFONE', type: 'varchar', length: 200, nullable: true })
	nuTelefone?: string;

	@Column({ name: 'NU_FAX', type: 'varchar', length: 200, nullable: true })
	nuFax?: string;

	@Column({ name: 'NO_EMAIL', type: 'varchar', length: 200, nullable: true })
	noEmail?: string;

	@Column({ name: 'NU_CPF', type: 'varchar', length: 200, nullable: true })
	nuCpf?: string;

	@Column({ name: 'NU_CNPJ', type: 'varchar', length: 200, nullable: true })
	nuCnpj?: string;

	@Column({
		name: 'CO_ATIVIDADE',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	coAtividade?: string;

	@Column({
		name: 'CO_CLIENTELA',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	coClientela?: string;

	@Column({ name: 'TP_UNIDADE', type: 'int', nullable: true })
	tpUnidade?: number;

	@Column({
		name: 'CO_TURNO_ATENDIMENTO',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	coTurnoAtendimento?: string;

	@Column({ name: 'CO_ESTADO_GESTOR', type: 'int', nullable: true })
	coEstadoGestor?: number;

	@Column({ name: 'CO_MUNICIPIO_GESTOR', type: 'int', nullable: true })
	coMunicipioGestor?: number;

	@Column({
		name: "TO_CHAR(DT_ATUALIZACAO,'DD/MM/YYYY')",
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	dtAtualizacao?: string;

	@Column({ name: 'NO_URL', type: 'varchar', length: 200, nullable: true })
	noUrl?: string;

	@Column({ name: 'NU_LATITUDE', type: 'varchar', length: 200, nullable: true })
	nuLatitude?: string;

	@Column({
		name: 'NU_LONGITUDE',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	nuLongitude?: string;

	@Column({
		name: 'TP_ESTAB_SEMPRE_ABERTO',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	tpEstabSempreAberto?: string;

	@Column({
		name: 'ST_CONEXAO_INTERNET',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	stConexaoInternet?: string;

	@Column({ name: 'TP_GESTAO', type: 'varchar', length: 200, nullable: true })
	tpGestao?: string;

	@Column({
		name: 'TP_LOGRADOURO_CAREFY',
		type: 'varchar',
		length: 200,
		nullable: true,
	})
	tpLogradouroCarefy?: string;

	@Column({ name: 'is_unimed', type: 'tinyint', width: 1, nullable: true })
	isUnimed?: boolean;

	@OneToMany(
		() => HospitalsCompanyEntity,
		(hospitalCompany) => hospitalCompany.hospital,
	)
	hospitalCompany?: Relation<HospitalsCompanyEntity>;
}
