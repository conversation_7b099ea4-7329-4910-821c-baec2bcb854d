import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>um<PERSON>,
	<PERSON>T<PERSON><PERSON><PERSON>,
	PrimaryGeneratedColumn,
	Relation,
} from 'typeorm';
import { CensoDadosEntity } from './censoDados.entity';
import { TiposConflitosEntity } from './tiposConflitos.entity';

@Entity('linhas_conflitadas_censo')
export class LinhasConflitadasCensoEntity {
	@PrimaryGeneratedColumn('increment')
	id: number;

	@ManyToOne(
		() => TiposConflitosEntity,
		(tipoConflito) => tipoConflito.linhasConflitadas,
	)
	@JoinColumn({ name: 'tipo_conflito_id' })
	tipoConflito: Relation<TiposConflitosEntity>;

	@ManyToOne(
		() => CensoDadosEntity,
		(censoDados) => censoDados.linhasConflitadas,
	)
	@JoinColumn({ name: 'censo_dados_id' })
	censoDados: Relation<CensoDadosEntity>;
}
