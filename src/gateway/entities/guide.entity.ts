import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyToOne,
	Jo<PERSON><PERSON><PERSON>umn,
} from 'typeorm';
import { HospitalEntity } from './hospital.entity';
import { PatientEntity } from './patient.entity';
import { HospitalizationEntity } from './hospitalization.entity';

@Entity('guide')
export class GuideEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true, name: 'id' })
	id: number;

	@ManyToOne(() => PatientEntity, { nullable: true })
	@JoinColumn({ name: 'patient_id' })
	patient?: PatientEntity;

	@Column({ type: 'int', unsigned: true, name: 'patient_id', nullable: true })
	patientId?: number;

	@ManyToOne(() => HospitalizationEntity, { nullable: true })
	@JoinColumn({ name: 'patient_way_id' })
	patientWay?: HospitalizationEntity;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'patient_way_id',
		nullable: true,
	})
	patientWayId?: number;

	@Column({
		type: 'timestamp',
		name: 'created',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created?: Date;

	@Column({
		type: 'timestamp',
		name: 'updated',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated?: Date;

	@Column({ type: 'varchar', length: 40, name: 'numeroSenha', nullable: true })
	numeroSenha?: string;

	@Column({ type: 'varchar', length: 100, name: 'numeroGuia', nullable: true })
	numeroGuia?: string;

	@Column({ type: 'timestamp', name: 'dataEmissaoGuia', nullable: true })
	dataEmissaoGuia?: Date;

	@Column({ type: 'timestamp', name: 'dataAutorizacaoGuia', nullable: true })
	dataAutorizacaoGuia?: Date;

	@Column({ type: 'varchar', length: 300, name: 'speciality', nullable: true })
	speciality?: string;

	@ManyToOne(() => HospitalEntity, { nullable: true })
	@JoinColumn({ name: 'hospital_id' })
	hospital?: HospitalEntity;

	@Column({ type: 'int', unsigned: true, name: 'hospital_id', nullable: true })
	hospitalId?: number;

	@Column({ type: 'tinyint', name: 'type', nullable: true })
	type?: number;

	@Column({ type: 'text', name: 'obs', nullable: true })
	obs?: string;

	@Column({ type: 'text', name: 'obs_daily', nullable: true })
	obsDaily?: string;

	@Column({ type: 'text', name: 'obs_procedure', nullable: true })
	obsProcedure?: string;

	@Column({ type: 'text', name: 'obs_drugs', nullable: true })
	obsDrugs?: string;

	@Column({ type: 'text', name: 'obs_materials', nullable: true })
	obsMaterials?: string;

	@Column({ type: 'timestamp', name: 'inicio_periodo', nullable: true })
	inicioPeriodo?: Date;

	@Column({ type: 'timestamp', name: 'final_periodo', nullable: true })
	finalPeriodo?: Date;

	@Column({ type: 'varchar', length: 45, name: 'valor_total', nullable: true })
	valorTotal?: string;

	@Column({ type: 'tinyint', name: 'enabled', default: 1 })
	enabled: number;

	@Column({ type: 'int', unsigned: true, name: 'user_id', nullable: true })
	userId?: number;

	@Column({ type: 'int', unsigned: true, name: 'auditor_id', nullable: true })
	auditorId?: number;

	@Column({ type: 'varchar', length: 200, name: 'tiss_link', nullable: true })
	tissLink?: string;

	@Column({ type: 'int', name: 'is_viewed', default: 1 })
	isViewed: number;

	@Column({
		type: 'decimal',
		precision: 8,
		scale: 2,
		name: 'value_request',
		nullable: true,
	})
	valueRequest?: number;

	@Column({
		type: 'decimal',
		precision: 8,
		scale: 2,
		name: 'value_saved',
		nullable: true,
	})
	valueSaved?: number;

	@Column({
		type: 'decimal',
		precision: 8,
		scale: 2,
		name: 'value_authorized',
		nullable: true,
	})
	valueAuthorized?: number;

	@Column({ type: 'tinyint', name: 'is_censo', default: 0 })
	isCenso: number;

	@Column({ type: 'int', name: 'reanalysis', default: 0 })
	reanalysis: number;

	@Column({
		type: 'varchar',
		length: 100,
		name: 'codIntegration',
		nullable: true,
	})
	codIntegration?: string;

	@Column({ type: 'text', name: 'obs_daily_provider', nullable: true })
	obsDailyProvider?: string;

	@Column({ type: 'text', name: 'obs_procedure_provider', nullable: true })
	obsProcedureProvider?: string;

	@Column({ type: 'text', name: 'obs_drugs_provider', nullable: true })
	obsDrugsProvider?: string;

	@Column({ type: 'text', name: 'obs_materials_provider', nullable: true })
	obsMaterialsProvider?: string;

	@Column({
		type: 'varchar',
		length: 254,
		name: 'motivoNegativa',
		nullable: true,
	})
	motivoNegativa?: string;

	@Column({ type: 'timestamp', name: 'data_expiracao_edicao', nullable: true })
	dataExpiracaoEdicao?: Date;
}
