import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn,
	Relation,
} from 'typeorm';
import { TipoContratoEntity } from './tipoContrato.entity';

@Entity('plans')
export class PlanosEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		unsigned: true,
		name: 'id',
	})
	id: number;

	@CreateDateColumn({
		type: 'timestamp',
		name: 'created',
		default: () => 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	created: Date;

	@UpdateDateColumn({
		type: 'timestamp',
		name: 'updated',
		default: () => 'CURRENT_TIMESTAMP',
		onUpdate: 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	updated: Date;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'company_id',
		nullable: true,
	})
	companyId: number;

	@Column({
		type: 'varchar',
		length: 200,
		name: 'name',
		nullable: true,
	})
	name: string;

	@Column({
		type: 'varchar',
		length: 45,
		name: 'cod_interno',
		nullable: true,
	})
	codInterno: string;

	@Column({
		type: 'int',
		name: 'enabled',
		default: 1,
		nullable: true,
	})
	enabled: boolean;

	@ManyToOne(() => TipoContratoEntity, (tipoContrato) => tipoContrato.id, {
		eager: true,
	})
	@JoinColumn({
		name: 'insurance_id',
	})
	tipoContrato: Relation<TipoContratoEntity>;
}
