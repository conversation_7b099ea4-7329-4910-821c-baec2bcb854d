import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	CreateDateColumn,
	UpdateDateColumn,
} from 'typeorm';

@Entity('corporations')
export class EmpresaEntity {
	@PrimaryGeneratedColumn({
		type: 'int',
		unsigned: true,
		name: 'id',
	})
	id: number;

	@CreateDateColumn({
		type: 'timestamp',
		name: 'created',
		default: () => 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	created: Date;

	@UpdateDateColumn({
		type: 'timestamp',
		name: 'updated',
		onUpdate: 'CURRENT_TIMESTAMP',
		nullable: true,
	})
	updated: Date;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'company_id',
		nullable: true,
	})
	companyId: number;

	@Column({
		type: 'varchar',
		length: 200,
		name: 'name',
		nullable: true,
	})
	name: string;

	@Column({
		type: 'varchar',
		length: 45,
		name: 'cod_interno',
		nullable: true,
	})
	codInterno: string;

	@Column({
		type: 'varchar',
		length: 14,
		name: 'cnpj',
		nullable: true,
	})
	cnpj: string;

	@Column({
		type: 'varchar',
		length: 200,
		name: 'responsaveis_atendimento',
		nullable: true,
	})
	responsaveisAtendimento: string;

	@Column({
		type: 'varchar',
		length: 50,
		name: 'cod_grup_emp',
		nullable: true,
	})
	codGrupEmp: string;

	@Column({
		type: 'varchar',
		length: 50,
		name: 'tipo_empresa',
		nullable: true,
	})
	tipoEmpresa: string;

	@Column({
		type: 'varchar',
		length: 300,
		name: 'desc_ativd',
		nullable: true,
	})
	descAtivd: string;

	@Column({
		type: 'varchar',
		length: 200,
		name: 'grupo_ajustado',
		nullable: true,
	})
	grupoAjustado: string;

	@Column({
		type: 'varchar',
		length: 50,
		name: 'porte_do_grupo',
		nullable: true,
	})
	porteDoGrupo: string;

	@Column({
		type: 'varchar',
		length: 50,
		name: 'status',
		nullable: true,
	})
	status: string;

	@Column({
		type: 'tinyint',
		name: 'enabled',
		default: 1,
		nullable: true,
	})
	enabled: number | boolean;

	@Column({
		type: 'int',
		name: 'lifes_number',
		default: 1,
		nullable: true,
	})
	lifesNumber: number;
}
