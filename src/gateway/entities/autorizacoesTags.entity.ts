import {
	<PERSON>tity,
	Column,
	CreateDate<PERSON><PERSON>umn,
	PrimaryGeneratedColumn,
	UpdateDateColumn,
	ManyToOne,
	JoinColumn,
	Relation,
} from 'typeorm';
import { AutorizacoesEntity } from './autorizacoes.entity';

@Entity('authorizations_tags')
export class AutorizacoesTagsEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true })
	id: number;

	@CreateDateColumn({ type: 'timestamp' })
	created: Date;

	@UpdateDateColumn({ type: 'timestamp' })
	updated: Date;

	@Column({ type: 'tinyint', width: 4, default: () => '1' })
	enabled: boolean;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'authorization_id',
		nullable: true,
	})
	authorizationId: number;

	@Column({ type: 'int', unsigned: true, name: 'user_id', nullable: true })
	userId: number;

	@Column({ type: 'int', unsigned: true, name: 'tag_id', nullable: true })
	tagId: number;

	@ManyToOne(() => AutorizacoesEntity, (autorizacoes) => autorizacoes.id)
	@JoinColumn({ name: 'authorization_id' })
	autorizacoes: Relation<AutorizacoesEntity>;
}
