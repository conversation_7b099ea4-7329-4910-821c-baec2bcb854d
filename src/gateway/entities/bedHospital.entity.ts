import {
	Entity,
	PrimaryGeneratedColumn,
	Column,
	ManyToOne,
	JoinColumn,
} from 'typeorm';
import { HospitalEntity } from './hospital.entity';
import { HospitalizationEntity } from './hospitalization.entity';

@Entity('bed_hospital')
export class BedHospitalEntity {
	@PrimaryGeneratedColumn({ type: 'int', unsigned: true, name: 'id' })
	id: number;

	@Column({
		type: 'datetime',
		name: 'created',
		nullable: true,
		default: () => 'CURRENT_TIMESTAMP',
	})
	created: Date;

	@Column({
		type: 'timestamp',
		name: 'updated',
		nullable: true,
		onUpdate: 'CURRENT_TIMESTAMP',
	})
	updated?: Date;

	@Column({ type: 'timestamp', name: 'changed', nullable: true })
	changed?: Date;

	@Column({ type: 'datetime', name: 'admission_in' })
	admissionIn: Date;

	@Column({ type: 'datetime', name: 'admission_out', nullable: true })
	admissionOut?: Date;

	@Column({
		type: 'int',
		unsigned: true,
		name: 'patient_way_id',
		nullable: true,
	})
	patientWayId?: number;

	@ManyToOne(() => HospitalizationEntity, { nullable: true })
	@JoinColumn({ name: 'patient_way_id' })
	hospitalization?: HospitalizationEntity;

	@Column({ type: 'varchar', length: 200, name: 'bed_number', nullable: true })
	bedNumber?: string;

	@Column({ type: 'varchar', length: 300, name: 'speciality', nullable: true })
	speciality?: string;

	@Column({ type: 'varchar', length: 200, name: 'accommodation' })
	accommodation: string;

	@Column({
		type: 'varchar',
		length: 300,
		name: 'accommodation_custom',
		nullable: true,
	})
	accommodationCustom?: string;

	@ManyToOne(() => HospitalEntity, { nullable: true })
	@JoinColumn({ name: 'hospital_id' })
	hospital?: HospitalEntity;

	@Column({ type: 'int', unsigned: true, name: 'hospital_id', nullable: true })
	hospitalId?: number;

	@Column({ type: 'int', unsigned: true, name: 'user_id', nullable: true })
	userId?: number;

	@Column({ type: 'tinyint', name: 'is_censo', default: 0 })
	isCenso: boolean;

	@Column({ type: 'int', name: 'is_transfer', default: 0 })
	isTransfer: boolean;

	@Column({
		type: 'decimal',
		precision: 8,
		scale: 2,
		name: 'price',
		default: 0.0,
	})
	price: number;

	@Column({ type: 'tinyint', name: 'enabled', default: 1 })
	enabled: boolean;

	@Column({
		type: 'varchar',
		length: 45,
		name: 'accommodation_isolation',
		nullable: true,
	})
	accommodationIsolation?: string;

	@Column({ type: 'int', name: 'codIntegration', nullable: true })
	codIntegration?: number;
}
