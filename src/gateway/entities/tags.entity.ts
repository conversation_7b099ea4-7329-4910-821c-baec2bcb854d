import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';

@Entity('tags')
export class TagsEntity {
	@PrimaryGeneratedColumn('increment')
	id: number;

	@Column({ name: 'created', type: 'timestamp' })
	dataCriacao: Date;

	@Column({ name: 'enabled', type: 'tinyint', default: 1 })
	enabled: boolean;

	@Column({ name: 'habilitado', type: 'tinyint' })
	habilitado: boolean;

	@Column({ name: 'tag', type: 'varchar', length: '70' })
	tag: string;

	@Column({ name: 'company_id', type: 'int', unsigned: true, nullable: true })
	companyId: number;

	@Column({ name: 'modulo', type: 'varchar', length: '100' })
	modulo: string;
}
