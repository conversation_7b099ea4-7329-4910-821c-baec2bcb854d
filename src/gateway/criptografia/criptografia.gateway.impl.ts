import * as crypto from 'crypto';

export class CriptografiaGatewayImpl {
	private readonly algorithm = 'aes-256-cbc';
	private readonly secretKey = crypto.randomBytes(32);
	private readonly iv = crypto.randomBytes(16);

	encrypt(text: string): string {
		const cipher = crypto.createCipheriv(
			this.algorithm,
			this.secretKey,
			this.iv,
		);
		const encrypted = Buffer.concat([
			cipher.update(text, 'utf8'),
			cipher.final(),
		]);
		return this.iv.toString('hex') + ':' + encrypted.toString('hex');
	}

	decrypt(encryptedText: string): string {
		const [ivHex, encryptedHex] = encryptedText.split(':');
		const iv = Buffer.from(ivHex, 'hex');
		const encrypted = Buffer.from(encryptedHex, 'hex');

		const decipher = crypto.createDecipheriv(
			this.algorithm,
			this.secretKey,
			iv,
		);
		const decrypted = Buffer.concat([
			decipher.update(encrypted),
			decipher.final(),
		]);
		return decrypted.toString('utf8');
	}
}
