import { CriptografiaGatewayImpl } from './criptografia.gateway.impl';

describe('CriptografiaGatewayImpl', () => {
	let criptografiaGateway: CriptografiaGatewayImpl;

	beforeEach(() => {
		criptografiaGateway = new CriptografiaGatewayImpl();
	});

	describe('encrypt', () => {
		it('deve criptografar um texto corretamente', () => {
			const plainText = 'texto para criptografar';
			const encryptedText = criptografiaGateway.encrypt(plainText);

			expect(encryptedText).toBeDefined();
			expect(encryptedText).not.toEqual(plainText);
			expect(encryptedText.includes(':')).toBeTruthy();
		});

		it('deve gerar resultados diferentes para o mesmo texto em instâncias diferentes', () => {
			const plainText = 'mesmo texto';
			const firstInstance = new CriptografiaGatewayImpl();
			const secondInstance = new CriptografiaGatewayImpl();

			const firstEncryption = firstInstance.encrypt(plainText);
			const secondEncryption = secondInstance.encrypt(plainText);

			expect(firstEncryption).not.toEqual(secondEncryption);
		});

		it('deve incluir o IV no texto criptografado', () => {
			const plainText = 'texto com iv';
			const encryptedText = criptografiaGateway.encrypt(plainText);

			const parts = encryptedText.split(':');
			expect(parts.length).toBe(2);
			expect(parts[0].length).toBe(32); // IV em hex (16 bytes = 32 caracteres em hex)
		});

		it('deve gerar resultados diferentes para textos diferentes', () => {
			const firstText = 'primeiro texto';
			const secondText = 'segundo texto';

			const firstEncryption = criptografiaGateway.encrypt(firstText);
			const secondEncryption = criptografiaGateway.encrypt(secondText);

			expect(firstEncryption).not.toEqual(secondEncryption);
		});
	});

	describe('decrypt', () => {
		it('deve descriptografar um texto previamente criptografado', () => {
			const originalText = 'texto original';
			const encryptedText = criptografiaGateway.encrypt(originalText);
			const decryptedText = criptografiaGateway.decrypt(encryptedText);

			expect(decryptedText).toEqual(originalText);
		});

		it('deve lançar erro ao tentar descriptografar um texto com formato inválido', () => {
			const invalidEncryptedText = 'textoInvalido';

			expect(() => {
				criptografiaGateway.decrypt(invalidEncryptedText);
			}).toThrow();
		});

		it('deve lançar erro ao tentar descriptografar com IV inválido', () => {
			const invalidIV = '0000000000000000'; // IV inválido em hex
			const validEncryptedText = criptografiaGateway.encrypt('texto qualquer');
			const encryptedPart = validEncryptedText.split(':')[1];
			const invalidEncryptedText = `${invalidIV}:${encryptedPart}`;

			expect(() => {
				criptografiaGateway.decrypt(invalidEncryptedText);
			}).toThrow();
		});

		it('deve lançar erro ao tentar descriptografar com texto criptografado inválido', () => {
			const validEncryptedText = criptografiaGateway.encrypt('texto qualquer');
			const ivPart = validEncryptedText.split(':')[0];
			const invalidEncryptedText = `${ivPart}:invalidEncryptedText`;

			expect(() => {
				criptografiaGateway.decrypt(invalidEncryptedText);
			}).toThrow();
		});
	});

	describe('integração encrypt/decrypt', () => {
		it('deve permitir criptografar e descriptografar textos de diferentes tamanhos', () => {
			const textos = [
				'a', // texto curto
				'texto médio para teste',
				'texto longo para testar a funcionalidade de criptografia e descriptografia com muitos caracteres e garantir que funcione corretamente em todos os casos possíveis', // texto longo
			];

			textos.forEach((texto) => {
				const encryptedText = criptografiaGateway.encrypt(texto);
				const decryptedText = criptografiaGateway.decrypt(encryptedText);
				expect(decryptedText).toEqual(texto);
			});
		});

		it('deve permitir criptografar e descriptografar textos com caracteres especiais', () => {
			const textoComEspeciais = 'Texto com @#$%¨&*()_+{}[]áéíóúçÁÉÍÓÚÇ';

			const encryptedText = criptografiaGateway.encrypt(textoComEspeciais);
			const decryptedText = criptografiaGateway.decrypt(encryptedText);

			expect(decryptedText).toEqual(textoComEspeciais);
		});
	});
});
