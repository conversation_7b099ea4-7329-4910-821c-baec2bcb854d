import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { UpdateHospitalizationGatewayImpl } from './updateHospitalization.gateway.impl';

describe('UpdateHospitalizationGatewayImpl', () => {
	let updateHospitalizationGateway: UpdateHospitalizationGatewayImpl;
	let hospitalizationRepository: jest.Mocked<Repository<HospitalizationEntity>>;

	beforeEach(() => {
		hospitalizationRepository = {
			update: jest.fn(),
		} as unknown as jest.Mocked<Repository<HospitalizationEntity>>;

		updateHospitalizationGateway = new UpdateHospitalizationGatewayImpl(
			hospitalizationRepository,
		);
	});

	it('deve atualizar um Hospitalization corretamente', async () => {
		const id = 1;
		const hospitalization: Partial<Hospitalization> = {
			admissionOut: new Date(),
		};

		await updateHospitalizationGateway.update(id, hospitalization);

		expect(hospitalizationRepository.update).toHaveBeenCalledWith(id, {
			admissionOut: hospitalization.admissionOut,
		});
	});
});
