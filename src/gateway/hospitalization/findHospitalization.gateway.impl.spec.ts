import { FindHospitalizationGatewayImpl } from './findHospitalization.gateway.impl';
import {
	<PERSON><PERSON><PERSON>,
	LessThanOrEqual,
	MoreThanOrEqual,
	Not,
	Or,
	Repository,
} from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { CensoDados } from 'src/core/domain/CensoDados';
import { HospitalizationMapper } from 'src/shared/mapper/hospitalization/HospitalizationMapper';

describe('FindHospitalizationGatewayImpl', () => {
	const patientId = *********;
	const companyId = 3;
	let repositoryMock: jest.Mocked<Repository<HospitalizationEntity>>;
	let findHospitalizationGateway: FindHospitalizationGatewayImpl;
	let censoDadosMock: CensoDados;
	beforeEach(() => {
		censoDadosMock = new CensoDados(
			1, // id
			2, // censoId
			3, // companyId
			1, //userId
			new Date(), // dataCriacao
			1, // conflito
			new Date(), // data
			'São Paulo', // municipio
			'Hospital São Luiz', // hospitalCredenciado
			'Controle A', // controle
			new Date('2000-01-01'), // dtNascimento
			new Date('2024-10-10'), // dataInternacao
			null, // dataAlta
			'', // motivoAlta
			'Diagnóstico A', // diagnostico
			'', // diagnosticoSecundario
			null, // previsaoAlta
			'Eletiva', // caraterInternacao
			'Clínica', // tipoInternacao
			'12345', // codigoGuia
			'', // altoCustoStatus
			'João Silva', // nomeBeneficiario
			'*********', // codBeneficiario
			'Campinas', // cidadeBeneficiario
			'SP', // estadoBeneficiario
			false, // recemNascido
			'', // tipoCliente
			0, // valorDiaria
			'', // regionalBeneficiario
			'', // tipoControle
			3, // diariasAutorizadas
			'123', // codigoHospital
			'456', // codigoPlano
			'Plano A', // nomePlano
			'789', // codigoEmpresa
			'Empresa X', // nomeEmpresa
			'Ativo', // statusPlano
			new Date('2022-01-01'),
		);

		repositoryMock = {
			find: jest.fn(),
			findOne: jest.fn(),
		} as unknown as jest.Mocked<Repository<HospitalizationEntity>>;

		findHospitalizationGateway = new FindHospitalizationGatewayImpl(
			repositoryMock,
		);
	});

	it('deve retornar um PatientWay ao buscar por codBeneficiario', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: { codBeneficiario: '*********', companyId: 3, enabled: 1 },
		} as HospitalizationEntity;
		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);

		const result =
			await findHospitalizationGateway.findByCodBeneficiario(censoDadosMock);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true },
			where: {
				patient: {
					codBeneficiario: '*********',
					companyId: 3,
					enabled: 1,
					birthday: new Date('2000-01-01'),
				},
				enabled: 1,
				admissionOut: IsNull(),
			},
		});

		expect(result).toEqual(patientWayEntityMock);
	});

	it('deve retornar um nulo ao buscar por codBeneficiario e nao bater', async () => {
		repositoryMock.findOne.mockResolvedValue(null);

		const result =
			await findHospitalizationGateway.findByCodBeneficiario(censoDadosMock);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true },
			where: {
				patient: {
					codBeneficiario: '*********',
					companyId: 3,
					enabled: 1,
					birthday: new Date('2000-01-01'),
				},
				enabled: 1,
				admissionOut: IsNull(),
			},
		});

		expect(result).toEqual(null);
	});

	it('deve retornar um PatientWay ao buscar por código da guia', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: {
				codBeneficiario: '*********',
				companyId: 3,
				enabled: 1,
				name: 'João Silva',
				birthday: new Date('2000-01-01'),
			},
			numeroGuia: '12345',
		} as HospitalizationEntity;

		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);

		const result = await findHospitalizationGateway.findByCodigoGuia(
			10,
			'12345',
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true },
			where: {
				numeroGuia: '12345',
				enabled: 1,
				patient: {
					companyId: 10,
					enabled: 1,
				},
			},
		});

		expect(result).toEqual(
			HospitalizationMapper.toHospitalizationDomain(patientWayEntityMock),
		);
	});

	it('deve retornar nulo ao buscar por código da guia e nao bater', async () => {
		repositoryMock.findOne.mockResolvedValue(null);

		const result = await findHospitalizationGateway.findByCodigoGuia(
			10,
			'12345',
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true },
			where: {
				numeroGuia: '12345',
				enabled: 1,
				patient: {
					companyId: 10,
					enabled: 1,
				},
			},
		});

		expect(result).toEqual(null);
	});

	it('deve retornar um PatientWay ao buscar por nome e data de nascimento, excluindo codBeneficiario', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: {
				codBeneficiario: '*********',
				companyId: 3,
				enabled: 1,
				name: 'João Silva',
				birthday: new Date('2000-01-01'),
			},
		} as HospitalizationEntity;

		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);

		const result =
			await findHospitalizationGateway.findByNomeNascimento(censoDadosMock);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				patient: {
					name: 'João Silva',
					birthday: new Date('2000-01-01'),
					companyId: 3,
					enabled: 1,
					codBeneficiario: Not('*********'),
				},
				enabled: 1,
			},
		});

		expect(result).toEqual(
			HospitalizationMapper.toHospitalizationDomain(patientWayEntityMock),
		);
	});

	it('deve retornar um nulo ao buscar por nome e data de nascimento, excluindo codBeneficiario e nao bater', async () => {
		repositoryMock.findOne.mockResolvedValue(null);

		const result =
			await findHospitalizationGateway.findByNomeNascimento(censoDadosMock);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				patient: {
					name: 'João Silva',
					birthday: new Date('2000-01-01'),
					companyId: 3,
					enabled: 1,
					codBeneficiario: Not('*********'),
				},
				enabled: 1,
			},
		});

		expect(result).toEqual(null);
	});
	it('deve retornar um PatientWay ao buscar entre dois períodos de data', async () => {
		const dataEntrada = new Date('2024-01-01');
		const dataAlta = new Date('2024-12-31');

		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: { codBeneficiario: '*********', companyId: 3, enabled: 1 },
			admissionIn: new Date('2024-03-01'),
			admissionOut: new Date('2024-10-01'),
		} as HospitalizationEntity;

		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);
		const result = await findHospitalizationGateway.findBeetweenPeriods(
			dataEntrada,
			dataAlta,
			patientId,
			companyId,
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: {
				id: true,
				admissionIn: true,
				admissionOut: true,
				bedHospitals: true,
			},
			where: [
				{
					enabled: 1,
					admissionIn: LessThanOrEqual(dataEntrada),
					admissionOut: Or(IsNull(), MoreThanOrEqual(dataAlta)),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
				{
					enabled: 1,
					admissionIn: MoreThanOrEqual(dataEntrada),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
			],
			relations: ['bedHospitals'],
		});

		expect(result).toEqual(
			HospitalizationMapper.toHospitalizationDomain(patientWayEntityMock),
		);
	});

	it('deve retornar um nulo ao buscar entre dois períodos de data e nao bater', async () => {
		const dataEntrada = new Date('2024-01-01');
		const dataAlta = new Date('2024-12-31');

		repositoryMock.findOne.mockResolvedValue(null);

		const result = await findHospitalizationGateway.findBeetweenPeriods(
			dataEntrada,
			dataAlta,
			patientId,
			companyId,
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			select: {
				id: true,
				admissionIn: true,
				admissionOut: true,
				bedHospitals: true,
			},
			where: [
				{
					enabled: 1,
					admissionIn: LessThanOrEqual(dataEntrada),
					admissionOut: Or(IsNull(), MoreThanOrEqual(dataAlta)),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
				{
					enabled: 1,
					admissionIn: MoreThanOrEqual(dataEntrada),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
			],
			relations: ['bedHospitals'],
		});

		expect(result).toEqual(null);
	});

	it('deve retornar uma lista de PatientWay ao buscar pacientes ativos pelo codBeneficiario', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: {
				id: 3,
				companyId: 3,
				enabled: 1,
			},
		} as HospitalizationEntity;

		repositoryMock.find.mockResolvedValue([patientWayEntityMock]);

		const result =
			await findHospitalizationGateway.findActiveHospitalizationByPatient(3, 3);

		expect(repositoryMock.find).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true, bedHospitals: true },
			where: {
				patient: { id: 3, companyId: 3, enabled: 1 },
				enabled: 1,
				admissionOut: IsNull(),
				bedHospitals: { enabled: true },
			},
			relations: ['bedHospitals'],
		});

		expect(result).toEqual([patientWayEntityMock]);
	});

	it('deve retornar nulo ao buscar pacientes ativos pelo codBeneficiario e nao bater', async () => {
		repositoryMock.find.mockResolvedValue(null);

		const result =
			await findHospitalizationGateway.findActiveHospitalizationByPatient(3, 3);

		expect(repositoryMock.find).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true, bedHospitals: true },
			where: {
				patient: { id: 3, companyId: 3, enabled: 1 },
				enabled: 1,
				admissionOut: IsNull(),
				bedHospitals: { enabled: true },
			},
			relations: ['bedHospitals'],
		});

		expect(result).toEqual(null);
	});

	it('deve retornar nulo ao encontrar pacientes ativos com valores nulos no array', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: { codBeneficiario: '*********', companyId: 3, enabled: 1 },
		} as HospitalizationEntity;

		repositoryMock.find.mockResolvedValue([patientWayEntityMock, null]);

		const result =
			await findHospitalizationGateway.findActiveHospitalizationByPatient(
				123,
				3,
			);

		expect(repositoryMock.find).toHaveBeenCalledWith({
			select: { id: true, admissionIn: true, bedHospitals: true },
			where: {
				patient: { id: 123, companyId: 3, enabled: 1 },
				enabled: 1,
				admissionOut: IsNull(),
				bedHospitals: { enabled: true },
			},
			relations: ['bedHospitals'],
		});

		expect(result).toEqual([
			HospitalizationMapper.toHospitalizationDomain(patientWayEntityMock),
			null,
		]);
	});

	it('deve retornar um Hospitalization ao buscar por data de internacao e paciente', async () => {
		const patientWayEntityMock: HospitalizationEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			patient: { codBeneficiario: '*********', companyId: 3, enabled: 1 },
			admissionIn: new Date('2024-01-01'),
		} as HospitalizationEntity;

		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);

		const result = await findHospitalizationGateway.findByAdmissionInAndPatient(
			new Date('2024-01-01'),
			123,
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			where: {
				admissionIn: new Date('2024-01-01'),
				patient: { id: 123 },
				enabled: 1,
			},
		});

		expect(result).toEqual(
			HospitalizationMapper.toHospitalizationDomain(patientWayEntityMock),
		);
	});
	it('deve retornar null se nao encontrar patient way no findByAdmissionInAndPatient', async () => {
		const patientWayEntityMock: HospitalizationEntity = null;

		repositoryMock.findOne.mockResolvedValue(patientWayEntityMock);

		const result = await findHospitalizationGateway.findByAdmissionInAndPatient(
			new Date('2024-01-01'),
			123,
		);

		expect(repositoryMock.findOne).toHaveBeenCalledWith({
			where: {
				admissionIn: new Date('2024-01-01'),
				patient: { id: 123 },
				enabled: 1,
			},
		});

		expect(result).toEqual(null);
	});
});
