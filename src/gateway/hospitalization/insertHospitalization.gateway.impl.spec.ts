import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { InsertHospitalizationGatewayImpl } from './insertHospitalization.gateway.impl';

describe('InsertPatientWayGatewayImpl', () => {
	let insertPatientWayGateway: InsertHospitalizationGatewayImpl;
	let patientWayRepository: jest.Mocked<Repository<HospitalizationEntity>>;

	beforeEach(() => {
		patientWayRepository = {
			create: jest.fn(),
			insert: jest.fn().mockResolvedValue({
				identifiers: [{ id: 1 }],
			}),
		} as unknown as jest.Mocked<Repository<HospitalizationEntity>>;

		insertPatientWayGateway = new InsertHospitalizationGatewayImpl(
			patientWayRepository,
		);
	});

	it('deve criar uma entidade PatientWayEntity corretamente', () => {
		const patientWay: Hospitalization = new Hospitalization(
			1,
			new Date('2022-01-01T00:00:00Z'),
			new Date('2022-01-02T00:00:00Z'),
			new Date('2022-01-03T00:00:00Z'),
			1, // isAdmissionOutCenso
			1, // enabled
			0, // isNewborn
			1, // isBill
			'Hospitalar',
			new Date('2022-01-04T00:00:00Z'),
			new Date('2022-01-05T00:00:00Z'),
			123,
			new Date('2022-01-06T00:00:00Z'),
			undefined,
			'outPatTest',
			new Date('2022-01-07T00:00:00Z'),
			'altaPrevistaIAValue',
			1, // altaPrevistaIAMin
			2, // altaPrevistaIAMax
			3, // altaPrevistaMI4U
			'obs test',
			'Dr. Test',
			'123456789',
			'CRM123',
			'SP',
			'especialidade',
			'numeroGuiaTest',
			456, // userId
			1, // isEventoAdverso
			'eventoAdversoObs',
			1, // isInternacaoDiaAnterior
			'caraterTest',
			'caraterTipoTest',
			1, // hasPrenatal
			1, // isPregnant
			1, // isPuerperium
			'numeroSenhaTest',
			'clinicalHistoryTest',
			new Date('2022-01-08T00:00:00Z'),
			789, // supervisorId
			1, // palliativeCare
			111, // codigoMedicoResponsavel
			'conselhoTest',
			new Date('2022-01-09T00:00:00Z'),
			'codIntegrationTest',
			new Date('2022-01-10T00:00:00Z'),
			1000, // realCost
			'codigoSituacaoTissTest',
			'descricaoSituacaoTissTest',
			1, // longaDecisao
			0.5, // prob
			1, // isEditableRealCost
			1, // curtaPermanencia
			'outDoctorNameTest',
			'UFTest',
			'outDoctorCrmTest',
			'outDoctorSpecialityTest',
			1, // outDoctorIsResponsible
			'numeroRegistroTest',
			'procedenciaTest',
			new Date('2022-01-11T00:00:00Z'),
			999, // curtaPermanenciaUser
		);

		const patientWayEntity = new HospitalizationEntity();
		patientWayEntity.id = patientWay.id;
		patientWayEntity.created = patientWay.created;
		patientWayEntity.updated = patientWay.updated;
		patientWayEntity.admissionIn = patientWay.admissionIn;
		patientWayEntity.isAdmissionOutCenso = patientWay.isAdmissionOutCenso;
		patientWayEntity.admissionOut = patientWay.admissionOut;
		patientWayEntity.admissionOutCreated = patientWay.admissionOutCreated;
		patientWayEntity.admissionOutUserId = patientWay.admissionOutUserId;
		patientWayEntity.admissionOutCreatedFirstTime =
			patientWay.admissionOutCreatedFirstTime;
		patientWayEntity.patient = null;
		patientWayEntity.outPat = patientWay.outPat;
		patientWayEntity.altaPrevista = patientWay.altaPrevista;
		patientWayEntity.altaPrevistaIA = patientWay.altaPrevistaIA;
		patientWayEntity.altaPrevistaIAMin = patientWay.altaPrevistaIAMin;
		patientWayEntity.altaPrevistaIAMax = patientWay.altaPrevistaIAMax;
		patientWayEntity.altaPrevistaMI4U = patientWay.altaPrevistaMI4U;
		patientWayEntity.obs = patientWay.obs;
		patientWayEntity.nomeMedicoResponsavel = patientWay.nomeMedicoResponsavel;
		patientWayEntity.contatoMedicoResponsavel =
			patientWay.contatoMedicoResponsavel;
		patientWayEntity.crmMedicoResponsavel = patientWay.crmMedicoResponsavel;
		patientWayEntity.ufcrmMedicoResponsavel = patientWay.ufcrmMedicoResponsavel;
		patientWayEntity.especialidadeMedicoResponsavel =
			patientWay.especialidadeMedicoResponsavel;
		patientWayEntity.numeroGuia = patientWay.numeroGuia;
		patientWayEntity.userId = patientWay.userId;
		patientWayEntity.isEventoAdverso = patientWay.isEventoAdverso;
		patientWayEntity.eventoAdversoObs = patientWay.eventoAdversoObs;
		patientWayEntity.isInternacaoDiaAnterior =
			patientWay.isInternacaoDiaAnterior;
		patientWayEntity.carater = patientWay.carater;
		patientWayEntity.caraterTipo = patientWay.caraterTipo;
		patientWayEntity.hasPrenatal = patientWay.hasPrenatal;
		patientWayEntity.isPregnant = patientWay.isPregnant;
		patientWayEntity.isPuerperium = patientWay.isPuerperium;
		patientWayEntity.isNewborn = patientWay.isNewborn;
		patientWayEntity.enabled = patientWay.enabled;
		patientWayEntity.isCenso = 1;
		patientWayEntity.isBill = patientWay.isBill;
		patientWayEntity.numeroSenha = patientWay.numeroSenha;
		patientWayEntity.clinicalHistory = patientWay.clinicalHistory;
		patientWayEntity.dataEmissaoGuia = patientWay.dataEmissaoGuia;
		patientWayEntity.supervisorId = patientWay.supervisorId;
		patientWayEntity.regime = patientWay.regime;
		patientWayEntity.palliativeCare = patientWay.palliativeCare;
		patientWayEntity.codigoMedicoResponsavel =
			patientWay.codigoMedicoResponsavel;
		patientWayEntity.conselhoMedicoResponsavel =
			patientWay.conselhoMedicoResponsavel;
		patientWayEntity.dataHoraSolicitacao = patientWay.dataHoraSolicitacao;
		patientWayEntity.codIntegration = patientWay.codIntegration;
		patientWayEntity.dataAuthorizationGuide = patientWay.dataAuthorizationGuide;
		patientWayEntity.realCost = patientWay.realCost;
		patientWayEntity.codigoSituacaoTiss = patientWay.codigoSituacaoTiss;
		patientWayEntity.descricaoSituacaoTiss = patientWay.descricaoSituacaoTiss;
		patientWayEntity.longaDecisao = patientWay.longaDecisao;
		patientWayEntity.prob = patientWay.prob;
		patientWayEntity.isEditableRealCost = patientWay.isEditableRealCost;
		patientWayEntity.curtaPermanencia = patientWay.curtaPermanencia;
		patientWayEntity.outDoctorName = patientWay.outDoctorName;
		patientWayEntity.outDoctorUfCrm = patientWay.outDoctorUfCrm;
		patientWayEntity.outDoctorCrm = patientWay.outDoctorCrm;
		patientWayEntity.outDoctorSpeciality = patientWay.outDoctorSpeciality;
		patientWayEntity.outDoctorIsResponsible = patientWay.outDoctorIsResponsible;
		patientWayEntity.numeroRegistro = patientWay.numeroRegistro;
		patientWayEntity.procedencia = patientWay.procedencia;
		patientWayEntity.curtaPermanenciaDate = patientWay.curtaPermanenciaDate;
		patientWayEntity.curtaPermanenciaUser = patientWay.curtaPermanenciaUser;

		patientWayRepository.create.mockReturnValue(patientWayEntity);

		const result =
			insertPatientWayGateway['createHospitalizationEntity'](patientWay);

		expect(patientWayRepository.create).toHaveBeenCalledWith(
			expect.objectContaining({
				id: patientWay.id,
				regime: patientWay.regime,
			}),
		);
		expect(result).toEqual(patientWayEntity);
	});

	it('deve inserir um PatientWay corretamente', async () => {
		const patientWay: Hospitalization = new Hospitalization(
			1,
			new Date('2022-01-01T00:00:00Z'),
			new Date('2022-01-02T00:00:00Z'),
			new Date('2022-01-03T00:00:00Z'),
			1, // isAdmissionOutCenso
			1, // enabled
			0, // isNewborn
			1, // isBill
			'Hospitalar',
			new Date('2022-01-04T00:00:00Z'),
			new Date('2022-01-05T00:00:00Z'),
			123,
			new Date('2022-01-06T00:00:00Z'),
			undefined,
			'outPatTest',
			new Date('2022-01-07T00:00:00Z'),
			'altaPrevistaIAValue',
			1, // altaPrevistaIAMin
			2, // altaPrevistaIAMax
			3, // altaPrevistaMI4U
			'obs test',
			'Dr. Test',
			'123456789',
			'CRM123',
			'SP',
			'especialidade',
			'numeroGuiaTest',
			456, // userId
			1, // isEventoAdverso
			'eventoAdversoObs',
			1, // isInternacaoDiaAnterior
			'caraterTest',
			'caraterTipoTest',
			1, // hasPrenatal
			1, // isPregnant
			1, // isPuerperium
			'numeroSenhaTest',
			'clinicalHistoryTest',
			new Date('2022-01-08T00:00:00Z'),
			789, // supervisorId
			1, // palliativeCare
			111, // codigoMedicoResponsavel
			'conselhoTest',
			new Date('2022-01-09T00:00:00Z'),
			'codIntegrationTest',
			new Date('2022-01-10T00:00:00Z'),
			1000, // realCost
			'codigoSituacaoTissTest',
			'descricaoSituacaoTissTest',
			1, // longaDecisao
			0.5, // prob
			1, // isEditableRealCost
			1, // curtaPermanencia
			'outDoctorNameTest',
			'UFTest',
			'outDoctorCrmTest',
			'outDoctorSpecialityTest',
			1, // outDoctorIsResponsible
			'numeroRegistroTest',
			'procedenciaTest',
			new Date('2022-01-11T00:00:00Z'),
			999, // curtaPermanenciaUser
		);

		const patientWayEntity = new HospitalizationEntity();
		patientWayEntity.id = patientWay.id;
		patientWayEntity.created = patientWay.created;
		patientWayEntity.updated = patientWay.updated;
		patientWayEntity.admissionIn = patientWay.admissionIn;
		patientWayEntity.isAdmissionOutCenso = patientWay.isAdmissionOutCenso;
		patientWayEntity.admissionOut = patientWay.admissionOut;
		patientWayEntity.admissionOutCreated = patientWay.admissionOutCreated;
		patientWayEntity.admissionOutUserId = patientWay.admissionOutUserId;
		patientWayEntity.admissionOutCreatedFirstTime =
			patientWay.admissionOutCreatedFirstTime;
		patientWayEntity.patient = null;
		patientWayEntity.outPat = patientWay.outPat;
		patientWayEntity.altaPrevista = patientWay.altaPrevista;
		patientWayEntity.altaPrevistaIA = patientWay.altaPrevistaIA;
		patientWayEntity.altaPrevistaIAMin = patientWay.altaPrevistaIAMin;
		patientWayEntity.altaPrevistaIAMax = patientWay.altaPrevistaIAMax;
		patientWayEntity.altaPrevistaMI4U = patientWay.altaPrevistaMI4U;
		patientWayEntity.obs = patientWay.obs;
		patientWayEntity.nomeMedicoResponsavel = patientWay.nomeMedicoResponsavel;
		patientWayEntity.contatoMedicoResponsavel =
			patientWay.contatoMedicoResponsavel;
		patientWayEntity.crmMedicoResponsavel = patientWay.crmMedicoResponsavel;
		patientWayEntity.ufcrmMedicoResponsavel = patientWay.ufcrmMedicoResponsavel;
		patientWayEntity.especialidadeMedicoResponsavel =
			patientWay.especialidadeMedicoResponsavel;
		patientWayEntity.numeroGuia = patientWay.numeroGuia;
		patientWayEntity.userId = patientWay.userId;
		patientWayEntity.isEventoAdverso = patientWay.isEventoAdverso;
		patientWayEntity.eventoAdversoObs = patientWay.eventoAdversoObs;
		patientWayEntity.isInternacaoDiaAnterior =
			patientWay.isInternacaoDiaAnterior;
		patientWayEntity.carater = patientWay.carater;
		patientWayEntity.caraterTipo = patientWay.caraterTipo;
		patientWayEntity.hasPrenatal = patientWay.hasPrenatal;
		patientWayEntity.isPregnant = patientWay.isPregnant;
		patientWayEntity.isPuerperium = patientWay.isPuerperium;
		patientWayEntity.isNewborn = patientWay.isNewborn;
		patientWayEntity.enabled = patientWay.enabled;
		patientWayEntity.isCenso = 1;
		patientWayEntity.isBill = patientWay.isBill;
		patientWayEntity.numeroSenha = patientWay.numeroSenha;
		patientWayEntity.clinicalHistory = patientWay.clinicalHistory;
		patientWayEntity.dataEmissaoGuia = patientWay.dataEmissaoGuia;
		patientWayEntity.supervisorId = patientWay.supervisorId;
		patientWayEntity.regime = patientWay.regime;
		patientWayEntity.palliativeCare = patientWay.palliativeCare;
		patientWayEntity.codigoMedicoResponsavel =
			patientWay.codigoMedicoResponsavel;
		patientWayEntity.conselhoMedicoResponsavel =
			patientWay.conselhoMedicoResponsavel;
		patientWayEntity.dataHoraSolicitacao = patientWay.dataHoraSolicitacao;
		patientWayEntity.codIntegration = patientWay.codIntegration;
		patientWayEntity.dataAuthorizationGuide = patientWay.dataAuthorizationGuide;
		patientWayEntity.realCost = patientWay.realCost;
		patientWayEntity.codigoSituacaoTiss = patientWay.codigoSituacaoTiss;
		patientWayEntity.descricaoSituacaoTiss = patientWay.descricaoSituacaoTiss;
		patientWayEntity.longaDecisao = patientWay.longaDecisao;
		patientWayEntity.prob = patientWay.prob;
		patientWayEntity.isEditableRealCost = patientWay.isEditableRealCost;
		patientWayEntity.curtaPermanencia = patientWay.curtaPermanencia;
		patientWayEntity.outDoctorName = patientWay.outDoctorName;
		patientWayEntity.outDoctorUfCrm = patientWay.outDoctorUfCrm;
		patientWayEntity.outDoctorCrm = patientWay.outDoctorCrm;
		patientWayEntity.outDoctorSpeciality = patientWay.outDoctorSpeciality;
		patientWayEntity.outDoctorIsResponsible = patientWay.outDoctorIsResponsible;
		patientWayEntity.numeroRegistro = patientWay.numeroRegistro;
		patientWayEntity.procedencia = patientWay.procedencia;
		patientWayEntity.curtaPermanenciaDate = patientWay.curtaPermanenciaDate;
		patientWayEntity.curtaPermanenciaUser = patientWay.curtaPermanenciaUser;

		patientWayRepository.create.mockReturnValue(patientWayEntity);

		const result = await insertPatientWayGateway.insert(patientWay);

		expect(patientWayRepository.create).toHaveBeenCalledWith(
			expect.objectContaining({
				id: patientWay.id,
				regime: patientWay.regime,
			}),
		);
		expect(patientWayRepository.insert).toHaveBeenCalledWith(patientWayEntity);
		expect(result).toEqual(1);
	});
});
