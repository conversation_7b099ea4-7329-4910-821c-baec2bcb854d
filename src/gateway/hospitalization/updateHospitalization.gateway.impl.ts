import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';

export class UpdateHospitalizationGatewayImpl
	implements UpdateHospitalizationGateway
{
	constructor(
		private readonly HospitalizationRepository: Repository<HospitalizationEntity>,
	) {}

	public async update(
		id: number,
		hospitalization: Partial<Hospitalization>,
	): Promise<void> {
		await this.HospitalizationRepository.update(id, hospitalization);
	}
}
