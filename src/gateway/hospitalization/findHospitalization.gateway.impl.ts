import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import {
	IsNull,
	LessThanOrEqual,
	MoreThanOrEqual,
	Not,
	Or,
	Repository,
} from 'typeorm';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { CensoDados } from 'src/core/domain/CensoDados';
import { HospitalizationMapper } from 'src/shared/mapper/hospitalization/HospitalizationMapper';

export class FindHospitalizationGatewayImpl
	implements FindHospitalizationGateway
{
	constructor(
		private readonly hospitalizationRepository: Repository<HospitalizationEntity>,
	) {}

	public async findByCodBeneficiario(
		censoDados: CensoDados,
	): Promise<Hospitalization> {
		const patientWay = this.hospitalizationRepository.findOne({
			select: { id: true, admissionIn: true },
			where: {
				patient: {
					birthday: censoDados.dtNascimento,
					codBeneficiario: censoDados.codBeneficiario,
					companyId: censoDados.companyId,
					enabled: 1,
				},
				enabled: 1,
				admissionOut: IsNull(),
			},
		});

		if (!(await patientWay)) return null;
		return HospitalizationMapper.toHospitalizationDomain(await patientWay);
	}

	public async findByNomeNascimento(
		censoDados: CensoDados,
	): Promise<Hospitalization> {
		const patientWay = await this.hospitalizationRepository.findOne({
			select: { id: true },
			where: {
				patient: {
					birthday: censoDados.dtNascimento,
					name: censoDados.nomeBeneficiario,
					companyId: censoDados.companyId,
					enabled: 1,
					codBeneficiario: Not(censoDados.codBeneficiario),
				},
				enabled: 1,
			},
		});
		if (!patientWay) return null;

		return HospitalizationMapper.toHospitalizationDomain(patientWay);
	}

	public async findByCodigoGuia(
		companyId: number,
		codigoGuia: string,
	): Promise<Hospitalization> {
		const patientWay = this.hospitalizationRepository.findOne({
			select: { id: true, admissionIn: true },
			where: {
				patient: {
					enabled: 1,
					companyId: companyId,
				},
				numeroGuia: codigoGuia,
				enabled: 1,
			},
		});
		if (!(await patientWay)) return null;
		return HospitalizationMapper.toHospitalizationDomain(await patientWay);
	}

	public async findBeetweenPeriods(
		dataEntrada: Date,
		dataAlta: Date,
		patientId: number,
		companyId: number,
	): Promise<Hospitalization> {
		const patientWay = await this.hospitalizationRepository.findOne({
			select: {
				id: true,
				admissionIn: true,
				admissionOut: true,
				bedHospitals: true,
			},
			where: [
				{
					enabled: 1,
					admissionIn: LessThanOrEqual(dataEntrada),
					admissionOut: Or(IsNull(), MoreThanOrEqual(dataAlta)),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
				{
					enabled: 1,
					admissionIn: MoreThanOrEqual(dataEntrada),
					patient: {
						id: patientId,
						companyId: companyId,
						enabled: 1,
					},
					bedHospitals: { enabled: true },
				},
			],
			relations: ['bedHospitals'],
		});

		if (!patientWay) return null;
		return HospitalizationMapper.toHospitalizationDomain(patientWay);
	}

	public async findActiveHospitalizationByPatient(
		patientId: number,
		companyId: number,
	): Promise<Hospitalization[]> {
		const patientWay = this.hospitalizationRepository.find({
			select: { id: true, admissionIn: true, bedHospitals: true },
			where: {
				patient: {
					id: patientId,
					companyId: companyId,
					enabled: 1,
				},
				enabled: 1,
				admissionOut: IsNull(),
				bedHospitals: { enabled: true },
			},
			relations: ['bedHospitals'],
		});
		if (!(await patientWay)) return null;

		return (await patientWay).map((censo) => {
			if (!censo) return null;
			return HospitalizationMapper.toHospitalizationDomain(censo);
		});
	}

	public async findByAdmissionInAndPatient(
		admissionIn: Date,
		id: number,
	): Promise<Hospitalization> {
		const patientWay = await this.hospitalizationRepository.findOne({
			where: { admissionIn, patient: { id }, enabled: 1 },
		});

		if (!patientWay) return null;

		return HospitalizationMapper.toHospitalizationDomain(patientWay);
	}
}
