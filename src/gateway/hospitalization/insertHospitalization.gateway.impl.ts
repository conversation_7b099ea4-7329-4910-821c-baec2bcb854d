import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';

export class InsertHospitalizationGatewayImpl
	implements InsertHospitalizationGateway
{
	constructor(
		private readonly hospitalizationRepository: Repository<HospitalizationEntity>,
	) {}

	public async insert(hospitalization: Hospitalization): Promise<number> {
		const createdHospitalization =
			this.createHospitalizationEntity(hospitalization);
		const insertedHospitalizationId = (
			await this.hospitalizationRepository.insert(createdHospitalization)
		).identifiers[0].id;
		return insertedHospitalizationId;
	}

	private createHospitalizationEntity(
		Hospitalization: Hospitalization,
	): HospitalizationEntity {
		return this.hospitalizationRepository.create({
			id: Hospitalization.id,
			created: Hospitalization.created,
			updated: Hospitalization.updated,
			admissionIn: Hospitalization.admissionIn,
			isAdmissionOutCenso: Hospitalization.isAdmissionOutCenso,
			admissionOut: Hospitalization.admissionOut,
			admissionOutCreated: Hospitalization.admissionOutCreated,
			admissionOutUserId: Hospitalization.admissionOutUserId,
			admissionOutCreatedFirstTime:
				Hospitalization.admissionOutCreatedFirstTime,
			patient: Hospitalization.patient,
			outPat: Hospitalization.outPat,
			altaPrevista: Hospitalization.altaPrevista,
			altaPrevistaIA: Hospitalization.altaPrevistaIA,
			altaPrevistaIAMin: Hospitalization.altaPrevistaIAMin,
			altaPrevistaIAMax: Hospitalization.altaPrevistaIAMax,
			altaPrevistaMI4U: Hospitalization.altaPrevistaMI4U,
			obs: Hospitalization.obs,
			nomeMedicoResponsavel: Hospitalization.nomeMedicoResponsavel,
			contatoMedicoResponsavel: Hospitalization.contatoMedicoResponsavel,
			crmMedicoResponsavel: Hospitalization.crmMedicoResponsavel,
			ufcrmMedicoResponsavel: Hospitalization.ufcrmMedicoResponsavel,
			especialidadeMedicoResponsavel:
				Hospitalization.especialidadeMedicoResponsavel,
			numeroGuia: Hospitalization.numeroGuia,
			userId: Hospitalization.userId,
			isEventoAdverso: Hospitalization.isEventoAdverso,
			eventoAdversoObs: Hospitalization.eventoAdversoObs,
			isInternacaoDiaAnterior: Hospitalization.isInternacaoDiaAnterior,
			carater: Hospitalization.carater,
			caraterTipo: Hospitalization.caraterTipo,
			hasPrenatal: Hospitalization.hasPrenatal,
			isPregnant: Hospitalization.isPregnant,
			isPuerperium: Hospitalization.isPuerperium,
			isNewborn: Hospitalization.isNewborn,
			enabled: Hospitalization.enabled,
			isCenso: 1,
			isBill: Hospitalization.isBill,
			numeroSenha: Hospitalization.numeroSenha,
			clinicalHistory: Hospitalization.clinicalHistory,
			dataEmissaoGuia: Hospitalization.dataEmissaoGuia,
			supervisorId: Hospitalization.supervisorId,
			regime: Hospitalization.regime,
			palliativeCare: Hospitalization.palliativeCare,
			codigoMedicoResponsavel: Hospitalization.codigoMedicoResponsavel,
			conselhoMedicoResponsavel: Hospitalization.conselhoMedicoResponsavel,
			dataHoraSolicitacao: Hospitalization.dataHoraSolicitacao,
			codIntegration: Hospitalization.codIntegration,
			dataAuthorizationGuide: Hospitalization.dataAuthorizationGuide,
			realCost: Hospitalization.realCost,
			codigoSituacaoTiss: Hospitalization.codigoSituacaoTiss,
			descricaoSituacaoTiss: Hospitalization.descricaoSituacaoTiss,
			longaDecisao: Hospitalization.longaDecisao,
			prob: Hospitalization.prob,
			isEditableRealCost: Hospitalization.isEditableRealCost,
			curtaPermanencia: Hospitalization.curtaPermanencia,
			outDoctorName: Hospitalization.outDoctorName,
			outDoctorUfCrm: Hospitalization.outDoctorUfCrm,
			outDoctorCrm: Hospitalization.outDoctorCrm,
			outDoctorSpeciality: Hospitalization.outDoctorSpeciality,
			outDoctorIsResponsible: Hospitalization.outDoctorIsResponsible,
			numeroRegistro: Hospitalization.numeroRegistro,
			procedencia: Hospitalization.procedencia,
			curtaPermanenciaDate: Hospitalization.curtaPermanenciaDate,
			curtaPermanenciaUser: Hospitalization.curtaPermanenciaUser,
		});
	}
}
