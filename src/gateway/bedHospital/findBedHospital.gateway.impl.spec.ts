import { BedHospital } from 'src/core/domain/BedHospital';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { FindBedHospitalGatewayImpl } from './findBedHospital.gateway.impl';
import { Repository } from 'typeorm';
import { BedHospitalMapper } from 'src/shared/mapper/bedHospital/BedHospitalMapper';

describe('FindBedHospitalGatewayImpl', () => {
	let findBedHospitalGateway: FindBedHospitalGatewayImpl;
	let bedHospitalRepository: jest.Mocked<Repository<BedHospitalEntity>>;

	beforeEach(() => {
		bedHospitalRepository = {
			findOne: jest.fn(),
		} as unknown as jest.Mocked<Repository<BedHospitalEntity>>;

		findBedHospitalGateway = new FindBedHospitalGatewayImpl(
			bedHospitalRepository,
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should find bed hospital by admission in and hospitalization', async () => {
		const admissionIn = new Date();
		const hospitalizationId = 1;
		const bedHospitalEntity: BedHospitalEntity = {
			id: 1,
			created: new Date(),
			updated: new Date(),
			changed: new Date(),
			admissionIn: new Date(),
			admissionOut: new Date(),
			patientWayId: 1,
			bedNumber: '1',
			speciality: '1',
			accommodation: '1',
			accommodationCustom: '1',
			hospital: { id: 1 },
			hospitalId: 1,
			userId: 1,
			isCenso: true,
			isTransfer: true,
			price: 1,
			enabled: true,
			accommodationIsolation: '1',
			codIntegration: 1,
		} as BedHospitalEntity;
		const bedHospitalDomain: BedHospital = {
			id: 1,
			created: new Date(),
			admissionIn: new Date(),
			accommodation: '1',
			isCenso: true,
			isTransfer: true,
			price: 1,
			enabled: true,
			hospital: { id: 1 },
			updated: new Date(),
			changed: new Date(),
			admissionOut: new Date(),
			patientWayId: 1,
			bedNumber: '1',
			speciality: '1',
			accommodationCustom: '1',
			userId: 1,
			accommodationIsolation: '1',
			codIntegration: 1,
		} as BedHospital;
		bedHospitalRepository.findOne.mockResolvedValue(bedHospitalEntity);
		jest
			.spyOn(BedHospitalMapper, 'toBedHospitalDomain')
			.mockReturnValue(bedHospitalDomain);

		const result =
			await findBedHospitalGateway.findByAdmissionInAndHospitalization(
				admissionIn,
				hospitalizationId,
			);

		expect(bedHospitalRepository.findOne).toHaveBeenCalledWith({
			where: {
				admissionIn: admissionIn,
				patientWayId: hospitalizationId,
				enabled: true,
			},
		});
		expect(result).toEqual(bedHospitalDomain);
	});

	it('should return null when bed hospital is not found', async () => {
		const admissionIn = new Date();
		const hospitalizationId = 1;
		bedHospitalRepository.findOne.mockResolvedValue(null);

		const result =
			await findBedHospitalGateway.findByAdmissionInAndHospitalization(
				admissionIn,
				hospitalizationId,
			);

		expect(bedHospitalRepository.findOne).toHaveBeenCalledWith({
			where: {
				admissionIn: admissionIn,
				patientWayId: hospitalizationId,
				enabled: true,
			},
		});
		expect(result).toEqual(null);
	});
});
