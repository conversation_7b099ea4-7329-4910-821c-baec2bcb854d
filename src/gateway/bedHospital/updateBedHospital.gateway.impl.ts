import { UpdateBedHospitalGateway } from 'src/core/application/gateway/bedHospital/updateBedHospital.gateway';
import { Repository, UpdateResult } from 'typeorm';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { BedHospital } from 'src/core/domain/BedHospital';

export class UpdateBedHospitalGatewayImpl implements UpdateBedHospitalGateway {
	constructor(
		private readonly bedHospitalRepository: Repository<BedHospitalEntity>,
	) {}

	public async update(
		id: number,
		bedHospital: Partial<BedHospital>,
	): Promise<UpdateResult> {
		return await this.bedHospitalRepository.update(id, bedHospital);
	}
}
