import { Repository } from 'typeorm';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { BedHospital } from 'src/core/domain/BedHospital';
import { UpdateBedHospitalGatewayImpl } from './updateBedHospital.gateway.impl';

describe('UpdateBedHospitalGatewayImpl', () => {
	let updateBedHospitalGateway: UpdateBedHospitalGatewayImpl;
	let bedHospitalRepository: jest.Mocked<Repository<BedHospitalEntity>>;

	beforeEach(() => {
		bedHospitalRepository = {
			update: jest.fn(),
		} as unknown as jest.Mocked<Repository<BedHospitalEntity>>;

		updateBedHospitalGateway = new UpdateBedHospitalGatewayImpl(
			bedHospitalRepository,
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should update bed hospital', async () => {
		const id = 1;
		const bedHospital: Partial<BedHospital> = {
			admissionOut: new Date(),
		};

		await updateBedHospitalGateway.update(id, bedHospital);

		expect(bedHospitalRepository.update).toHaveBeenCalledWith(id, bedHospital);
	});
});
