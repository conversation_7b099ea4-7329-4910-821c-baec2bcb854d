import { InsertBedHospitalGateway } from 'src/core/application/gateway/bedHospital/insertBedHospital.gateway';
import { BedHospital } from 'src/core/domain/BedHospital';
import { Repository } from 'typeorm';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { BedHospitalMapper } from 'src/shared/mapper/bedHospital/BedHospitalMapper';

export class InsertBedHospitalGatewayImpl implements InsertBedHospitalGateway {
	constructor(private bedHospitalRepository: Repository<BedHospitalEntity>) {}

	public async insertBedHospital(
		bedHospital: BedHospital,
	): Promise<BedHospital> {
		const bedHospitalEntity = this.createBedHospital(bedHospital);
		const result = await this.bedHospitalRepository.save(bedHospitalEntity);
		return BedHospitalMapper.toBedHospitalDomain(result);
	}

	private createBedHospital(bedHospital: BedHospital): BedHospitalEntity {
		return this.bedHospitalRepository.create({
			id: bedHospital.id,
			created: bedHospital.created,
			admissionIn: bedHospital.admissionIn,
			accommodation: bedHospital.accommodation,
			isCenso: bedHospital.isCenso,
			isTransfer: bedHospital.isTransfer,
			price: bedHospital.price,
			enabled: bedHospital.enabled,
			accommodationCustom: bedHospital.accommodationCustom,
			accommodationIsolation: bedHospital.accommodationIsolation,
			admissionOut: bedHospital.admissionOut,
			bedNumber: bedHospital.bedNumber,
			changed: bedHospital.changed,
			codIntegration: bedHospital.codIntegration,
			hospital: bedHospital.hospital,
			hospitalId: bedHospital.hospital.id,
			patientWayId: bedHospital.patientWayId,
			speciality: bedHospital.speciality,
			updated: bedHospital.updated,
			userId: bedHospital.userId,
		});
	}
}
