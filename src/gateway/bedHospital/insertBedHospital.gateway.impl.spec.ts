import { BedHospital } from 'src/core/domain/BedHospital';
import { Repository } from 'typeorm';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { BedHospitalMapper } from 'src/shared/mapper/bedHospital/BedHospitalMapper';
import { InsertBedHospitalGatewayImpl } from './insertBedHospital.gateway.impl';

describe('InsertBedHospitalGatewayImpl', () => {
	let gateway: InsertBedHospitalGatewayImpl;
	let bedHospitalRepository: jest.Mocked<Repository<BedHospitalEntity>>;

	const bedHospitalDomain: BedHospital = {
		id: null,
		created: new Date('2023-03-01T00:00:00Z'),
		admissionIn: new Date('2023-03-10T00:00:00Z'),
		accommodation: 'Teste Acomodação',
		isCenso: true,
		isTransfer: false,
		price: 100,
		enabled: true,
		hospital: 10,
		accommodationCustom: '',
		accommodationIsolation: '',
		admissionOut: new Date('2023-03-01T00:00:00Z'),
		bedNumber: '',
		changed: new Date('2023-03-01T00:00:00Z'),
		codIntegration: null,
		patientWayId: 1,
		speciality: '',
		updated: null,
		userId: 1,
	} as BedHospital;

	const createdEntity = {
		id: bedHospitalDomain.id,
		created: bedHospitalDomain.created,
		admissionIn: bedHospitalDomain.admissionIn,
		accommodation: bedHospitalDomain.accommodation,
		isCenso: bedHospitalDomain.isCenso,
		isTransfer: bedHospitalDomain.isTransfer,
		price: bedHospitalDomain.price,
		enabled: bedHospitalDomain.enabled,
		hospital: bedHospitalDomain.hospital,
		accommodationCustom: bedHospitalDomain.accommodationCustom,
		accommodationIsolation: bedHospitalDomain.accommodationIsolation,
		admissionOut: bedHospitalDomain.admissionOut,
		bedNumber: bedHospitalDomain.bedNumber,
		changed: bedHospitalDomain.changed,
		codIntegration: bedHospitalDomain.codIntegration,
		patientWayId: bedHospitalDomain.patientWayId,
		speciality: bedHospitalDomain.speciality,
		updated: bedHospitalDomain.updated,
		userId: bedHospitalDomain.userId,
	};

	const savedEntity = {
		...createdEntity,
		id: 1,
	};

	const mappedDomain: BedHospital = {
		...bedHospitalDomain,
		id: 1,
	};

	beforeEach(() => {
		bedHospitalRepository = {
			create: jest.fn().mockImplementation((entity) => entity),
			save: jest.fn().mockResolvedValue(savedEntity),
		} as unknown as jest.Mocked<Repository<BedHospitalEntity>>;

		gateway = new InsertBedHospitalGatewayImpl(bedHospitalRepository);

		jest
			.spyOn(BedHospitalMapper, 'toBedHospitalDomain')
			.mockReturnValue(mappedDomain);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('deve criar a entidade de BedHospital, salvá-la e retornar o domínio mapeado', async () => {
		const result = await gateway.insertBedHospital(bedHospitalDomain);

		expect(bedHospitalRepository.create).toHaveBeenCalledWith({
			id: bedHospitalDomain.id,
			created: bedHospitalDomain.created,
			admissionIn: bedHospitalDomain.admissionIn,
			accommodation: bedHospitalDomain.accommodation,
			isCenso: bedHospitalDomain.isCenso,
			isTransfer: bedHospitalDomain.isTransfer,
			price: bedHospitalDomain.price,
			enabled: bedHospitalDomain.enabled,
			hospital: bedHospitalDomain.hospital,
			accommodationCustom: bedHospitalDomain.accommodationCustom,
			accommodationIsolation: bedHospitalDomain.accommodationIsolation,
			admissionOut: bedHospitalDomain.admissionOut,
			bedNumber: bedHospitalDomain.bedNumber,
			changed: bedHospitalDomain.changed,
			codIntegration: bedHospitalDomain.codIntegration,
			patientWayId: bedHospitalDomain.patientWayId,
			speciality: bedHospitalDomain.speciality,
			updated: bedHospitalDomain.updated,
			userId: bedHospitalDomain.userId,
		});

		expect(result.patientWayId).not.toBe(null);
		expect(result.hospital).not.toBe(null);
		expect(bedHospitalRepository.save).toHaveBeenCalledWith(createdEntity);
		expect(BedHospitalMapper.toBedHospitalDomain).toHaveBeenCalledWith(
			savedEntity,
		);

		expect(result).toEqual(mappedDomain);
	});
});
