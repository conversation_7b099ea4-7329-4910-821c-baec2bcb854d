import { FindBedHospitalGateway } from 'src/core/application/gateway/bedHospital/findBedHospital.gateway';
import { BedHospital } from 'src/core/domain/BedHospital';
import { Repository } from 'typeorm';
import { BedHospitalEntity } from '../entities/bedHospital.entity';
import { BedHospitalMapper } from 'src/shared/mapper/bedHospital/BedHospitalMapper';

export class FindBedHospitalGatewayImpl implements FindBedHospitalGateway {
	constructor(
		private readonly bedHospitalRepository: Repository<BedHospitalEntity>,
	) {}

	public async findByAdmissionInAndHospitalization(
		admissionIn: Date,
		hospitalizationId: number,
	): Promise<BedHospital> {
		const bedHospital = await this.bedHospitalRepository.findOne({
			where: {
				admissionIn: admissionIn,
				patientWayId: hospitalizationId,
				enabled: true,
			},
		});
		if (!bedHospital) return null;
		return BedHospitalMapper.toBedHospitalDomain(bedHospital);
	}
}
