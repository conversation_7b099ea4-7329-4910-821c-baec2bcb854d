import { InsertGuideDailyGateway } from 'src/core/application/gateway/guideDaily/insertGuideDaily.gateway';
import { GuideDaily } from 'src/core/domain/GuideDaily';
import { Repository } from 'typeorm';
import { GuideDailyEntity } from '../entities/guideDaily.entity';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { GuideDailyMapper } from 'src/shared/mapper/guideDaily/GuideDailyMapper';

export class InsertGuideDailyGatewayImpl implements InsertGuideDailyGateway {
	constructor(
		private readonly guideDailyRepository: Repository<GuideDailyEntity>,
	) {}

	public async insertGuideDaily(guideDaily: GuideDaily): Promise<GuideDaily> {
		const guideDailyEntity = this.createGuideDaily(guideDaily);
		return GuideDailyMapper.toDomain(guideDailyEntity);
	}

	private createGuideDaily(guideDaily: GuideDaily): GuideDailyEntity {
		return this.guideDailyRepository.create({
			id: guideDaily.id,
			guide: GuideMapper.toEntity(guideDaily.guide),
			accommodation: guideDaily.accommodation,
			qtdeDaysRequested: guideDaily.qtdeDaysRequested,
			qtdeDaysAuthorized: guideDaily.qtdeDaysAuthorized,
			qtdeDaysDenied: guideDaily.qtdeDaysDenied,
			isCenso: guideDaily.isCenso,
			userId: guideDaily.userId,
			price: guideDaily.price,
			guideRequest: guideDaily.guideRequestId,
			enabled: guideDaily.enabled,
			dateStart: guideDaily.dateStart,
			dateFinish: guideDaily.dateFinish,
			qtdeDays: guideDaily.qtdeDays,
			aux: guideDaily.aux,
			auxId: guideDaily.auxId,
			codIntegration: guideDaily.codIntegration,
			updated: guideDaily.updated,
		});
	}
}
