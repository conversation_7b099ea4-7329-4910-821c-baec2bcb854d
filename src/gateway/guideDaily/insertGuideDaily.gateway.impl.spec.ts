import { Repository } from 'typeorm';
import { InsertGuideDailyGatewayImpl } from '../../gateway/guideDaily/insertGuideDaily.gateway.impl';
import { GuideDaily } from 'src/core/domain/GuideDaily';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { GuideDailyMapper } from 'src/shared/mapper/guideDaily/GuideDailyMapper';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { GuideEntity } from '../entities/guide.entity';

describe('InsertGuideDailyGatewayImpl', () => {
	let gateway: InsertGuideDailyGatewayImpl;
	let repositoryMock: Partial<Repository<GuideDailyEntity>>;

	const dummyGuideDaily = new GuideDaily(
		null,
		{ id: 1 },
		'Accommodation Test',
		1,
		2,
		3,
		0,
		123,
		100.0,
		10,
		1,
		new Date('2023-01-01T00:00:00Z'),
		new Date('2023-01-02T00:00:00Z'),
		4,
		0,
		11,
		'integration',
		new Date('2023-01-03T00:00:00Z'),
	);

	const dummyGuideDailyEntity: GuideDailyEntity = {
		id: dummyGuideDaily.id,
		guide: { id: 1 } as GuideEntity,
		accommodation: dummyGuideDaily.accommodation,
		qtdeDaysRequested: dummyGuideDaily.qtdeDaysRequested,
		qtdeDaysAuthorized: dummyGuideDaily.qtdeDaysAuthorized,
		qtdeDaysDenied: dummyGuideDaily.qtdeDaysDenied,
		isCenso: dummyGuideDaily.isCenso,
		userId: dummyGuideDaily.userId,
		price: dummyGuideDaily.price,
		guideRequest: dummyGuideDaily.guideRequestId,
		enabled: dummyGuideDaily.enabled,
		dateStart: dummyGuideDaily.dateStart,
		dateFinish: dummyGuideDaily.dateFinish,
		qtdeDays: dummyGuideDaily.qtdeDays,
		aux: dummyGuideDaily.aux,
		auxId: dummyGuideDaily.auxId,
		codIntegration: dummyGuideDaily.codIntegration,
		updated: dummyGuideDaily.updated,
	} as GuideDailyEntity;

	beforeEach(() => {
		repositoryMock = {
			create: jest.fn().mockReturnValue(dummyGuideDailyEntity),
		};

		jest
			.spyOn(GuideMapper, 'toEntity')
			.mockReturnValue({ id: 1 } as GuideEntity);

		jest
			.spyOn(GuideDailyMapper, 'toDomain')
			.mockReturnValue(
				new GuideDaily(
					456,
					{ id: 1 },
					'Accommodation Test',
					1,
					2,
					3,
					0,
					123,
					100.0,
					10,
					1,
					new Date('2023-01-01T00:00:00Z'),
					new Date('2023-01-02T00:00:00Z'),
					4,
					0,
					11,
					'integration',
					new Date('2023-01-03T00:00:00Z'),
				),
			);

		gateway = new InsertGuideDailyGatewayImpl(
			repositoryMock as Repository<GuideDailyEntity>,
		);
	});

	it('deve chamar repository.create com os parâmetros corretos e retornar o objeto de domínio mapeado', async () => {
		const result = await gateway.insertGuideDaily(dummyGuideDaily);

		expect(repositoryMock.create).toHaveBeenCalledWith({
			id: dummyGuideDaily.id,
			guide: { id: 1 },
			accommodation: dummyGuideDaily.accommodation,
			qtdeDaysRequested: dummyGuideDaily.qtdeDaysRequested,
			qtdeDaysAuthorized: dummyGuideDaily.qtdeDaysAuthorized,
			qtdeDaysDenied: dummyGuideDaily.qtdeDaysDenied,
			isCenso: dummyGuideDaily.isCenso,
			userId: dummyGuideDaily.userId,
			price: dummyGuideDaily.price,
			guideRequest: dummyGuideDaily.guideRequestId,
			enabled: dummyGuideDaily.enabled,
			dateStart: dummyGuideDaily.dateStart,
			dateFinish: dummyGuideDaily.dateFinish,
			qtdeDays: dummyGuideDaily.qtdeDays,
			aux: dummyGuideDaily.aux,
			auxId: dummyGuideDaily.auxId,
			codIntegration: dummyGuideDaily.codIntegration,
			updated: dummyGuideDaily.updated,
		});

		expect(GuideDailyMapper.toDomain).toHaveBeenCalledWith(
			dummyGuideDailyEntity,
		);

		expect(result.id).toBe(456);
	});
});
