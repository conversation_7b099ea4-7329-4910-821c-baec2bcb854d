import { EditCensoDadosGateway } from 'src/core/application/gateway/censoDados/editCensoDados.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { Repository } from 'typeorm';
import { EditCensoDadosGatewayImpl } from './editCensoDados.gateway.impl';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { LinhasConflitadasCensoEntity } from '../entities/linhasConflitadasCenso.entity';
import { TipoConflito } from 'src/core/domain/TipoConflito';

describe('EditCensoDadosGatewayImpl', () => {
	let censoDados: CensoDados[];
	let editCensoDadosGateway: EditCensoDadosGateway;
	let censoDadosRepository: jest.Mocked<Repository<CensoDadosEntity>>;
	let linhasConflitadasRepository: jest.Mocked<
		Repository<LinhasConflitadasCensoEntity>
	>;
	beforeEach(() => {
		censoDados = [
			new CensoDados(
				1,
				100,
				200,
				1,
				new Date('2023-01-01'),
				1,
				new Date('2023-02-01'),
				'São Paulo',
				'Hospital X',
				'Controle 123',
				new Date('1990-01-01'),
				new Date('2023-01-10'),
				new Date('2023-01-15'),
				'Alta Médica',
				'Diagnóstico Principal',
				'Diagnóstico Secundário',
				new Date('2023-01-20'),
				'Urgente',
				'Clínica',
				'GUIA123',
				'Ativo',
				'José da Silva',
				'1234567890',
				'São Paulo',
				'SP',
				false,
				'Tipo A',
				0,
				'SP-Regional',
				'Controle Tipo 1',
				10,
				'HOSP123',
				'PLANO123',
				'Plano de Saúde XYZ',
				'EMPRESA123',
				'Empresa Teste',
				'Ativo',
				new Date('2023-01-01'),
				null,
				null,
				[new TipoConflito(1, 'conflito')],
			),
		];

		censoDadosRepository = {
			update: jest.fn(),
		} as unknown as jest.Mocked<Repository<CensoDadosEntity>>;
		linhasConflitadasRepository = {
			delete: jest.fn(),
			insert: jest.fn(),
		} as unknown as jest.Mocked<Repository<LinhasConflitadasCensoEntity>>;
		editCensoDadosGateway = new EditCensoDadosGatewayImpl(
			censoDadosRepository,
			linhasConflitadasRepository,
		);
	});

	it('deve editar uma lista de censoDados', async () => {
		await editCensoDadosGateway.editCensoDados(censoDados);

		expect(censoDadosRepository.update).toHaveBeenCalledTimes(
			censoDados.length,
		);

		censoDados.forEach((censo) => {
			expect(censoDadosRepository.update).toHaveBeenCalledWith(censo.id, {
				data: censoDados[0].data,
				municipio: censoDados[0].municipio,
				hospitalCredenciado: censoDados[0].hospitalCredenciado,
				controle: censoDados[0].controle,
				dtNascimento: censoDados[0].dtNascimento,
				dataInternacao: censoDados[0].dataInternacao,
				dataAlta: censoDados[0].dataAlta,
				motivoAlta: censoDados[0].motivoAlta,
				diagnostico: censoDados[0].diagnostico,
				diagnosticoSecundario: censoDados[0].diagnosticoSecundario,
				previsaoAlta: censoDados[0].previsaoAlta,
				caraterInternacao: censoDados[0].caraterInternacao,
				tipoInternacao: censoDados[0].tipoInternacao,
				codigoGuia: censoDados[0].codigoGuia,
				altoCustoStatus: censoDados[0].altoCustoStatus,
				nomeBeneficiario: censoDados[0].nomeBeneficiario,
				codBeneficiario: censoDados[0].codBeneficiario,
				cidadeBeneficiario: censoDados[0].cidadeBeneficiario,
				estadoBeneficiario: censoDados[0].estadoBeneficiario,
				recemNascido: censoDados[0].recemNascido,
				tipoCliente: censoDados[0].tipoCliente,
				valorDiaria: String(censoDados[0].valorDiaria),
				regionalBeneficiario: censoDados[0].regionalBeneficiario,
				tipoControle: censoDados[0].tipoControle,
				diariasAutorizadas: censoDados[0].diariasAutorizadas,
				codigoHospital: censoDados[0].codigoHospital,
				codigoPlano: censoDados[0].codigoPlano,
				nomePlano: censoDados[0].nomePlano,
				codigoEmpresa: censoDados[0].codigoEmpresa,
				nomeEmpresa: censoDados[0].nomeEmpresa,
				statusPlano: censoDados[0].statusPlano,
				dataPlanoDesde: censoDados[0].dataPlanoDesde,
				conflito: 1,
				acomodacao: undefined,
			});
		});
	});

	it('deve editar uma linhaConflitada ao editar um censo com uma lista de tiposConflito', async () => {
		await editCensoDadosGateway.editCensoDados(censoDados);
		expect(linhasConflitadasRepository.delete).toHaveBeenCalled();
		expect(linhasConflitadasRepository.insert).toHaveBeenCalled();
		expect(censoDados[0].conflito).toBe(1);
	});

	it('deve somente deletar as linhas conflitadas quando passado uma lista de conflitos vazia', async () => {
		censoDados[0].tiposConflitos.pop();
		await editCensoDadosGateway.editCensoDados(censoDados);
		expect(linhasConflitadasRepository.delete).toHaveBeenCalled();
		expect(censoDados[0].conflito).toBe(0);
	});
});
