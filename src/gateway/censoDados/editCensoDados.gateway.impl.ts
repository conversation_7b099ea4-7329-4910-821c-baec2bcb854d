import { EditCensoDadosGateway } from 'src/core/application/gateway/censoDados/editCensoDados.gateway';
import { Repository } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { CensoDados } from 'src/core/domain/CensoDados';
import { LinhasConflitadasCensoEntity } from '../entities/linhasConflitadasCenso.entity';

export class EditCensoDadosGatewayImpl implements EditCensoDadosGateway {
	constructor(
		private readonly censoDadosRepository: Repository<CensoDadosEntity>,
		private readonly linhasConflitadasRepository: Repository<LinhasConflitadasCensoEntity>,
	) {}

	public async editCensoDados(censoDados: CensoDados[]): Promise<void> {
		const updatePromises = censoDados.map(async (censoDados) => {
			await this.editLinhasConflitadas(censoDados);
			return this.censoDadosRepository.update(censoDados.id, {
				data: censoDados.data,
				municipio: censoDados.municipio,
				hospitalCredenciado: censoDados.hospitalCredenciado,
				controle: censoDados.controle,
				dtNascimento: censoDados.dtNascimento,
				dataInternacao: censoDados.dataInternacao,
				dataAlta: censoDados.dataAlta,
				motivoAlta: censoDados.motivoAlta,
				diagnostico: censoDados.diagnostico,
				diagnosticoSecundario: censoDados.diagnosticoSecundario,
				previsaoAlta: censoDados.previsaoAlta,
				caraterInternacao: censoDados.caraterInternacao,
				tipoInternacao: censoDados.tipoInternacao,
				codigoGuia: censoDados.codigoGuia,
				altoCustoStatus: censoDados.altoCustoStatus,
				nomeBeneficiario: censoDados.nomeBeneficiario,
				codBeneficiario: censoDados.codBeneficiario,
				cidadeBeneficiario: censoDados.cidadeBeneficiario,
				estadoBeneficiario: censoDados.estadoBeneficiario,
				recemNascido: censoDados.recemNascido,
				tipoCliente: censoDados.tipoCliente,
				valorDiaria: String(censoDados.valorDiaria),
				regionalBeneficiario: censoDados.regionalBeneficiario,
				tipoControle: censoDados.tipoControle,
				diariasAutorizadas: censoDados.diariasAutorizadas,
				codigoHospital: censoDados.codigoHospital,
				codigoPlano: censoDados.codigoPlano,
				nomePlano: censoDados.nomePlano,
				codigoEmpresa: censoDados.codigoEmpresa,
				nomeEmpresa: censoDados.nomeEmpresa,
				statusPlano: censoDados.statusPlano,
				dataPlanoDesde: censoDados.dataPlanoDesde,
				conflito: censoDados.conflito,
				acomodacao: censoDados.acomodacao,
			});
		});
		await Promise.all(updatePromises);
	}

	private async editLinhasConflitadas(censoDados: CensoDados): Promise<void> {
		const tiposConflito = censoDados.tiposConflitos.filter(
			(conflito) => conflito != null,
		);
		await this.linhasConflitadasRepository.delete({
			censoDados: {
				id: censoDados.id,
			},
		});
		if (tiposConflito.length === 0) {
			censoDados.conflito = 0;
			return;
		}
		censoDados.conflito = 1;

		for (const tipoConflito of tiposConflito) {
			await this.linhasConflitadasRepository.insert({
				tipoConflito: tipoConflito,
				censoDados: {
					id: censoDados.id,
				},
			});
		}
	}
}
