import { Test, TestingModule } from '@nestjs/testing';
import { Brackets, IsNull, Repository, SelectQueryBuilder } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { CensoDadosListagem } from 'src/core/application/dto/CensoDadosListagem';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { CensoDadosMapper } from 'src/shared/mapper/censoDados/censoDados.mapper';
import { FindCensoDadosGatewayImpl } from './findCensoDados.gateway.Impl';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

jest.mock('typeorm', () => ({
	...jest.requireActual('typeorm'),
	Repository: jest.fn().mockImplementation(() => ({
		find: jest.fn(),
		createQueryBuilder: jest.fn(),
	})),
}));

const mockCensoDados = new CensoDados(
	1,
	100,
	200,
	1,
	new Date('2023-01-01'),
	0,
	new Date('2023-02-01'),
	'São Paulo',
	'Hospital X',
	'Controle 123',
	new Date('1990-01-01'),
	new Date('2023-01-10'),
	new Date('2023-01-15'),
	'Alta Médica',
	'Diagnóstico Principal',
	'Diagnóstico Secundário',
	new Date('2023-01-20'),
	'Urgente',
	'Clínica',
	'GUIA123',
	'Ativo',
	'José da Silva',
	'1234567890',
	'São Paulo',
	'SP',
	false,
	'Tipo A',
	0,
	'SP-Regional',
	'Controle Tipo 1',
	10,
	'HOSP123',
	'PLANO123',
	'Plano de Saúde XYZ',
	'EMPRESA123',
	'Empresa Teste',
	'Ativo',
	new Date('2023-01-01'),
	null,
	null,
	[new TipoConflito(1, 'conflito', SeverityCensoConflicts.ERROR)],
);

const censoEntity: CensoDadosEntity = {
	censo: null,
	id: 1,
	dataCriacao: new Date('2023-01-01'),
	dataExclusao: null,
	dataEdicao: null,
	dataExpiracao: null,
	censoId: 1,
	companyId: 1,
	userId: 1,
	conflito: 1,
	data: new Date('2023-01-01'),
	municipio: 'São Paulo',
	hospitalCredenciado: 'Hospital X',
	controle: 'Controle 123',
	dtNascimento: new Date('1990-01-01'),
	dataInternacao: new Date('2023-01-10'),
	dataAlta: new Date('2023-01-15'),
	motivoAlta: 'Alta Médica',
	diagnostico: 'Diagnóstico Principal',
	diagnosticoSecundario: 'Diagnóstico Secundário',
	previsaoAlta: new Date('2023-01-20'),
	caraterInternacao: 'Urgente',
	tipoInternacao: 'Clínica',
	codigoGuia: 'GUIA123',
	altoCustoStatus: 'Ativo',
	nomeBeneficiario: 'José da Silva',
	codBeneficiario: '1234567890',
	cidadeBeneficiario: 'São Paulo',
	estadoBeneficiario: 'SP',
	recemNascido: false,
	tipoCliente: 'Tipo A',
	valorDiaria: '',
	regionalBeneficiario: 'SP-Regional',
	tipoControle: 'Controle Tipo 1',
	diariasAutorizadas: 10,
	codigoHospital: 'HOSP123',
	codigoPlano: 'PLANO123',
	nomePlano: 'Plano de Saúde XYZ',
	codigoEmpresa: 'EMPRESA123',
	nomeEmpresa: 'Empresa Teste',
	statusPlano: 'Ativo',
	dataPlanoDesde: new Date('2023-01-01'),
	linhasConflitadas: [
		{
			id: 1,
			censoDados: null,
			tipoConflito: {
				id: 1,
				descricao: 'Conflito 1',
				gravidade: SeverityCensoConflicts.ERROR,
				linhasConflitadas: [],
			},
		},
	],
	acomodacao: '',
};

describe('FindCensoDadosGatewayImpl', () => {
	let findCensoDadosGatewayImpl: FindCensoDadosGatewayImpl;
	let censoDadosRepository: Repository<CensoDadosEntity>;
	let queryBuilderMock: jest.Mocked<SelectQueryBuilder<CensoDadosEntity>>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'CENSO_DADOS_REPOSITORY',
					useValue: {
						find: jest.fn(),
						createQueryBuilder: jest.fn(),
					},
				},
				{
					provide: 'FindCensoDadosGateway',
					useFactory: (censoDadosRepository: Repository<CensoDadosEntity>) =>
						new FindCensoDadosGatewayImpl(censoDadosRepository),
					inject: ['CENSO_DADOS_REPOSITORY'],
				},
			],
		}).compile();

		findCensoDadosGatewayImpl = module.get<FindCensoDadosGatewayImpl>(
			'FindCensoDadosGateway',
		);
		censoDadosRepository = module.get<Repository<CensoDadosEntity>>(
			'CENSO_DADOS_REPOSITORY',
		);

		queryBuilderMock = {
			leftJoinAndSelect: jest.fn().mockReturnThis(),
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			orWhere: jest.fn().mockReturnThis(),
			skip: jest.fn().mockReturnThis(),
			take: jest.fn().mockReturnThis(),
			select: jest.fn().mockReturnThis(),
			getMany: jest.fn().mockResolvedValue([mockCensoDados]),
			getCount: jest.fn().mockResolvedValue(10),
		} as unknown as jest.Mocked<SelectQueryBuilder<CensoDadosEntity>>;
	});

	it('deve encontrar CensoDados por CensoId', async () => {
		(censoDadosRepository.find as jest.Mock).mockResolvedValueOnce([
			mockCensoDados,
		]);

		const mockCensoId = 100;
		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result = await findCensoDadosGatewayImpl.findByCensoId(mockCensoId);

		expect(censoDadosRepository.find).toHaveBeenCalledWith({
			where: { censoId: mockCensoId, dataExclusao: IsNull() },
			relations: ['censo'],
		});
		expect(result).toHaveLength(1);
		expect(result[0].id).toBe(mockCensoDados.id);
		expect(result[0].municipio).toBe(mockCensoDados.municipio);
	});

	it('deve encontrar a listagem de CensoDados', async () => {
		const mockCensoId = 100;
		const mockPagination: Paginacao = { page: 1, limit: 10 };

		(censoDadosRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);

		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result: CensoDadosListagem =
			await findCensoDadosGatewayImpl.findListagem(
				mockCensoId,
				{ onlyError: false, onlyWarning: false },
				mockPagination,
			);

		expect(result).toBeDefined();
		expect(result.page).toBe(mockPagination.page);
		expect(result.totalQuantity).toBe(1);
		expect(result.censoData).toHaveLength(1);
		expect(result.censoData[0].id).toBe(mockCensoDados.id);
	});

	it('deve encontrar a listagem de CensoDados com onlyError, onlyWarning e noRowsWithWarnings', async () => {
		const mockCensoId = 100;
		const mockPagination: Paginacao = { page: 1, limit: 10 };

		(censoDadosRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);

		queryBuilderMock.getMany.mockResolvedValue([
			censoEntity,
			censoEntity,
			censoEntity,
		]);

		jest
			.spyOn(CensoDadosMapper, 'toTipoConflito')
			.mockReturnValueOnce(mockCensoDados.tiposConflitos[0]);
		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result: CensoDadosListagem =
			await findCensoDadosGatewayImpl.findListagem(
				mockCensoId,
				{ onlyError: true, onlyWarning: true, noRowsWithWarnings: true },
				mockPagination,
			);

		expect(result).toBeDefined();
		expect(result.page).toBe(mockPagination.page);
		expect(result.totalQuantity).toBe(3);
		expect(result.censoData).toHaveLength(3);
		expect(result.censoData[0]).toStrictEqual({ ...mockCensoDados, index: 0 });
	});

	it('deve encontrar a listagem de CensoDados com only conflito igual a null', async () => {
		const mockCensoId = 100;
		const mockPagination: Paginacao = { page: 1, limit: 10 };

		(censoDadosRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);

		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result: CensoDadosListagem =
			await findCensoDadosGatewayImpl.findListagem(
				mockCensoId,
				{ onlyError: false, onlyWarning: false },
				mockPagination,
			);

		expect(result).toBeDefined();
		expect(result.page).toBe(mockPagination.page);
		expect(result.totalQuantity).toBe(1);
		expect(result.censoData).toHaveLength(1);
		expect(result.censoData[0]).toStrictEqual({ ...mockCensoDados, index: 0 });
	});

	it('deve encontrar a listagem de CensoDados com only conflito igual a null', async () => {
		const mockCensoId = 100;
		const mockPagination: Paginacao = { page: 1, limit: 10 };

		(censoDadosRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);

		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result: CensoDadosListagem =
			await findCensoDadosGatewayImpl.findListagem(
				mockCensoId,
				{ onlyError: false, onlyWarning: false },
				mockPagination,
			);

		expect(result).toBeDefined();
		expect(result.page).toBe(mockPagination.page);
		expect(result.totalQuantity).toBe(1);
		expect(result.censoData).toHaveLength(1);
		expect(result.censoData[0]).toStrictEqual({ ...mockCensoDados, index: 0 });
	});

	it('deve encontrar a listagem de CensoDados com noRowsWithWarnings igual a 1', async () => {
		const mockCensoId = 100;
		const mockPagination: Paginacao = { page: 1, limit: 10 };

		(censoDadosRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);
		jest
			.spyOn(CensoDadosMapper, 'toCensoDados')
			.mockReturnValueOnce(mockCensoDados);

		const result: CensoDadosListagem =
			await findCensoDadosGatewayImpl.findListagem(
				mockCensoId,
				{ onlyError: false, onlyWarning: false, noRowsWithWarnings: true },
				mockPagination,
			);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
			expect.any(Brackets),
		);

		expect(result).toBeDefined();
		expect(result.page).toBe(mockPagination.page);
		expect(result.totalQuantity).toBe(1);
		expect(result.censoData).toHaveLength(1);
		expect(result.censoData[0]).toStrictEqual({ ...mockCensoDados, index: 0 });
	});

	it('deve aplicar corretamente o filtro noRowsWithWarnings testando diretamente o método applyFilters', () => {
		const testQueryBuilderMock = {
			andWhere: jest.fn().mockReturnThis(),
		};

		findCensoDadosGatewayImpl['applyFilters'](
			testQueryBuilderMock as unknown as SelectQueryBuilder<CensoDadosEntity>,
			{
				onlyError: false,
				onlyWarning: false,
				noRowsWithWarnings: true,
			},
		);

		expect(testQueryBuilderMock.andWhere).toHaveBeenCalledWith(
			expect.any(Brackets),
		);

		const bracketsCall = testQueryBuilderMock.andWhere.mock.calls[0][0];
		expect(bracketsCall).toBeInstanceOf(Brackets);
	});

	it('deve retornar 0 CensoDados se não houver registros', async () => {
		(censoDadosRepository.find as jest.Mock).mockResolvedValueOnce([]);
		const mockCensoId = 100;

		const result = await findCensoDadosGatewayImpl.findByCensoId(mockCensoId);

		expect(result).toHaveLength(0);
	});

	it('deve retornar nulo quando não encontrar CensoDados por CensoId', async () => {
		(censoDadosRepository.find as jest.Mock).mockResolvedValueOnce(null);

		const result = await findCensoDadosGatewayImpl.findByCensoId(100);

		expect(result).toStrictEqual([]);
	});
});
