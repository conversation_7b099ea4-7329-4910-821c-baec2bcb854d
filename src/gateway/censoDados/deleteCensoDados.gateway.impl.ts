import { DeleteCensoDadosGateway } from 'src/core/application/gateway/censoDados/deleteCensoDados.gateway';
import { In, Repository } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';

export class DeleteCensoDadosGatewayImpl implements DeleteCensoDadosGateway {
	constructor(private censoDadosRepository: Repository<CensoDadosEntity>) {}

	public async deleteBatch(censoDataIds: number[]): Promise<void> {
		await this.censoDadosRepository.update(
			{ id: In(censoDataIds) },
			{ dataExclusao: new Date() },
		);
	}
}
