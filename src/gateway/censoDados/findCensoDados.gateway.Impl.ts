import { <PERSON>rackets, <PERSON>Null, Repository, SelectQueryBuilder } from 'typeorm';
import { FindCensoDadosGateway } from 'src/core/application/gateway/censoDados/findCensoDados.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { CensoDadosMapper } from 'src/shared/mapper/censoDados/censoDados.mapper';
import { CensoDadosListagem } from 'src/core/application/dto/CensoDadosListagem';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { CensoDadosFilters } from 'src/core/application/dto/CensoDadosFilters';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

export class FindCensoDadosGatewayImpl implements FindCensoDadosGateway {
	constructor(private censoDadosRepository: Repository<CensoDadosEntity>) {}

	public async findByCensoId(censoId: number): Promise<CensoDados[]> {
		const censoDados = await this.censoDadosRepository.find({
			where: { censoId: censoId, dataExclusao: IsNull() },
			relations: ['censo'],
		});

		if (!censoDados) return [];

		return censoDados.map((dado) => CensoDadosMapper.toCensoDados(dado));
	}

	public async findListagem(
		censoId: number,
		filters: CensoDadosFilters,
		pagination: Paginacao,
	): Promise<CensoDadosListagem> {
		const skip = (pagination.page - 1) * pagination.limit;
		const censoData = await this.buildListagemQuery(
			censoId,
			skip,
			pagination.limit,
			filters,
			pagination.search,
		).getMany();

		const allData = await this.buildCountQuery(censoId, filters).getMany();
		const totalQuantity = allData.length;

		const censoDataMap = censoData.map((dados) =>
			CensoDadosMapper.toCensoDados(dados),
		);

		const censoDadosFormatted = this.adicionarIndice(censoDataMap, skip);

		const rowsWithErrors = allData.filter(
			(dado) =>
				dado.linhasConflitadas &&
				dado.linhasConflitadas.filter(
					(rows) =>
						rows.tipoConflito.gravidade === SeverityCensoConflicts.ERROR,
				).length > 0,
		);

		const rowsWithWarnings = allData.filter(
			(dado) =>
				dado.linhasConflitadas &&
				dado.linhasConflitadas.filter(
					(rows) =>
						rows.tipoConflito.gravidade === SeverityCensoConflicts.WARNING,
				).length > 0,
		);

		return {
			page: pagination.page,
			totalQuantity,
			censoData: censoDadosFormatted,
			totalAmountErrors: rowsWithErrors.length,
			totalAmountWarnings: rowsWithWarnings.length,
		};
	}

	private buildListagemQuery(
		censoId: number,
		skip: number,
		limit: number,
		filters: CensoDadosFilters,
		search?: string,
	): SelectQueryBuilder<CensoDadosEntity> {
		const query = this.censoDadosRepository
			.createQueryBuilder('censoDados')
			.leftJoinAndSelect('censoDados.censo', 'censo')
			.leftJoinAndSelect('censoDados.linhasConflitadas', 'linhasConflitadas')
			.leftJoinAndSelect('linhasConflitadas.tipoConflito', 'tipoConflito')
			.where('censoDados.censoId = :censoId', { censoId })
			.andWhere('censoDados.dataExclusao IS NULL')
			.skip(skip)
			.take(limit);

		this.applyFilters(query, filters);

		query.select(['censoDados', 'linhasConflitadas', 'tipoConflito']);

		this.applySearchFilter(query, search);

		return query;
	}

	private buildCountQuery(
		censoId: number,
		filters: CensoDadosFilters,
	): SelectQueryBuilder<CensoDadosEntity> {
		const query = this.censoDadosRepository
			.createQueryBuilder('censoDados')
			.leftJoinAndSelect('censoDados.linhasConflitadas', 'linhasConflitadas')
			.leftJoinAndSelect('linhasConflitadas.tipoConflito', 'tipoConflito')
			.where('censoDados.censoId = :censoId', { censoId })
			.andWhere('censoDados.dataExclusao IS NULL');

		this.applyFilters(query, filters);

		return query;
	}

	/* istanbul ignore next */
	private applySearchFilter(
		query: SelectQueryBuilder<CensoDadosEntity>,
		search?: string,
	): void {
		if (search) {
			query.andWhere(
				new Brackets((qb) => {
					qb.where('censoDados.hospitalCredenciado LIKE :search')
						.orWhere('censoDados.nomeBeneficiario LIKE :search')
						.orWhere('censoDados.nomeEmpresa LIKE :search')
						.orWhere('censoDados.acomodacao LIKE :search');
				}),
				{ search: `%${search}%` },
			);
		}
	}

	private applyFilters(
		query: SelectQueryBuilder<CensoDadosEntity>,
		filters: CensoDadosFilters,
	): void {
		if (filters.onlyError) {
			query.andWhere('tipoConflito.gravidade = "error"');
		}

		if (filters.onlyWarning) {
			query.andWhere('tipoConflito.gravidade = "warning"');
		}

		if (filters.noRowsWithWarnings) {
			query.andWhere(
				new Brackets((qb) => {
					qb.where('tipoConflito.gravidade != "warning"').orWhere(
						'censoDados.conflito = 0',
					);
				}),
			);
		}
	}

	private adicionarIndice(
		censoDados: CensoDados[],
		skip: number,
	): (CensoDados & { index: number })[] {
		return censoDados.map((item, index) => ({
			...item,
			index: index + skip,
		}));
	}
}
