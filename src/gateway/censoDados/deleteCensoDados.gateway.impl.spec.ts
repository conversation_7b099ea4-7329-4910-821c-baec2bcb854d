import { In, Repository } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { DeleteCensoDadosGatewayImpl } from './deleteCensoDados.gateway.impl';
import { DeleteCensoDadosGateway } from 'src/core/application/gateway/censoDados/deleteCensoDados.gateway';

describe('DeleteCensoDadosGatewayImpl', () => {
	let censoDadosRepositoryMock: jest.Mocked<Repository<CensoDadosEntity>>;
	let deleteCensoDadosGateway: DeleteCensoDadosGateway;

	beforeEach(() => {
		censoDadosRepositoryMock = {
			update: jest.fn(),
		} as unknown as jest.Mocked<Repository<CensoDadosEntity>>;

		deleteCensoDadosGateway = new DeleteCensoDadosGatewayImpl(
			censoDadosRepositoryMock,
		);
	});

	it('deve deletar um batch de CensoDados ', async () => {
		const censoDataIds = [1, 2, 3];
		await deleteCensoDadosGateway.deleteBatch(censoDataIds);
		expect(censoDadosRepositoryMock.update).toHaveBeenCalledWith(
			{ id: In(censoDataIds) },
			{ dataExclusao: expect.any(Date) },
		);
	});
});
