import { InsertCensoDadosGatewayImpl } from './insertCensoDados.gateway.impl';
import { InsertResult, Repository } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { LinhasConflitadasCensoEntity } from '../entities/linhasConflitadasCenso.entity';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { InsereCensoDados } from 'src/core/application/dto/InsereCensoDados';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

jest.mock('typeorm', () => ({
	Repository: jest.fn().mockImplementation(() => ({
		insert: jest.fn(),
		create: jest.fn(),
	})),
}));

describe('InsertCensoDadosGatewayImpl', () => {
	let insertCensoDadosGateway: InsertCensoDadosGatewayImpl;
	let censodadosRepository: jest.Mocked<Repository<CensoDadosEntity>>;
	let linhasConflitadasRepository: jest.Mocked<
		Repository<LinhasConflitadasCensoEntity>
	>;
	let insereCensoDados: InsereCensoDados;
	beforeEach(() => {
		insereCensoDados = {
			censoDados: {
				id: 1,
				dataCriacao: new Date(),
				censoId: 1,
				companyId: 1,
				userId: 1,
				conflito: null,
				data: new Date(),
				municipio: 'Municipio A',
				hospitalCredenciado: 'Hospital A',
				controle: 'Controle A',
				dtNascimento: new Date(),
				dataInternacao: new Date(),
				dataAlta: new Date(),
				motivoAlta: 'Alta',
				diagnostico: 'Diagnóstico A',
				diagnosticoSecundario: 'Diagnóstico Secundário',
				previsaoAlta: new Date(),
				caraterInternacao: 'Caráter A',
				tipoInternacao: 'Tipo A',
				codigoGuia: '123',
				altoCustoStatus: 'Status',
				nomeBeneficiario: 'Beneficiário A',
				codBeneficiario: '12345',
				cidadeBeneficiario: 'Cidade A',
				estadoBeneficiario: 'Estado A',
				recemNascido: false,
				tipoCliente: 'Cliente A',
				valorDiaria: 100,
				regionalBeneficiario: 'Regional A',
				tipoControle: 'Controle A',
				diariasAutorizadas: 5,
				codigoHospital: '123',
				codigoPlano: '456',
				nomePlano: 'Plano A',
				codigoEmpresa: '789',
				nomeEmpresa: 'Empresa A',
				statusPlano: 'Ativo',
				dataPlanoDesde: new Date(),
			},
			tiposConflito: [
				{
					id: 1,
					descricao: 'Conflito A',
					gravidade: SeverityCensoConflicts.WARNING,
				},
				{
					id: 2,
					descricao: 'Conflito B',
					gravidade: SeverityCensoConflicts.WARNING,
				},
			],
		};

		censodadosRepository = {
			create: jest.fn().mockReturnValue({
				dataCriacao: insereCensoDados.censoDados.dataCriacao,
				censoId: insereCensoDados.censoDados.censoId,
				companyId: insereCensoDados.censoDados.companyId,
				userId: insereCensoDados.censoDados.userId,
			}),
			insert: jest.fn(),
		} as unknown as jest.Mocked<Repository<CensoDadosEntity>>;
		linhasConflitadasRepository = {
			create: jest.fn().mockReturnValue({
				tipoConflito: insereCensoDados.tiposConflito,
			}),
			insert: jest.fn(),
		} as unknown as jest.Mocked<Repository<LinhasConflitadasCensoEntity>>;

		insertCensoDadosGateway = new InsertCensoDadosGatewayImpl(
			censodadosRepository,
			linhasConflitadasRepository,
		);
	});

	describe('insert', () => {
		it('should insert a CensoDadosEntity and linhas conflitadas', async () => {
			// Simula o retorno de insert no censodadosRepository
			const insertResult: InsertResult = { raw: { id: 1 } } as InsertResult;
			censodadosRepository.insert.mockResolvedValue(insertResult);

			// Chama o método insert
			await insertCensoDadosGateway.insert(insereCensoDados);

			// Verifica se o insert foi chamado para CensoDadosEntity
			expect(censodadosRepository.insert).toHaveBeenCalledWith(
				expect.objectContaining({
					dataCriacao: insereCensoDados.censoDados.dataCriacao,
					censoId: insereCensoDados.censoDados.censoId,
					companyId: insereCensoDados.censoDados.companyId,
					userId: insereCensoDados.censoDados.userId,
				}),
			);

			// Verifica se o insert foi chamado para LinhasConflitadasCensoEntity para cada conflito
			expect(linhasConflitadasRepository.insert).toHaveBeenCalledTimes(2); // Porque temos 2 conflitos
			expect(linhasConflitadasRepository.insert).toHaveBeenCalledWith(
				expect.objectContaining({
					tipoConflito: [
						{
							id: 1,
							descricao: 'Conflito A',
							gravidade: SeverityCensoConflicts.WARNING,
						},
						{
							id: 2,
							descricao: 'Conflito B',
							gravidade: SeverityCensoConflicts.WARNING,
						},
					],
				}),
			);
		});

		it('should not insert LinhasConflitadasCensoEntity when tiposConflito is empty', async () => {
			// Simula o retorno de insert no censodadosRepository
			insereCensoDados.tiposConflito.pop();
			insereCensoDados.tiposConflito.pop();

			const insertResult: InsertResult = { raw: { id: 1 } } as InsertResult;
			censodadosRepository.insert.mockResolvedValue(insertResult);

			// Chama o método insert
			await insertCensoDadosGateway.insert(insereCensoDados);

			// Verifica se o insert foi chamado para CensoDadosEntity
			expect(censodadosRepository.insert).toHaveBeenCalledTimes(1);

			// Verifica se o insert para LinhasConflitadasCensoEntity NÃO foi chamado
			expect(linhasConflitadasRepository.insert).not.toHaveBeenCalled();
		});
	});

	describe('insereLinhasConflitadas', () => {
		it('should skip invalid tipoConflito', async () => {
			insereCensoDados.tiposConflito.pop();
			insereCensoDados.tiposConflito.pop();

			const tiposConflito: TipoConflito[] = [
				null,
				{ id: 1, descricao: 'Valid', gravidade: SeverityCensoConflicts.ERROR },
			];
			insereCensoDados.tiposConflito.push(...tiposConflito);
			const insertResult: InsertResult = { raw: { id: 1 } } as InsertResult;
			await insertCensoDadosGateway['insereLinhasConflitadas'](
				tiposConflito,
				insertResult,
			);

			// O método deve ser chamado apenas para o tipo de conflito válido
			expect(linhasConflitadasRepository.insert).toHaveBeenCalledTimes(1);
		});
	});
});
