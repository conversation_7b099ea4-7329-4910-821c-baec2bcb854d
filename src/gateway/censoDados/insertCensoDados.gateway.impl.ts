import { InsertCensoDadosGateway } from 'src/core/application/gateway/censoDados/insertCensoDados.gateway';
import { InsertResult, Repository } from 'typeorm';
import { CensoDadosEntity } from '../entities/censoDados.entity';
import { InsereCensoDados } from 'src/core/application/dto/InsereCensoDados';
import { addWeeks } from 'date-fns';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { LinhasConflitadasCensoEntity } from '../entities/linhasConflitadasCenso.entity';

export class InsertCensoDadosGatewayImpl implements InsertCensoDadosGateway {
	constructor(
		private readonly censodadosRepository: Repository<CensoDadosEntity>,
		private readonly linhasConflitadasRepository: Repository<LinhasConflitadasCensoEntity>,
	) {}

	public async insert(insereCensoDados: InsereCensoDados): Promise<void> {
		const insertedCensoDados = await this.censodadosRepository.insert(
			this.createCensoDadosEntity(insereCensoDados),
		);
		this.insereLinhasConflitadas(
			insereCensoDados.tiposConflito,
			insertedCensoDados,
		);
	}

	private async insereLinhasConflitadas(
		tiposConflito: TipoConflito[],
		insertedResult: InsertResult,
	): Promise<void> {
		for (const tipoConflito of tiposConflito) {
			if (!tipoConflito) {
				continue;
			}
			await this.linhasConflitadasRepository.insert(
				this.createLinhasConflitadasEntity(insertedResult, tipoConflito),
			);
		}
	}

	private createCensoDadosEntity(
		insereCensoDados: InsereCensoDados,
	): CensoDadosEntity {
		const censoDadosEntity = this.censodadosRepository.create({
			dataCriacao: insereCensoDados.censoDados.dataCriacao,
			dataExclusao: null,
			dataEdicao: null,
			dataExpiracao: addWeeks(insereCensoDados.censoDados.dataCriacao, 1),
			censoId: insereCensoDados.censoDados.censoId,
			companyId: insereCensoDados.censoDados.companyId,
			userId: insereCensoDados.censoDados.userId,
			conflito: insereCensoDados.censoDados.conflito,
			data: insereCensoDados.censoDados.data,
			municipio: insereCensoDados.censoDados.municipio,
			hospitalCredenciado: insereCensoDados.censoDados.hospitalCredenciado,
			controle: insereCensoDados.censoDados.controle,
			dtNascimento: insereCensoDados.censoDados.dtNascimento,
			dataInternacao: insereCensoDados.censoDados.dataInternacao,
			dataAlta: insereCensoDados.censoDados.dataAlta,
			motivoAlta: insereCensoDados.censoDados.motivoAlta,
			diagnostico: insereCensoDados.censoDados.diagnostico,
			diagnosticoSecundario: insereCensoDados.censoDados.diagnosticoSecundario,
			previsaoAlta: insereCensoDados.censoDados.previsaoAlta,
			caraterInternacao: insereCensoDados.censoDados.caraterInternacao,
			tipoInternacao: insereCensoDados.censoDados.tipoInternacao,
			codigoGuia: insereCensoDados.censoDados.codigoGuia,
			altoCustoStatus: insereCensoDados.censoDados.altoCustoStatus,
			nomeBeneficiario: insereCensoDados.censoDados.nomeBeneficiario,
			codBeneficiario: insereCensoDados.censoDados.codBeneficiario,
			cidadeBeneficiario: insereCensoDados.censoDados.cidadeBeneficiario,
			estadoBeneficiario: insereCensoDados.censoDados.estadoBeneficiario,
			recemNascido: insereCensoDados.censoDados.recemNascido,
			tipoCliente: insereCensoDados.censoDados.tipoCliente,
			valorDiaria: String(insereCensoDados.censoDados.valorDiaria),
			regionalBeneficiario: insereCensoDados.censoDados.regionalBeneficiario,
			tipoControle: insereCensoDados.censoDados.tipoControle,
			diariasAutorizadas: insereCensoDados.censoDados.diariasAutorizadas,
			codigoHospital: insereCensoDados.censoDados.codigoHospital,
			codigoPlano: insereCensoDados.censoDados.codigoPlano,
			nomePlano: insereCensoDados.censoDados.nomePlano,
			codigoEmpresa: insereCensoDados.censoDados.codigoEmpresa,
			nomeEmpresa: insereCensoDados.censoDados.nomeEmpresa,
			statusPlano: insereCensoDados.censoDados.statusPlano,
			dataPlanoDesde: insereCensoDados.censoDados.dataPlanoDesde,

			acomodacao: insereCensoDados.censoDados.acomodacao,
		});

		return censoDadosEntity;
	}

	private createLinhasConflitadasEntity(
		insertedResult: InsertResult,
		tipoConflito: TipoConflito,
	): LinhasConflitadasCensoEntity {
		const tiposConflitosEntity = this.linhasConflitadasRepository.create({
			tipoConflito: { id: tipoConflito.id, descricao: tipoConflito.descricao },
			censoDados: insertedResult.raw.insertId,
		});
		return tiposConflitosEntity;
	}
}
