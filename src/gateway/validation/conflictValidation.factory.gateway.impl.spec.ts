import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { ConflictValidationFactoryGatewayImpl } from './conflictValidation.factory.gateway.impl';
import { CensoDados } from 'src/core/domain/CensoDados';
import { InvalidConflictValidationException } from 'src/shared/exceptions/conflict/invalidConflictValidation.exception';
import { DataEntradaSuperiorAltaConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataEntradaSuperiorAltaConflict.validation.strategy';
import { DataNascimentoFuturoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataNascimentoFuturoConflict.validation.strategy';
import { MesmoCodigoGuiaInternacaoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/mesmoCodigoGuiaInternacaoConflict.validation.strategy';
import { DataEntradaInferiorDataNascimentoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataEntradaInferiorDataNascimentoConflict.validation.strategy';
import { PeriodoEntreInternacaoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/periodoEntreInternacaoConflict.validation.strategy';
import { CadastroInternacaoFuturaSemAltaConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/cadastroInternacaoFuturaSemAltaConflict.validation.strategy';
import { HospitalNotFoundConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/hospitalNotFoundConflict.validation.strategy';
import { PatientEntity } from '../entities/patient.entity';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';

describe('ConflictValidationFactoryGatewayImpl', () => {
	let patientWayRepositoryMock: jest.Mocked<Repository<HospitalizationEntity>>;
	let conflictValidationFactoryGatewayImpl: ConflictValidationFactoryGatewayImpl;
	let patientRepositoryMock: jest.Mocked<Repository<PatientEntity>>;
	let censoDadosMock: jest.Mocked<CensoDados>;
	let hospitalCompanyRepositoryMock: jest.Mocked<
		Repository<HospitalsCompanyEntity>
	>;

	beforeEach(() => {
		patientWayRepositoryMock = {
			find: jest.fn(),
		} as unknown as jest.Mocked<Repository<HospitalizationEntity>>;

		conflictValidationFactoryGatewayImpl =
			new ConflictValidationFactoryGatewayImpl(
				hospitalCompanyRepositoryMock,
				patientWayRepositoryMock,
				patientRepositoryMock,
			);

		censoDadosMock = {} as jest.Mocked<CensoDados>;
	});

	const validStrategies = [
		// CodigoBeneficiarioDuplicadoConflictValidationStrategy.name,
		HospitalNotFoundConflictValidationStrategy.name,
		DataEntradaSuperiorAltaConflictValidationStrategy.name,
		DataNascimentoFuturoConflictValidationStrategy.name,
		// NomeNascimentoConflictValidationStrategy.name,
		MesmoCodigoGuiaInternacaoConflictValidationStrategy.name,
		DataEntradaInferiorDataNascimentoConflictValidationStrategy.name,
		PeriodoEntreInternacaoConflictValidationStrategy.name,
		CadastroInternacaoFuturaSemAltaConflictValidationStrategy.name,
	];

	it.each(validStrategies)(
		'deve retornar um ConflictValidationGateway para a estratégia válida %s',
		(strategyName) => {
			const conflictGateway = conflictValidationFactoryGatewayImpl.execute(
				censoDadosMock,
				strategyName,
			);
			expect(conflictGateway).toHaveProperty('validate');
		},
	);

	it('deve lançar um erro quando a estratégia for inválida', () => {
		expect(() =>
			conflictValidationFactoryGatewayImpl.execute(
				censoDadosMock,
				'NomeInvalido',
			),
		).toThrow(InvalidConflictValidationException);
	});
});
