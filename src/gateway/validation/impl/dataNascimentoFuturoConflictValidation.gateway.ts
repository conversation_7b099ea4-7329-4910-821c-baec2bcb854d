import { ConflictValidationGateway } from '../../../core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { isAfter } from 'date-fns';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class DataNascimentoFuturoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(private readonly censoDados: CensoDados) {}

	public async validate(): Promise<TipoConflito> {
		const dataNascimentoFuturo = isAfter(
			this.censoDados.dtNascimento,
			new Date(),
		);
		if (dataNascimentoFuturo)
			return new TipoConflito(
				3,
				'A data de nascimento do paciente está no futuro. Corrija essa informação.',
			);
		return null;
	}
}
