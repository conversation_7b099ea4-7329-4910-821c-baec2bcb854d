import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { HospitalizationValidation } from 'src/shared/censoValidations/hospitalizationValidation';

export class PeriodoEntreInternacaoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findHospitalizationGateway: FindHospitalizationGateway,
		private readonly censoDados: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		const hospitalization =
			await this.findHospitalizationGateway.findBeetweenPeriods(
				this.censoDados.dataInternacao,
				this.censoDados.dataAlta,
				this.censoDados.patientId,
				this.censoDados.companyId,
			);

		if (!hospitalization) return null;
		const dataAlta = this.censoDados.dataAlta
			? new Date(this.censoDados.dataAlta)
			: null;
		if (
			HospitalizationValidation.validateConflict(
				hospitalization,
				new Date(this.censoDados.dataInternacao),
				dataAlta,
				this.censoDados.acomodacao,
			)
		) {
			return null;
		}

		return new TipoConflito(
			7,
			'O período de internação (data de entrada e saída) conflita com uma internação já cadastrada para o mesmo paciente.',
			SeverityCensoConflicts.WARNING,
		);
	}
}
