import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { DataNascimentoFuturoConflictValidationGateway } from './dataNascimentoFuturoConflictValidation.gateway';

describe('DataNascimentoFuturoConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('4000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'<PERSON>rgent<PERSON>',
			'<PERSON><PERSON><PERSON><PERSON>',
			'GUIA123',
			'Ativo',
			'<PERSON>',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway =
			new DataNascimentoFuturoConflictValidationGateway(censoDadosMock);
	});

	it('deve retornar tipo conflito se data de nascimento for futura', async () => {
		const result = await conflictValidationGateway.validate();

		expect(result).not.toBeNull();
		expect(result.id).toBe(3);
		expect(result.descricao).toBe(
			'A data de nascimento do paciente está no futuro. Corrija essa informação.',
		);
	});

	it('deve retornar nulo se data de nascimento for passada', async () => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);
		conflictValidationGateway =
			new DataNascimentoFuturoConflictValidationGateway(censoDadosMock);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});
});
