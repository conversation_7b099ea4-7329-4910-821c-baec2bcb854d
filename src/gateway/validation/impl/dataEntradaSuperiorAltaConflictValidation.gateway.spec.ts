import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { DataEntradaSuperiorAltaConflictValidationGateway } from './dataEntradaSuperiorAltaConflictValidation.gateway';
import { TipoConflito } from 'src/core/domain/TipoConflito';

describe('DataEntradaSuperiorAltaConflictValidation', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-05'),
			'Alta Médica',
			'<PERSON>agnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway =
			new DataEntradaSuperiorAltaConflictValidationGateway(censoDadosMock);
	});

	it('deve retornar tipo conflito se data de alta for inferior a data de internação', async () => {
		const result = await conflictValidationGateway.validate();

		expect(result).toEqual(
			new TipoConflito(
				2,
				'A data de internação não pode ser superior à data de alta.',
			),
		);
	});

	it('deve retornar nulo se data de alta for null e a data de entrada não', async () => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			null,
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway =
			new DataEntradaSuperiorAltaConflictValidationGateway(censoDadosMock);

		const result = await conflictValidationGateway.validate();
		expect(result).toBeNull();
	});

	it('deve retornar nulo data de alta nao for inferior a data de internação', async () => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-20'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway =
			new DataEntradaSuperiorAltaConflictValidationGateway(censoDadosMock);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});
});
