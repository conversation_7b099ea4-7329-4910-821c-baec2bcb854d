import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class NomeNascimentoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findPatientGateway: FindPatientGateway,
		private readonly censoDados: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		const patientWay =
			await this.findPatientGateway.findByNameBirthAndNotCodBeneficiary(
				this.censoDados.nomeBeneficiario,
				this.censoDados.dtNascimento,
				this.censoDados.companyId,
				this.censoDados.codBeneficiario,
			);
		if (patientWay)
			return new TipoConflito(
				4,
				'Pacientes com o mesmo nome e data de nascimento, mas códigos diferentes. Verifique se há duplicidade no cadastro.',
			);
		return null;
	}
}
