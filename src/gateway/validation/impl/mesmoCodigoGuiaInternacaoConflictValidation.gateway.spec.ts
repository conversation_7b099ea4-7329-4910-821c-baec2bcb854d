import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { MesmoCodigoGuiaInternacaoConflictValidationGateway } from './mesmoCodigoGuiaInternacaoConflictValidation.gateway';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { TipoConflito } from 'src/core/domain/TipoConflito';

describe('MesmoCodigoGuiaInternacaoConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let findPatientWayGatewayMock: jest.Mocked<FindHospitalizationGateway>;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		findPatientWayGatewayMock = {
			findByCodigoGuia: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalizationGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway =
			new MesmoCodigoGuiaInternacaoConflictValidationGateway(
				findPatientWayGatewayMock,
				censoDadosMock,
			);
	});

	it('deve retornar nada caso não houver código de guia', async () => {
		Object.defineProperty(censoDadosMock, 'codigoGuia', {
			value: null,
		});

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});

	it('deve retornar tipo conflito se houver internação com mesmo código de guia', async () => {
		findPatientWayGatewayMock.findByCodigoGuia.mockResolvedValue(
			{} as Hospitalization,
		);

		const result = await conflictValidationGateway.validate();

		expect(result).toEqual(
			new TipoConflito(5, 'Já existe uma internação com esse código de guia.'),
		);
	});

	it('deve retornar null se não houver internação com mesmo código de guia', async () => {
		findPatientWayGatewayMock.findByCodigoGuia.mockResolvedValue(null);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});

	it('deve retornar null se a data de internação for a mesma e a data de alta não for nula', async () => {
		Object.defineProperty(censoDadosMock, 'dataInternacao', {
			value: new Date('2023-01-10'),
		});
		Object.defineProperty(censoDadosMock, 'dataAlta', {
			value: new Date('2023-01-20'),
		});

		findPatientWayGatewayMock.findByCodigoGuia.mockResolvedValue({
			admissionIn: new Date('2023-01-10'),
		} as Hospitalization);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});
});
