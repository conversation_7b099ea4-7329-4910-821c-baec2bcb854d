import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { HospitalNotFoundConflictValidationGateway } from './hospitalNotFoundConflictValidation.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { FindHospitalCompanyGateway } from 'src/core/application/gateway/hospital/findHospitalCompany.gateway';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

describe('HospitalNotFoundConflictValidationGateway', () => {
	let findHospitalCompanyGatewayMock: jest.Mocked<FindHospitalCompanyGateway>;
	let censoDadosMock: CensoDados;
	let conflictValidationGateway: ConflictValidationGateway;

	beforeEach(() => {
		findHospitalCompanyGatewayMock = {
			findHospitalCompanyByName: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalCompanyGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway = new HospitalNotFoundConflictValidationGateway(
			findHospitalCompanyGatewayMock,
			censoDadosMock,
		);
	});

	it('deve retornar nada caso o hospital seja encontrado', async () => {
		findHospitalCompanyGatewayMock.findHospitalCompanyByName.mockResolvedValue(
			{} as HospitalsCompany,
		);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});

	it('deve retornar tipo conflito caso o hospital não seja encontrado', async () => {
		findHospitalCompanyGatewayMock.findHospitalCompanyByName.mockResolvedValue(
			null,
		);

		const result = await conflictValidationGateway.validate();

		expect(result).toEqual(
			new TipoConflito(
				9,
				'Prestador não encontrado. Para continuar, insira o número CNES do prestador na coluna correspondente e realize o cadastro.',
				SeverityCensoConflicts.WARNING,
			),
		);
	});
});
