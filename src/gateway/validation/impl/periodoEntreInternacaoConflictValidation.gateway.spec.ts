import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { PeriodoEntreInternacaoConflictValidationGateway } from './periodoEntreInternacaoConflictValidation.gateway';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

describe('PeriodoEntreInternacaoConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let findPatientWayGatewayMock: jest.Mocked<FindHospitalizationGateway>;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		findPatientWayGatewayMock = {
			findBeetweenPeriods: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalizationGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			null,
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
			null,
		);

		conflictValidationGateway =
			new PeriodoEntreInternacaoConflictValidationGateway(
				findPatientWayGatewayMock,
				censoDadosMock,
			);
	});

	it('deve retornar tipo conflito quando uma internação conflitar conflita com uma internação já cadastrada para o mesmo paciente', async () => {
		findPatientWayGatewayMock.findBeetweenPeriods.mockResolvedValue({
			admissionIn: new Date('2023-01-10'),
			admissionOut: null,
			bedHospitals: [],
		} as Hospitalization);
		const result = await conflictValidationGateway.validate();
		expect(result).toEqual(
			new TipoConflito(
				7,
				'O período de internação (data de entrada e saída) conflita com uma internação já cadastrada para o mesmo paciente.',
				SeverityCensoConflicts.WARNING,
			),
		);
	});

	it('deve retornar null quando não houver conflito', async () => {
		findPatientWayGatewayMock.findBeetweenPeriods.mockResolvedValue(null);
		const result = await conflictValidationGateway.validate();
		expect(result).toBeNull();
	});

	it('deve retornar null se a data de internação for a mesma e a data de alta não for nula', async () => {
		Object.defineProperty(censoDadosMock, 'dataInternacao', {
			value: new Date('2023-01-10'),
		});
		Object.defineProperty(censoDadosMock, 'dataAlta', {
			value: new Date('2023-01-20'),
		});

		findPatientWayGatewayMock.findBeetweenPeriods.mockResolvedValue({
			admissionIn: new Date('2023-01-10'),
		} as Hospitalization);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});

	it('deve retornar null se for troca de acomodação do leito', async () => {
		Object.defineProperty(censoDadosMock, 'dataInternacao', {
			value: new Date('2023-01-10'),
		});
		Object.defineProperty(censoDadosMock, 'dataAlta', {
			value: null,
		});

		Object.defineProperty(censoDadosMock, 'acomodacao', {
			value: 'UTI',
		});

		findPatientWayGatewayMock.findBeetweenPeriods.mockResolvedValue({
			admissionIn: new Date('2023-01-10'),
			bedHospitals: [
				{
					admissionIn: new Date('2023-01-10'),
					accommodation: 'ENFERMARIA',
				},
			],
		} as Hospitalization);

		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});
});
