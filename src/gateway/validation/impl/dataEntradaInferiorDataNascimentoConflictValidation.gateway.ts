import { isAfter } from 'date-fns';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class DataEntradaInferiorDataNascimentoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(private readonly censoDados: CensoDados) {}

	public async validate(): Promise<TipoConflito> {
		if (!this.censoDados.dataInternacao || !this.censoDados.dtNascimento) {
			return null;
		}

		const isDataNascimentoAposInternacao = isAfter(
			this.censoDados.dtNascimento,
			this.censoDados.dataInternacao,
		);
		if (isDataNascimentoAposInternacao)
			return new TipoConflito(
				6,
				'A data de entrada na internação não pode ser anterior à data de nascimento do paciente.',
			);
		return null;
	}
}
