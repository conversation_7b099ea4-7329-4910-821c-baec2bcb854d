import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { CodigoBeneficiarioDuplicadoConflictValidationGateway } from './codigoBeneficiarioDuplicadoConflictValidation.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { Patient } from 'src/core/domain/Patient';

describe('CadastroInternacaoFuturaSemAltaConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let findPatientWayGatewayMock: jest.Mocked<FindPatientGateway>;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		findPatientWayGatewayMock = {
			findByCodBeneficiaryAndNotName: jest.fn(),
		} as unknown as jest.Mocked<FindPatientGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[new TipoConflito(1, 'conflito')],
		);

		conflictValidationGateway =
			new CodigoBeneficiarioDuplicadoConflictValidationGateway(
				findPatientWayGatewayMock,
				censoDadosMock,
			);
	});

	it('deve retornar um tipo de conflito se houver conflito de codigo beneficiario duplicado', async () => {
		findPatientWayGatewayMock.findByCodBeneficiaryAndNotName.mockResolvedValueOnce(
			{} as Patient,
		);

		const result = await conflictValidationGateway.validate();

		expect(
			findPatientWayGatewayMock.findByCodBeneficiaryAndNotName,
		).toHaveBeenCalledWith(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);
		expect(result).toEqual(
			new TipoConflito(
				1,
				'O código já existe para este beneficiário. Verifique também o status de habilitação do beneficiário.',
			),
		);
	});

	it('deve retornar null se não houver conflito de codigo beneficiario duplicado', async () => {
		findPatientWayGatewayMock.findByCodBeneficiaryAndNotName.mockResolvedValueOnce(
			null,
		);

		const result = await conflictValidationGateway.validate();

		expect(
			findPatientWayGatewayMock.findByCodBeneficiaryAndNotName,
		).toHaveBeenCalledWith(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);
		expect(result).toBeNull();
	});
});
