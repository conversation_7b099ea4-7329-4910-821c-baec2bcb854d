import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { HospitalizationValidation } from 'src/shared/censoValidations/hospitalizationValidation';

export class CadastroInternacaoFuturaSemAltaConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findPatientWayGateway: FindHospitalizationGateway,
		private readonly censoDados: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		const patientWayListWithoutOut =
			await this.findPatientWayGateway.findActiveHospitalizationByPatient(
				this.censoDados.patientId,
				this.censoDados.companyId,
			);

		const isCadastroInternacaoFuturaSemAlta = patientWayListWithoutOut.filter(
			(patientWay) => {
				return !HospitalizationValidation.validateConflict(
					patientWay,
					new Date(this.censoDados.dataInternacao),
					new Date(this.censoDados.dataAlta),
					this.censoDados.acomodacao,
				);
			},
		);

		if (isCadastroInternacaoFuturaSemAlta.length > 0)
			return new TipoConflito(
				8,
				'Para pacientes ainda internados (sem data de alta registrada), não é possível cadastrar uma internação futura. A data de entrada não pode ser maior do que a internação ainda em andamento.',
				SeverityCensoConflicts.WARNING,
			);
		return null;
	}
}
