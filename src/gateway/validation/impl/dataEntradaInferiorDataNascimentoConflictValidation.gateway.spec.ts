import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { DataEntradaInferiorDataNascimentoConflictValidationGateway } from './dataEntradaInferiorDataNascimentoConflictValidation.gateway';

describe('DataEntradaInferiorDataNascimentoConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2000-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[new TipoConflito(1, 'conflito')],
		);

		conflictValidationGateway =
			new DataEntradaInferiorDataNascimentoConflictValidationGateway(
				censoDadosMock,
			);
	});

	it('deve retornar um tipo de conflito se houver conflito de data de entrada inferior a data de nascimento', async () => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('2027-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[new TipoConflito(1, 'conflito')],
		);
		conflictValidationGateway =
			new DataEntradaInferiorDataNascimentoConflictValidationGateway(
				censoDadosMock,
			);
		const result = await conflictValidationGateway.validate();
		expect(result).toEqual(
			new TipoConflito(
				6,
				'A data de entrada na internação não pode ser anterior à data de nascimento do paciente.',
			),
		);
	});

	it('deve retornar null se a data de entrada for posterior a data de nascimento', async () => {
		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});

	it('deve retornar null caso data de nascimento ou data de internação for null', async () => {
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			null,
			null,
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'1234567890',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[new TipoConflito(1, 'conflito')],
		);
		conflictValidationGateway =
			new DataEntradaInferiorDataNascimentoConflictValidationGateway(
				censoDadosMock,
			);
		const result = await conflictValidationGateway.validate();

		expect(result).toBeNull();
	});
});
