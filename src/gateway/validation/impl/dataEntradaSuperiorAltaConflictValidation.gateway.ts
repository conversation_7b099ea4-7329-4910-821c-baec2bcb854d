import { ConflictValidationGateway } from '../../../core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { isAfter } from 'date-fns';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class DataEntradaSuperiorAltaConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(private readonly censoDados: CensoDados) {}

	public async validate(): Promise<TipoConflito> {
		const dataInernacaoSuperiorAlta = isAfter(
			this.censoDados.dataInternacao,
			this.censoDados.dataAlta,
		);

		const dataAltaNula = this.censoDados.dataAlta === null;

		if (dataInernacaoSuperiorAlta && !dataAltaNula) {
			return new TipoConflito(
				2,
				'A data de internação não pode ser superior à data de alta.',
			);
		}
		return null;
	}
}
