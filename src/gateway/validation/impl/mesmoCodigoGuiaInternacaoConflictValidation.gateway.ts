import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class MesmoCodigoGuiaInternacaoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findHospitalizationGateway: FindHospitalizationGateway,
		private readonly censoDados: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		if (!this.censoDados.codigoGuia) return null;

		const patientWay = await this.findHospitalizationGateway.findByCodigoGuia(
			this.censoDados.companyId,
			this.censoDados.codigoGuia,
		);

		if (!patientWay) return null;

		if (
			new Date(patientWay.admissionIn).getTime() ===
				new Date(this.censoDados.dataInternacao).getTime() &&
			this.censoDados.dataAlta !== null
		) {
			return null;
		}

		return new TipoConflito(
			5,
			'Já existe uma internação com esse código de guia.',
		);
	}
}
