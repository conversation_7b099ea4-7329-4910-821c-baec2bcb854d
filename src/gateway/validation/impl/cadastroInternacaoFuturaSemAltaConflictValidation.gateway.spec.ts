import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { CadastroInternacaoFuturaSemAltaConflictValidationGateway } from './cadastroInternacaoFuturaSemAltaConflictValidation.gateway';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { Hospitalization } from 'src/core/domain/Hospitalization';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

describe('CadastroInternacaoFuturaSemAltaConflictValidationGateway', () => {
	let conflictValidationGateway: CadastroInternacaoFuturaSemAltaConflictValidationGateway;
	let findHospitalizationGatewayMock: jest.Mocked<FindHospitalizationGateway>;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		findHospitalizationGatewayMock = {
			findActiveHospitalizationByPatient: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalizationGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[new TipoConflito(1, 'conflito')],
		);

		conflictValidationGateway =
			new CadastroInternacaoFuturaSemAltaConflictValidationGateway(
				findHospitalizationGatewayMock,
				censoDadosMock,
			);
	});

	describe('validate', () => {
		it('should not return conflict when no future internment without discharge is found', async () => {
			const mockPatientWayList = [
				{ admissionIn: new Date('2024-12-15') } as unknown as Hospitalization,
				{ admissionIn: new Date('2024-12-20') } as unknown as Hospitalization,
			];

			findHospitalizationGatewayMock.findActiveHospitalizationByPatient.mockResolvedValue(
				mockPatientWayList,
			);

			await conflictValidationGateway.validate();

			expect(
				findHospitalizationGatewayMock.findActiveHospitalizationByPatient,
			).toHaveBeenCalledWith(
				censoDadosMock.patientId,
				censoDadosMock.companyId,
			);
		});

		it('should return conflict when future internment without discharge is found', async () => {
			const mockPatientWayList = [
				{
					nome: 'jao',
					admissionIn: new Date('2024-12-15'),
				} as unknown as Hospitalization,
				{
					nome: 'joana',
					admissionIn: new Date('2024-12-20'),
				} as unknown as Hospitalization,
			];

			findHospitalizationGatewayMock.findActiveHospitalizationByPatient.mockResolvedValue(
				mockPatientWayList,
			);

			const result = await conflictValidationGateway.validate();

			expect(
				findHospitalizationGatewayMock.findActiveHospitalizationByPatient,
			).toHaveBeenCalledWith(
				censoDadosMock.patientId,
				censoDadosMock.companyId,
			);

			expect(result).toEqual(
				new TipoConflito(
					8,
					'Para pacientes ainda internados (sem data de alta registrada), não é possível cadastrar uma internação futura. A data de entrada não pode ser maior do que a internação ainda em andamento.',
					SeverityCensoConflicts.WARNING,
				),
			);
		});

		it('should return null when no patient way is found', async () => {
			findHospitalizationGatewayMock.findActiveHospitalizationByPatient.mockResolvedValue(
				[],
			);

			const result = await conflictValidationGateway.validate();

			expect(
				findHospitalizationGatewayMock.findActiveHospitalizationByPatient,
			).toHaveBeenCalledWith(
				censoDadosMock.patientId,
				censoDadosMock.companyId,
			);

			expect(result).toBeNull();
		});

		it('should return null when the admission date is the same as the census date and the discharge date is not null', async () => {
			Object.defineProperty(censoDadosMock, 'dataInternacao', {
				value: new Date('2024-12-15'),
			});
			Object.defineProperty(censoDadosMock, 'dataAlta', {
				value: new Date('2024-12-20'),
			});

			const mockPatientWayList = [
				{
					admissionIn: new Date('2024-12-15'),
				} as unknown as Hospitalization,
			];

			findHospitalizationGatewayMock.findActiveHospitalizationByPatient.mockResolvedValue(
				mockPatientWayList,
			);

			const result = await conflictValidationGateway.validate();

			expect(result).toBeNull();
		});
	});
});
