import { ConflictValidationGateway } from '../../../core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';

export class CodigoBeneficiarioDuplicadoConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findPatientGateway: FindPatientGateway,
		private readonly censoDados: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		const haveDuplicatedCode =
			await this.findPatientGateway.findByCodBeneficiaryAndNotName(
				this.censoDados.nomeBeneficiario,
				this.censoDados.codBeneficiario,
				this.censoDados.companyId,
			);
		if (haveDuplicatedCode)
			return new TipoConflito(
				1,
				'O código já existe para este beneficiário. Verifique também o status de habilitação do beneficiário.',
			);

		return null;
	}
}
