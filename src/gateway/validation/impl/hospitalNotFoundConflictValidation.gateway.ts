import { ConflictValidationGateway } from '../../../core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { FindHospitalCompanyGateway } from 'src/core/application/gateway/hospital/findHospitalCompany.gateway';
import { SeverityCensoConflicts } from 'src/core/application/enums/typeConflicts/SeverityCensoConflicts.enum';

export class HospitalNotFoundConflictValidationGateway
	implements ConflictValidationGateway
{
	constructor(
		private readonly findHospitalCompanyGateway: FindHospitalCompanyGateway,
		private readonly censoData: CensoDados,
	) {}

	public async validate(): Promise<TipoConflito> {
		const hospitalsCompanies =
			await this.findHospitalCompanyGateway.findHospitalCompanyByName(
				this.censoData.hospitalCredenciado,
				this.censoData.companyId,
			);

		if (!hospitalsCompanies) {
			return new TipoConflito(
				9,
				'Prestador não encontrado. Para continuar, insira o número CNES do prestador na coluna correspondente e realize o cadastro.',
				SeverityCensoConflicts.WARNING,
			);
		}

		return null;
	}
}
