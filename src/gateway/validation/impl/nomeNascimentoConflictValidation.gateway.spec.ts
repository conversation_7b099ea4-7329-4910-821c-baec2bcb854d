import { ConflictValidationGateway } from 'src/core/application/gateway/validation/abstract/conflictValidation.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { NomeNascimentoConflictValidationGateway } from './nomeNascimentoConflictValidation.gateway';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { Patient } from 'src/core/domain/Patient';

describe('NomeNascimentoConflictValidationGateway', () => {
	let conflictValidationGateway: ConflictValidationGateway;
	let findPatientGatewayMock: jest.Mocked<FindPatientGateway>;
	let censoDadosMock: CensoDados;

	beforeEach(() => {
		findPatientGatewayMock = {
			findByNameBirthAndNotCodBeneficiary: jest.fn(),
		} as unknown as jest.Mocked<FindPatientGateway>;

		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			10,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
			null,
			[],
		);

		conflictValidationGateway = new NomeNascimentoConflictValidationGateway(
			findPatientGatewayMock,
			censoDadosMock,
		);
	});

	it('deve retornar conflito se nome e nascimento já existirem e código beneficiario for diferente', async () => {
		findPatientGatewayMock.findByNameBirthAndNotCodBeneficiary.mockResolvedValue(
			{
				id: 1,
			} as Patient,
		);

		const result = await conflictValidationGateway.validate();

		expect(
			findPatientGatewayMock.findByNameBirthAndNotCodBeneficiary,
		).toHaveBeenCalledWith(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.dtNascimento,
			censoDadosMock.companyId,
			censoDadosMock.codBeneficiario,
		);
		expect(result).toEqual(
			new TipoConflito(
				4,
				'Pacientes com o mesmo nome e data de nascimento, mas códigos diferentes. Verifique se há duplicidade no cadastro.',
			),
		);
	});

	it('deve retornar null se nome e nascimento não existirem', async () => {
		findPatientGatewayMock.findByNameBirthAndNotCodBeneficiary.mockResolvedValue(
			null,
		);

		const result = await conflictValidationGateway.validate();

		expect(
			findPatientGatewayMock.findByNameBirthAndNotCodBeneficiary,
		).toHaveBeenCalledWith(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.dtNascimento,
			censoDadosMock.companyId,
			censoDadosMock.codBeneficiario,
		);
		expect(result).toBeNull();
	});
});
