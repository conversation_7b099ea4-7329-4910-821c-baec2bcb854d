import { ConflictValidationFactoryGateway as ConflictValidationFactoryGateway } from 'src/core/application/gateway/validation/conflictValidation.factory.gateway';
import { Repository } from 'typeorm';
import { HospitalizationEntity } from '../entities/hospitalization.entity';
import { CensoDados } from 'src/core/domain/CensoDados';
// import { CodigoBeneficiarioDuplicadoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/codigoBeneficiarioDuplicadoConflict.validation.strategy';
// import { CodigoBeneficiarioDuplicadoConflictValidationGateway } from './impl/codigoBeneficiarioDuplicadoConflictValidation.gateway';
import { ConflictValidationGateway } from '../../core/application/gateway/validation/abstract/conflictValidation.gateway';
import { InvalidConflictValidationException } from 'src/shared/exceptions/conflict/invalidConflictValidation.exception';
import { HttpStatus } from '@nestjs/common';
import { FindHospitalizationGatewayImpl } from '../hospitalization/findHospitalization.gateway.impl';
import { DataEntradaSuperiorAltaConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataEntradaSuperiorAltaConflict.validation.strategy';
import { DataEntradaSuperiorAltaConflictValidationGateway } from './impl/dataEntradaSuperiorAltaConflictValidation.gateway';
import { DataNascimentoFuturoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataNascimentoFuturoConflict.validation.strategy';
import { DataNascimentoFuturoConflictValidationGateway } from './impl/dataNascimentoFuturoConflictValidation.gateway';
import { MesmoCodigoGuiaInternacaoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/mesmoCodigoGuiaInternacaoConflict.validation.strategy';
import { MesmoCodigoGuiaInternacaoConflictValidationGateway } from './impl/mesmoCodigoGuiaInternacaoConflictValidation.gateway';
import { DataEntradaInferiorDataNascimentoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/dataEntradaInferiorDataNascimentoConflict.validation.strategy';
import { DataEntradaInferiorDataNascimentoConflictValidationGateway } from './impl/dataEntradaInferiorDataNascimentoConflictValidation.gateway';
import { PeriodoEntreInternacaoConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/periodoEntreInternacaoConflict.validation.strategy';
import { PeriodoEntreInternacaoConflictValidationGateway } from './impl/periodoEntreInternacaoConflictValidation.gateway';
import { CadastroInternacaoFuturaSemAltaConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/cadastroInternacaoFuturaSemAltaConflict.validation.strategy';
import { CadastroInternacaoFuturaSemAltaConflictValidationGateway } from './impl/cadastroInternacaoFuturaSemAltaConflictValidation.gateway';
import { PatientEntity } from '../entities/patient.entity';
import { HospitalNotFoundConflictValidationStrategy } from 'src/core/application/validations/strategy/impl/hospitalNotFoundConflict.validation.strategy';
import { HospitalNotFoundConflictValidationGateway } from './impl/hospitalNotFoundConflictValidation.gateway';
import { HospitalsCompanyEntity } from '../entities/hospitalsCompany.entity';
import { FindHospitalCompanyGatewayImpl } from '../hospitalCompany/findHospitalCompany.gateway.impl';
// import { FindPatientGatewayImpl } from '../patient/findPatient.gateway.impl';

export class ConflictValidationFactoryGatewayImpl
	implements ConflictValidationFactoryGateway
{
	constructor(
		private readonly hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
		private readonly hospitalizationRepository: Repository<HospitalizationEntity>,
		private readonly patientRepository: Repository<PatientEntity>,
	) {}

	public execute(
		censoDados: CensoDados,
		validationName: string,
	): ConflictValidationGateway {
		switch (validationName) {
			// case CodigoBeneficiarioDuplicadoConflictValidationStrategy.name:
			// 	return new CodigoBeneficiarioDuplicadoConflictValidationGateway(
			// 		new FindPatientGatewayImpl(this.patientRepository),
			// 		censoDados,
			// 	);
			case HospitalNotFoundConflictValidationStrategy.name:
				return new HospitalNotFoundConflictValidationGateway(
					new FindHospitalCompanyGatewayImpl(this.hospitalCompanyRepository),
					censoDados,
				);
			case DataEntradaSuperiorAltaConflictValidationStrategy.name:
				return new DataEntradaSuperiorAltaConflictValidationGateway(censoDados);
			case DataNascimentoFuturoConflictValidationStrategy.name:
				return new DataNascimentoFuturoConflictValidationGateway(censoDados);
			// case NomeNascimentoConflictValidationStrategy.name:
			// 	return new NomeNascimentoConflictValidationGateway(
			// 		new FindPatientGatewayImpl(this.patientRepository),
			// 		censoDados,
			// 	);
			case MesmoCodigoGuiaInternacaoConflictValidationStrategy.name:
				return new MesmoCodigoGuiaInternacaoConflictValidationGateway(
					new FindHospitalizationGatewayImpl(this.hospitalizationRepository),
					censoDados,
				);
			case DataEntradaInferiorDataNascimentoConflictValidationStrategy.name:
				return new DataEntradaInferiorDataNascimentoConflictValidationGateway(
					censoDados,
				);
			case PeriodoEntreInternacaoConflictValidationStrategy.name:
				return new PeriodoEntreInternacaoConflictValidationGateway(
					new FindHospitalizationGatewayImpl(this.hospitalizationRepository),
					censoDados,
				);
			case CadastroInternacaoFuturaSemAltaConflictValidationStrategy.name:
				return new CadastroInternacaoFuturaSemAltaConflictValidationGateway(
					new FindHospitalizationGatewayImpl(this.hospitalizationRepository),
					censoDados,
				);
			default:
				throw new InvalidConflictValidationException(
					HttpStatus.BAD_REQUEST,
					'Invalid validation strategy',
				);
		}
	}
}
