import { Repository } from 'typeorm';
import { InsertGuideStatusGatewayImpl } from '../../gateway/guideStatus/insertGuideStatus.gateway.impl';
import { GuideStatusEntity } from '../../gateway/entities/guideStatus.entity';
import { GuideStatus } from 'src/core/domain/GuideStatus';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { GuideStatusMapper } from 'src/shared/mapper/guide/GuideStatusMapper';
import { GuideEntity } from '../entities/guide.entity';
import { Guide } from 'src/core/domain/Guide';

describe('InsertGuideStatusGatewayImpl', () => {
	let repositoryMock: jest.Mocked<Repository<GuideStatusEntity>>;
	let gateway: InsertGuideStatusGatewayImpl;

	beforeEach(() => {
		repositoryMock = {
			create: jest.fn(),
			save: jest.fn(),
		} as unknown as jest.Mocked<Repository<GuideStatusEntity>>;

		gateway = new InsertGuideStatusGatewayImpl(repositoryMock);
	});

	it('deve criar a entidade a partir do domínio e chamar repository.save, retornando o objeto salvo (guide null)', async () => {
		const dummyGuideStatus = new GuideStatus(
			null, // id
			null, // guide (domínio) – será convertido para entidade via GuideMapper.toEntity
			100, // approvedBy
			2, // status (por exemplo: 2 - aprovado)
			50, // justificativa
			'Observação de teste', // obs
			new Date('2023-01-01T00:00:00Z'), // created
			new Date('2023-01-02T00:00:00Z'), // updated
			1, // enabled
			10, // userId
		);

		jest.spyOn(GuideMapper, 'toEntity').mockReturnValue(null);

		const dummyGuideStatusEntity: GuideStatusEntity = {
			id: dummyGuideStatus.id,
			approvedBy: dummyGuideStatus.approvedBy,
			created: dummyGuideStatus.created,
			updated: dummyGuideStatus.updated,
			enabled: dummyGuideStatus.enabled,
			guide: null,
			justificativa: dummyGuideStatus.justificativa,
			obs: dummyGuideStatus.obs,
			status: dummyGuideStatus.status,
			userId: dummyGuideStatus.userId,
		} as GuideStatusEntity;

		const expectedDomainResult = new GuideStatus(
			dummyGuideStatus.id,
			null,
			dummyGuideStatus.approvedBy,
			dummyGuideStatus.status,
			dummyGuideStatus.justificativa,
			dummyGuideStatus.obs,
			dummyGuideStatus.created,
			dummyGuideStatus.updated,
			dummyGuideStatus.enabled,
			dummyGuideStatus.userId,
		);

		repositoryMock.create.mockReturnValue(dummyGuideStatusEntity);
		repositoryMock.save.mockResolvedValue(dummyGuideStatusEntity);
		jest
			.spyOn(GuideStatusMapper, 'toDomain')
			.mockReturnValue(expectedDomainResult);

		const result = await gateway.insertGuideStatus(dummyGuideStatus);

		expect(repositoryMock.create).toHaveBeenCalledWith({
			id: dummyGuideStatus.id,
			approvedBy: dummyGuideStatus.approvedBy,
			created: dummyGuideStatus.created,
			updated: dummyGuideStatus.updated,
			enabled: dummyGuideStatus.enabled,
			guide: null,
			justificativa: dummyGuideStatus.justificativa,
			obs: dummyGuideStatus.obs,
			status: dummyGuideStatus.status,
			userId: dummyGuideStatus.userId,
		});

		expect(repositoryMock.save).toHaveBeenCalledWith(dummyGuideStatusEntity);
		expect(GuideStatusMapper.toDomain).toHaveBeenCalledWith(
			dummyGuideStatusEntity,
		);

		expect(result).toEqual(expectedDomainResult);
	});

	it('deve criar a entidade a partir do domínio e chamar repository.save, retornando o objeto salvo', async () => {
		const dummyGuideStatus = new GuideStatus(
			null, // id
			new Guide(1, null, null, null), // guide (domínio) – será convertido para entidade via GuideMapper.toEntity
			100, // approvedBy
			2, // status (por exemplo: 2 - aprovado)
			50, // justificativa
			'Observação de teste', // obs
			new Date('2023-01-01T00:00:00Z'), // created
			new Date('2023-01-02T00:00:00Z'), // updated
			1, // enabled
			10, // userId
		);

		const guideEntity = { id: 1 } as GuideEntity;
		const guideDomain = new Guide(1, null, null, null);

		jest.spyOn(GuideMapper, 'toEntity').mockReturnValue(guideEntity);
		jest.spyOn(GuideMapper, 'toDomain').mockReturnValue(guideDomain);

		const dummyGuideStatusEntity: GuideStatusEntity = {
			id: dummyGuideStatus.id,
			approvedBy: dummyGuideStatus.approvedBy,
			created: dummyGuideStatus.created,
			updated: dummyGuideStatus.updated,
			enabled: dummyGuideStatus.enabled,
			guide: guideEntity,
			justificativa: dummyGuideStatus.justificativa,
			obs: dummyGuideStatus.obs,
			status: dummyGuideStatus.status,
			userId: dummyGuideStatus.userId,
		} as GuideStatusEntity;

		const expectedDomainResult = new GuideStatus(
			dummyGuideStatus.id,
			guideDomain,
			dummyGuideStatus.approvedBy,
			dummyGuideStatus.status,
			dummyGuideStatus.justificativa,
			dummyGuideStatus.obs,
			dummyGuideStatus.created,
			dummyGuideStatus.updated,
			dummyGuideStatus.enabled,
			dummyGuideStatus.userId,
		);

		repositoryMock.create.mockReturnValue(dummyGuideStatusEntity);
		repositoryMock.save.mockResolvedValue(dummyGuideStatusEntity);
		jest
			.spyOn(GuideStatusMapper, 'toDomain')
			.mockReturnValue(expectedDomainResult);

		const result = await gateway.insertGuideStatus(dummyGuideStatus);

		expect(repositoryMock.create).toHaveBeenCalledWith({
			id: dummyGuideStatus.id,
			approvedBy: dummyGuideStatus.approvedBy,
			created: dummyGuideStatus.created,
			updated: dummyGuideStatus.updated,
			enabled: dummyGuideStatus.enabled,
			guide: { id: 1 },
			justificativa: dummyGuideStatus.justificativa,
			obs: dummyGuideStatus.obs,
			status: dummyGuideStatus.status,
			userId: dummyGuideStatus.userId,
		});

		expect(repositoryMock.save).toHaveBeenCalledWith(dummyGuideStatusEntity);
		expect(GuideStatusMapper.toDomain).toHaveBeenCalledWith(
			dummyGuideStatusEntity,
		);

		expect(result).toEqual(expectedDomainResult);
	});
});
