import { InsertGuideStatusGateway } from 'src/core/application/gateway/guideStatus/insertGuideStatus.gateway';
import { GuideStatus } from 'src/core/domain/GuideStatus';
import { Repository } from 'typeorm';
import { GuideStatusEntity } from '../entities/guideStatus.entity';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';
import { GuideStatusMapper } from 'src/shared/mapper/guide/GuideStatusMapper';

export class InsertGuideStatusGatewayImpl implements InsertGuideStatusGateway {
	constructor(
		private readonly guideStatusRepository: Repository<GuideStatusEntity>,
	) {}

	public async insertGuideStatus(
		guideStatus: GuideStatus,
	): Promise<GuideStatus> {
		const entity = this.createGuideStatusEntity(guideStatus);
		const newGuide = await this.guideStatusRepository.save(entity);
		return GuideStatusMapper.toDomain(newGuide);
	}

	public createGuideStatusEntity(guideStatus: GuideStatus): GuideStatusEntity {
		return this.guideStatusRepository.create({
			id: guideStatus.id,
			approvedBy: guideStatus.approvedBy,
			created: guideStatus.created,
			updated: guideStatus.updated,
			enabled: guideStatus.enabled,
			guide: guideStatus.guide ? GuideMapper.toEntity(guideStatus.guide) : null,
			justificativa: guideStatus.justificativa,
			obs: guideStatus.obs,
			status: guideStatus.status,
			userId: guideStatus.userId,
		});
	}
}
