import { InsertFormCidsGateway } from 'src/core/application/gateway/formCids/insertFormCids.gateway';
import { FormCids } from 'src/core/domain/FormCids';
import { Repository } from 'typeorm';
import { FormCidsEntity } from '../entities/formCids.entity';
import { FormCidsMapper } from 'src/shared/mapper/formCids/FormCidsMapper';

export class InsertFormCidsGatewayImpl implements InsertFormCidsGateway {
	constructor(private repository: Repository<FormCidsEntity>) {}

	public async insertFormCids(formCids: FormCids): Promise<FormCids> {
		const formEntityCids = this.createFormCids(formCids);
		const result = await this.repository.save(formEntityCids);
		return FormCidsMapper.toDomain(result);
	}

	private createFormCids(formCids: FormCids): FormCidsEntity {
		return this.repository.create({
			isCenso: formCids.isCenso,
			status: formCids.status,
			updated: formCids.updated,
			enabled: formCids.enabled,
			created: formCids.created,
			codIntegration: formCids.codIntegration,
			cidSubCategoriaId: formCids.cidSubCategoriaId,
			cidCategoriaId: formCids.cidCategoriaId,
			type: formCids.type,
			userId: formCids.userId,
			patientWay: { id: formCids.patientWay.id },
		});
	}
}
