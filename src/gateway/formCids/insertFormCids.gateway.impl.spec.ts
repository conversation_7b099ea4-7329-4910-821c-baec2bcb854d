import { FormCids } from 'src/core/domain/FormCids';
import { Repository } from 'typeorm';
import { FormCidsEntity } from '../entities/formCids.entity';
import { FormCidsMapper } from 'src/shared/mapper/formCids/FormCidsMapper';
import { InsertFormCidsGatewayImpl } from './insertFormCids.gateway.impl';

describe('InsertFormCidsGatewayImpl', () => {
	let gateway: InsertFormCidsGatewayImpl;
	let repository: jest.Mocked<Repository<FormCidsEntity>>;

	const formCidsDomain: FormCids = {
		isCenso: true,
		status: 'active',
		updated: new Date('2023-04-01T00:00:00Z'),
		enabled: true,
		created: new Date('2023-03-01T00:00:00Z'),
		codIntegration: 'cod-123',
		cidSubCategoriaId: 10,
		cidCategoriaId: 20,
		type: 'test',
		userId: 100,
		patientWay: { id: 5 },
	} as unknown as FormCids;

	const createdEntity: FormCidsEntity = {
		isCenso: formCidsDomain.isCenso,
		status: formCidsDomain.status,
		updated: formCidsDomain.updated,
		enabled: formCidsDomain.enabled,
		created: formCidsDomain.created,
		codIntegration: formCidsDomain.codIntegration,
		cidSubCategoriaId: formCidsDomain.cidSubCategoriaId,
		cidCategoriaId: formCidsDomain.cidCategoriaId,
		type: formCidsDomain.type,
		userId: formCidsDomain.userId,
		patientWay: { id: formCidsDomain.patientWay.id },
	} as FormCidsEntity;

	const savedEntity: FormCidsEntity = {
		...createdEntity,
		id: 1,
	};

	const mappedDomain: FormCids = {
		...formCidsDomain,
		id: 1,
	} as FormCids;

	beforeEach(() => {
		repository = {
			create: jest.fn().mockImplementation((entity) => entity),
			save: jest.fn().mockResolvedValue(savedEntity),
		} as unknown as jest.Mocked<Repository<FormCidsEntity>>;

		gateway = new InsertFormCidsGatewayImpl(repository);

		jest.spyOn(FormCidsMapper, 'toDomain').mockReturnValue(mappedDomain);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('deve criar a entidade de FormCids, salvá-la e retornar o domínio mapeado', async () => {
		const result = await gateway.insertFormCids(formCidsDomain);

		expect(repository.create).toHaveBeenCalledWith({
			isCenso: formCidsDomain.isCenso,
			status: formCidsDomain.status,
			updated: formCidsDomain.updated,
			enabled: formCidsDomain.enabled,
			created: formCidsDomain.created,
			codIntegration: formCidsDomain.codIntegration,
			cidSubCategoriaId: formCidsDomain.cidSubCategoriaId,
			cidCategoriaId: formCidsDomain.cidCategoriaId,
			type: formCidsDomain.type,
			userId: formCidsDomain.userId,
			patientWay: { id: formCidsDomain.patientWay.id },
		});

		expect(repository.save).toHaveBeenCalledWith(createdEntity);

		expect(FormCidsMapper.toDomain).toHaveBeenCalledWith(savedEntity);

		expect(result).toEqual(mappedDomain);
	});
});
