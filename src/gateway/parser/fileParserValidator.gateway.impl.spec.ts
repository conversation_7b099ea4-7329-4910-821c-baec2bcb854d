import { File } from 'src/core/domain/File';
import { ContentValidation } from 'src/core/application/validations/content.validation';
import { parseFile } from 'fast-csv';
import { requiredFields } from 'src/helpers/requiredCensoFields.helper';
import { FileParserValidatorGatewayImpl } from './fileParserValidator.gateway.Impl';
import { FileParserValidatorGateway } from 'src/core/application/gateway/parser/fileParserValidator.gateway';
import { InvalidContentException } from 'src/shared/exceptions/upload/InvalidContent.exception';

jest.mock('fast-csv', () => ({
	parseFile: jest.fn(),
}));

jest.mock('fs', () => ({
	createReadStream: jest.fn(() => ({
		on: jest.fn().mockImplementation(function (_event, handler) {
			handler();
			return this;
		}),
		close: jest.fn(),
	})),
}));

jest.mock('src/core/application/validations/content.validation');

describe('FileParserValidatorGatewayImpl', () => {
	let mockFile: File;
	let fileParserValidatorGateway: FileParserValidatorGateway;
	let mockContentValidation: jest.Mocked<ContentValidation>;

	beforeEach(() => {
		mockFile = new File(
			'test.csv',
			'text/csv',
			1024,
			'/path/to/test.csv',
			10,
			1,
			'hash_value',
		);
		fileParserValidatorGateway = new FileParserValidatorGatewayImpl();
		mockContentValidation = new ContentValidation(
			requiredFields,
		) as jest.Mocked<ContentValidation>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should parse CSV file and validate each row successfully', async () => {
		const mockCsvData = [
			{ field1: 'value1', field2: 'value2' },
			{ field1: 'value3', field2: 'value4' },
		];

		(parseFile as jest.Mock).mockImplementation(() => {
			const mockParseStream = {
				on: (event: string, callback: (data?: object) => void): object => {
					if (event === 'data') {
						mockCsvData.forEach((row) => callback(row));
					}
					if (event === 'end') {
						callback();
					}
					return mockParseStream;
				},
			};
			return mockParseStream;
		});

		mockContentValidation.validate = jest.fn();

		await expect(
			fileParserValidatorGateway.parse(mockFile),
		).resolves.not.toThrow();
	});

	it('should reject with an error if parsing the CSV file fails', async () => {
		const mockError = new InvalidContentException(
			400,
			`Por favor, verifique a coluna que não foi preenchida e preencha-a antes de importar o arquivo novamente.`,
			['data'],
		);
		(parseFile as jest.Mock).mockImplementation(() => {
			const mockParseStream = {
				on: (event: string, callback: (data?: object) => void): object => {
					if (event === 'error') {
						callback(mockError);
					}
					return mockParseStream;
				},
			};
			return mockParseStream;
		});

		await expect(fileParserValidatorGateway.parse(mockFile)).rejects.toThrow(
			'Por favor, verifique a coluna que não foi preenchida e preencha-a antes de importar o arquivo novamente.',
		);
	});
});
