import { FileParserValidatorGateway } from 'src/core/application/gateway/parser/fileParserValidator.gateway';
import { File } from 'src/core/domain/File';
import { requiredFields } from 'src/helpers/requiredCensoFields.helper';
import { ContentValidation } from 'src/core/application/validations/content.validation';
import { parseFile } from 'fast-csv';
import { sanitizeHeader } from 'src/helpers/sanitizeHeader.helper';
import { detectDelimiter } from 'src/helpers/detectDelimiter';

export class FileParserValidatorGatewayImpl
	implements FileParserValidatorGateway
{
	public async parse(file: File): Promise<number> {
		const contentValidation = new ContentValidation(
			requiredFields.map(sanitizeHeader),
		);
		const caminhoCsv = file.caminhoInterno;
		return await this.startValidateParsing(caminhoCsv, contentValidation);
	}

	private async startValidateParsing(
		csvPath: string,
		contentValidation: ContentValidation,
	): Promise<number> {
		let lineCount = 0;
		const delimiter = await detectDelimiter(csvPath);
		return new Promise(async (resolve, reject) => {
			parseFile(csvPath, {
				delimiter: delimiter,
				headers: true,
			})
				.on('data', (row: Record<string, string>) => {
					const lowerCaseRow = Object.fromEntries(
						Object.entries(row).map(([key, value]) => [
							sanitizeHeader(key),
							value.toLowerCase(),
						]),
					);
					contentValidation.validate(lowerCaseRow);
					lineCount++;
				})
				.on('end', () => resolve(lineCount))
				.on('error', reject);
		});
	}
}
