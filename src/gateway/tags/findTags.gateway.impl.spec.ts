import { Test, TestingModule } from '@nestjs/testing';
import { ILike, Repository } from 'typeorm';
import { TagsEntity } from '../entities/tags.entity';
import { FindTagsGatewayImpl } from './findTags.gateway.impl';
import { TagsMapper } from 'src/shared/mapper/tags/tags.mapper';
import { Tags } from 'src/core/domain/Tags';

describe('FindTagsGatewayImpl', () => {
	let findTagsGatewayImpl: FindTagsGatewayImpl;
	let tagsRepository: Repository<TagsEntity>;

	const mockTagsEntity: TagsEntity = {
		id: 1,
		tag: 'Test Tag',
		companyId: 1,
		modulo: 'test',
		habilitado: true,
		enabled: true,
		dataCriacao: new Date(),
	} as TagsEntity;

	const mockTagsDomain: Tags = new Tags(1, true, 'Test Tag', 'test');

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'TAGS_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: FindTagsGatewayImpl,
					useFactory: (tagsRepository: Repository<TagsEntity>) =>
						new FindTagsGatewayImpl(tagsRepository),
					inject: ['TAGS_REPOSITORY'],
				},
			],
		}).compile();

		findTagsGatewayImpl = module.get<FindTagsGatewayImpl>(FindTagsGatewayImpl);
		tagsRepository = module.get<Repository<TagsEntity>>('TAGS_REPOSITORY');
	});

	describe('searchTags', () => {
		it('deve buscar tags com os filtros corretos e retornar o domínio mapeado', async () => {
			const name = 'Test';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			(tagsRepository.find as jest.Mock).mockResolvedValue([mockTagsEntity]);
			jest.spyOn(TagsMapper, 'toTagsDomain').mockReturnValue(mockTagsDomain);

			const result = await findTagsGatewayImpl.searchTags(
				name,
				companyId,
				modulo,
				limit,
			);

			expect(tagsRepository.find).toHaveBeenCalledWith({
				where: {
					tag: ILike(`%${name}%`),
					companyId,
					modulo,
					habilitado: true,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toHaveLength(1);
			expect(result[0]).toBe(mockTagsDomain);
			expect(TagsMapper.toTagsDomain).toHaveBeenCalledWith(mockTagsEntity);
		});

		it('deve retornar array vazio quando nenhuma tag é encontrada', async () => {
			const name = 'NonExistent';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			(tagsRepository.find as jest.Mock).mockResolvedValue([]);

			const result = await findTagsGatewayImpl.searchTags(
				name,
				companyId,
				modulo,
				limit,
			);

			expect(result).toHaveLength(0);
			expect(tagsRepository.find).toHaveBeenCalledWith({
				where: {
					tag: ILike(`%${name}%`),
					companyId,
					modulo,
					habilitado: true,
					enabled: true,
				},
				take: limit,
			});
		});

		it('deve propagar erro quando repository falhar', async () => {
			const name = 'Test';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			const errorMessage = 'Database error';
			(tagsRepository.find as jest.Mock).mockRejectedValue(
				new Error(errorMessage),
			);

			await expect(
				findTagsGatewayImpl.searchTags(name, companyId, modulo, limit),
			).rejects.toThrow(errorMessage);

			expect(tagsRepository.find).toHaveBeenCalledWith({
				where: {
					tag: ILike(`%${name}%`),
					companyId,
					modulo,
					habilitado: true,
					enabled: true,
				},
				take: limit,
			});
		});

		it('deve buscar tags com string vazia quando name não for fornecido', async () => {
			const name = '';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			(tagsRepository.find as jest.Mock).mockResolvedValue([mockTagsEntity]);
			jest.spyOn(TagsMapper, 'toTagsDomain').mockReturnValue(mockTagsDomain);

			const result = await findTagsGatewayImpl.searchTags(
				name,
				companyId,
				modulo,
				limit,
			);

			expect(tagsRepository.find).toHaveBeenCalledWith({
				where: {
					tag: ILike(`%%`),
					companyId,
					modulo,
					habilitado: true,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toHaveLength(1);
			expect(result[0]).toBe(mockTagsDomain);
		});
	});
});
