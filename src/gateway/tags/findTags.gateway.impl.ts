import { ILike, Repository } from 'typeorm';
import { FindTagsGateway } from 'src/core/application/gateway/tags/findTags.gateway';
import { TagsEntity } from '../entities/tags.entity';
import { Tags } from 'src/core/domain/Tags';
import { TagsMapper } from 'src/shared/mapper/tags/tags.mapper';

export class FindTagsGatewayImpl implements FindTagsGateway {
	constructor(private tagsRepository: Repository<TagsEntity>) {}

	async searchTags(
		name: string,
		companyId: number,
		modulo: string,
		limit: number,
	): Promise<Tags[]> {
		const tagsEntity = await this.tagsRepository.find({
			where: {
				tag: ILike(`%${name || ''}%`),
				companyId,
				modulo,
				habilitado: true,
				enabled: true,
			},
			take: limit,
		});

		return tagsEntity.map((tag) => TagsMapper.toTagsDomain(tag));
	}
}
