import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { Patient } from 'src/core/domain/Patient';
import { Not, Repository } from 'typeorm';
import { FindPatientGatewayImpl } from './findPatient.gateway.impl';
import { PatientEntity } from '../entities/patient.entity';

describe('FindPatientGateway', () => {
	let censoDadosMock: CensoDados;
	let repoMock: jest.Mocked<Repository<PatientEntity>>;
	let findPatientGateway: FindPatientGateway;
	const patientEntityMock: PatientEntity = {
		id: 1,
		name: '<PERSON>',
		birthday: new Date('2000-01-01'),
		companyId: 1,
		enabled: 1,
		codBeneficiario: '*********',
	} as PatientEntity;
	beforeEach(() => {
		censoDadosMock = new CensoDados(
			1, // id
			2, // censoId
			3, // companyId
			1, //userId
			new Date(), // dataCriacao
			1, // conflito
			new Date(), // data
			'São Paulo', // municipio
			'Hospital São Luiz', // hospitalCredenciado
			'Controle A', // controle
			new Date('2000-01-01'), // dtNascimento
			new Date('2024-10-10'), // dataInternacao
			null, // dataAlta
			'', // motivoAlta
			'Diagnóstico A', // diagnostico
			'', // diagnosticoSecundario
			null, // previsaoAlta
			'Eletiva', // caraterInternacao
			'Clínica', // tipoInternacao
			'12345', // codigoGuia
			'', // altoCustoStatus
			'João Silva', // nomeBeneficiario
			'*********', // codBeneficiario
			'Campinas', // cidadeBeneficiario
			'SP', // estadoBeneficiario
			false, // recemNascido
			'', // tipoCliente
			0, // valorDiaria
			'', // regionalBeneficiario
			'', // tipoControle
			3, // diariasAutorizadas
			'123', // codigoHospital
			'456', // codigoPlano
			'Plano A', // nomePlano
			'789', // codigoEmpresa
			'Empresa X', // nomeEmpresa
			'Ativo', // statusPlano
			new Date('2022-01-01'),
		);
		repoMock = {
			findOne: jest.fn(),
		} as unknown as jest.Mocked<Repository<PatientEntity>>;
		findPatientGateway = new FindPatientGatewayImpl(repoMock);
	});

	it('deve achar um paciente pelo nome e nascimento', async () => {
		repoMock.findOne.mockResolvedValue(patientEntityMock);

		const result = await findPatientGateway.findByNameBirthAndNotCodBeneficiary(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.dtNascimento,
			censoDadosMock.companyId,
			censoDadosMock.codBeneficiario,
		);
		expect(repoMock.findOne).toHaveBeenCalledTimes(1);
		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				name: censoDadosMock.nomeBeneficiario,
				birthday: censoDadosMock.dtNascimento,
				companyId: censoDadosMock.companyId,
				enabled: 1,
				codBeneficiario: Not(censoDadosMock.codBeneficiario),
			},
		});
		expect(result).toBeInstanceOf(Patient);
	});

	it('ao nao encontrar um paciente pelo nome e nascimento deve retornar nulo', async () => {
		repoMock.findOne.mockResolvedValue(null);

		const result = await findPatientGateway.findByNameBirthAndNotCodBeneficiary(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.dtNascimento,
			censoDadosMock.companyId,
			censoDadosMock.codBeneficiario,
		);
		expect(repoMock.findOne).toHaveBeenCalledTimes(1);
		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				name: censoDadosMock.nomeBeneficiario,
				birthday: censoDadosMock.dtNascimento,
				companyId: censoDadosMock.companyId,
				enabled: 1,
				codBeneficiario: Not(censoDadosMock.codBeneficiario),
			},
		});
		expect(result).toBe(null);
	});

	it('deve encontrar um paciente pelo codigo beneficiario', async () => {
		repoMock.findOne.mockResolvedValue(patientEntityMock);
		const result = await findPatientGateway.findByCodBeneficiary(
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);

		expect(repoMock.findOne).toHaveBeenCalledTimes(1);
		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				codBeneficiario: censoDadosMock.codBeneficiario,
				companyId: censoDadosMock.companyId,
				enabled: 1,
			},
		});
		expect(result).toBeInstanceOf(Patient);
	});

	it('ao nao encontrar um paciente codigo beneficiario deve retornar nulo', async () => {
		repoMock.findOne.mockResolvedValue(null);
		const result = await findPatientGateway.findByCodBeneficiary(
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);

		expect(repoMock.findOne).toHaveBeenCalledTimes(1);
		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				codBeneficiario: censoDadosMock.codBeneficiario,
				companyId: censoDadosMock.companyId,
				enabled: 1,
			},
		});
		expect(result).toBe(null);
	});

	it('deve encontrar um paciente pelo codigo beneficiario e nao pelo nome', async () => {
		repoMock.findOne.mockResolvedValue(patientEntityMock);
		const result = await findPatientGateway.findByCodBeneficiaryAndNotName(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);

		expect(repoMock.findOne).toHaveBeenCalledTimes(1);
		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				name: Not(censoDadosMock.nomeBeneficiario),
				codBeneficiario: censoDadosMock.codBeneficiario,
				companyId: censoDadosMock.companyId,
				enabled: 1,
			},
		});
		expect(result).toBeInstanceOf(Patient);
	});

	it('deve retornar null se nao encontrar nenhum paciente pelo codigo beneficiario e nao pelo nome', async () => {
		repoMock.findOne.mockResolvedValue(null);
		const result = await findPatientGateway.findByCodBeneficiaryAndNotName(
			censoDadosMock.nomeBeneficiario,
			censoDadosMock.codBeneficiario,
			censoDadosMock.companyId,
		);

		expect(repoMock.findOne).toHaveBeenCalledWith({
			select: { id: true },
			where: {
				name: Not(censoDadosMock.nomeBeneficiario),
				codBeneficiario: censoDadosMock.codBeneficiario,
				companyId: censoDadosMock.companyId,
				enabled: 1,
			},
		});
		expect(result).toBeNull();
	});
});
