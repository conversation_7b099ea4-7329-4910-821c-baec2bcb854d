import { Not, Repository } from 'typeorm';
import { FindPatientGateway } from '../../core/application/gateway/patient/findPatient.gateway';
import { PatientEntity } from '../entities/patient.entity';
import { Patient } from 'src/core/domain/Patient';
import { PatientMapper } from 'src/shared/mapper/hospitalization/PatientMapper';
export class FindPatientGatewayImpl implements FindPatientGateway {
	constructor(private readonly patientRepository: Repository<PatientEntity>) {}

	public async findByNameBirthAndNotCodBeneficiary(
		name: string,
		birth: Date,
		companyId: number,
		codBeneficiary: string,
	): Promise<Patient> {
		const patient = await this.patientRepository.findOne({
			select: { id: true },
			where: {
				name: name,
				birthday: birth,
				companyId: companyId,
				enabled: 1,
				codBeneficiario: Not(codBeneficiary),
			},
		});
		if (!patient) return null;

		return PatientMapper.toPatientDomain(patient);
	}

	public async findByCodBeneficiary(
		codBeneficiary: string,
		companyId: number,
	): Promise<Patient> {
		const patient = this.patientRepository.findOne({
			select: { id: true },
			where: {
				codBeneficiario: codBeneficiary,
				companyId: companyId,
				enabled: 1,
			},
		});
		if (!(await patient)) return null;
		return PatientMapper.toPatientDomain(await patient);
	}

	public async findByCodBeneficiaryAndNotName(
		name: string,
		codBeneficiario: string,
		companyId: number,
	): Promise<Patient> {
		const patient = this.patientRepository.findOne({
			select: { id: true },
			where: {
				name: Not(name),
				codBeneficiario: codBeneficiario,
				companyId: companyId,
				enabled: 1,
			},
		});
		if (!(await patient)) return null;
		return PatientMapper.toPatientDomain(await patient);
	}
}
