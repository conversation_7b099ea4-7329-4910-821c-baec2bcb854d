import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { Patient } from 'src/core/domain/Patient';
import { Repository } from 'typeorm';
import { PatientEntity } from '../entities/patient.entity';

export class InsertPatientGatewayImpl implements InsertPatientGateway {
	constructor(private patientRepository: Repository<PatientEntity>) {}

	public async insertPatient(patient: Patient): Promise<number> {
		const patientEntity = this.createPatientEntity(patient);

		return (
			await this.patientRepository.upsert(patientEntity, [
				'codBeneficiario',
				'enabled',
			])
		).identifiers[0].id;
	}

	public createPatientEntity(patient: Patient): PatientEntity {
		return this.patientRepository.create({
			id: patient.id,
			enabled: patient.enabled,
			created: patient.created,
			updated: patient.updated,
			userId: patient.userId,
			name: patient.name,
			email: patient.email,
			motherName: patient.motherName,
			companyId: patient.companyId,
			gender: patient.gender,
			codBeneficiario: patient.codBeneficiario,
			birthday: patient.birthday,
			planId: patient.planId,
			planStartDate: patient.planStartDate,
			planEndDate: patient.planEndDate,
			group: patient.group,
			occupation: patient.occupation,
			pregnantAddInfo: patient.pregnantAddInfo,
			lactatingAddInfo: patient.lactatingAddInfo,
			permissionAppMaxDate: patient.permissionAppMaxDate,
			startDate: patient.startDate,
			image: patient.image,
			tel: patient.tel,
			patientCity: patient.patientCity,
			codCidadeIbge: patient.codCidadeIbge,
			regHosp: patient.regHosp,
			color: patient.color,
			cpf: patient.cpf,
			internadoRepasse: patient.internadoRepasse,
			isCenso: patient.isCenso,
			replicateFlag: patient.replicateFlag,
			cityId: patient.cityId,
			street: patient.street,
			homeNumber: patient.homeNumber,
			neighborhood: patient.neighborhood,
			zipcode: patient.zipcode,
			complement: patient.complement,
			corporationId: patient.corporationId,
			status: patient.status,
			error: patient.error,
			codIntegration: patient.codIntegration,
			planRegulamentado: patient.planRegulamentado,
			planoTipo: patient.planoTipo,
			planoAbrangencia: patient.planoAbrangencia,
			planoTipoRede: patient.planoTipoRede,
			planoCarencia: patient.planoCarencia,
			gruposCidadesIbge: patient.gruposCidadesIbge,
			isRn: patient.isRn,
			carteiraNacionalSaude: patient.carteiraNacionalSaude,
			idade: patient.idade,
			sexo: patient.sexo,
			massa: patient.massa,
			altura: patient.altura,
			superficieCorporal: patient.superficieCorporal,
		});
	}
}
