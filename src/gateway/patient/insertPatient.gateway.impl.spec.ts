import { Repository } from 'typeorm';
import { PatientEntity } from '../entities/patient.entity';
import { Patient } from 'src/core/domain/Patient';
import { InsertPatientGatewayImpl } from './insertPatient.gateway.impl';

describe('InsertPatientGatewayImpl', () => {
	let insertPatientGateway: InsertPatientGatewayImpl;
	let patientRepository: jest.Mocked<Repository<PatientEntity>>;

	beforeEach(() => {
		patientRepository = {
			create: jest.fn(),
			upsert: jest.fn().mockResolvedValue({
				identifiers: [{ id: 1 }],
			}),
		} as unknown as jest.Mocked<Repository<PatientEntity>>;

		insertPatientGateway = new InsertPatientGatewayImpl(patientRepository);
	});

	it('deve criar uma entidade PatientEntity corretamente', () => {
		const patient: Patient = new Patient(
			1,
			1,
			new Date(),
			new Date(),
			123,
			'<PERSON>',
			'<EMAIL>',
		);

		const patientEntity = new PatientEntity();
		patientEntity.id = 1;
		patientEntity.enabled = 1;
		patientEntity.created = patient.created;
		patientEntity.updated = patient.updated;
		patientEntity.userId = 123;
		patientEntity.name = 'John Doe';
		patientEntity.email = '<EMAIL>';

		patientRepository.create.mockReturnValue(patientEntity);

		const result = insertPatientGateway.createPatientEntity(patient);

		expect(patientRepository.create).toHaveBeenCalledWith(
			expect.objectContaining({
				id: 1,
				name: 'John Doe',
				email: '<EMAIL>',
			}),
		);

		expect(result).toEqual(patientEntity);
	});

	it('deve inserir um paciente corretamente', async () => {
		const patient: Patient = new Patient(
			1,
			1,
			new Date(),
			new Date(),
			123,
			'John Doe',
			'<EMAIL>',
		);

		const patientEntity = new PatientEntity();
		patientEntity.id = 1;
		patientEntity.enabled = 1;
		patientEntity.created = patient.created;
		patientEntity.updated = patient.updated;
		patientEntity.userId = 123;
		patientEntity.name = 'John Doe';
		patientEntity.email = '<EMAIL>';

		patientRepository.create.mockReturnValue(patientEntity);

		const result = await insertPatientGateway.insertPatient(patient);

		expect(patientRepository.create).toHaveBeenCalledWith(
			expect.objectContaining({
				id: 1,
				name: 'John Doe',
				email: '<EMAIL>',
			}),
		);

		expect(patientRepository.upsert).toHaveBeenCalledWith(patientEntity, [
			'codBeneficiario',
			'enabled',
		]);
		expect(result).toEqual(1);
	});
});
