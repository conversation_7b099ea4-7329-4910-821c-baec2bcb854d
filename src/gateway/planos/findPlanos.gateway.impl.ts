import { ILike, Repository } from 'typeorm';
import { FindPlanosGateway } from '../../core/application/gateway/planos/findPlanos.gateway';
import { PlanosEntity } from '../entities/planos.entity';
import { Planos } from '../../core/domain/Planos';
import { PlanosMapper } from '../../shared/mapper/planos/planos.mapper';

export class FindPlanosGatewayImpl implements FindPlanosGateway {
	constructor(private readonly planosRepository: Repository<PlanosEntity>) {}

	async searchPlanos(
		name: string,
		companyId: number,
		limit: number,
	): Promise<Planos[]> {
		const result = await this.planosRepository.find({
			where: {
				name: <PERSON><PERSON>(`%${name || ''}%`),
				companyId,
				enabled: true,
			},
			take: limit,
		});

		if (!result) return null;
		return result.map((plano) => PlanosMapper.toPlanosDomain(plano));
	}
}
