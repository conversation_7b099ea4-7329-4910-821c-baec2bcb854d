import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { PlanosEntity } from '../entities/planos.entity';
import { FindPlanosGatewayImpl } from './findPlanos.gateway.impl';
import { Planos } from 'src/core/domain/Planos';
import { PlanosMapper } from 'src/shared/mapper/planos/planos.mapper';
import { TipoContrato } from 'src/core/domain/TipoContrato';
import { TipoContratoEntity } from '../entities/tipoContrato.entity';

describe('FindPlanosGatewayImpl', () => {
	let findPlanosGatewayImpl: FindPlanosGatewayImpl;
	let planosRepository: Repository<PlanosEntity>;

	// Mock TipoContratoEntity
	const mockTipoContratoEntity = new TipoContratoEntity();
	mockTipoContratoEntity.id = 1;
	mockTipoContratoEntity.name = 'Tipo Contrato Test';
	mockTipoContratoEntity.codInterno = 'TC001';

	// Mock PlanosEntity
	const mockPlanosEntity = new PlanosEntity();
	mockPlanosEntity.id = 1;
	mockPlanosEntity.name = 'Plano Test';
	mockPlanosEntity.companyId = 100;
	mockPlanosEntity.codInterno = 'P001';
	mockPlanosEntity.enabled = true;
	mockPlanosEntity.created = new Date();
	mockPlanosEntity.updated = new Date();
	mockPlanosEntity.tipoContrato = mockTipoContratoEntity;

	// Mock Planos domain model
	const mockTipoContrato = new TipoContrato(1, 'Tipo Contrato Test', 'TC001');

	const mockPlanos = new Planos(1, 'Plano Test', mockTipoContrato);

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'PLANOS_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: 'FindPlanosGateway',
					useFactory: (planosRepository: Repository<PlanosEntity>) =>
						new FindPlanosGatewayImpl(planosRepository),
					inject: ['PLANOS_REPOSITORY'],
				},
			],
		}).compile();

		findPlanosGatewayImpl =
			module.get<FindPlanosGatewayImpl>('FindPlanosGateway');
		planosRepository =
			module.get<Repository<PlanosEntity>>('PLANOS_REPOSITORY');
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('searchPlanos', () => {
		it('deve encontrar planos pelo nome', async () => {
			const name = 'Plano';
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(planosRepository, 'find')
				.mockResolvedValue([mockPlanosEntity]);

			jest.spyOn(PlanosMapper, 'toPlanosDomain').mockReturnValue(mockPlanos);

			const result = await findPlanosGatewayImpl.searchPlanos(
				name,
				companyId,
				limit,
			);

			expect(planosRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(), // ILike is hard to test directly
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(Planos);
			expect(result[0].id).toBe(mockPlanos.id);
			expect(result[0].nome).toBe(mockPlanos.nome);

			// Verificar o objeto TipoContrato
			expect(result[0].tipoContrato).toBeInstanceOf(TipoContrato);
			expect(result[0].tipoContrato.id).toBe(mockPlanos.tipoContrato.id);
			expect(result[0].tipoContrato.nome).toBe(mockPlanos.tipoContrato.nome);
			expect(result[0].tipoContrato.codigoInterno).toBe(
				mockPlanos.tipoContrato.codigoInterno,
			);
		});
		it('deve encontrar planos pelo nome mesmo se for vazio', async () => {
			const name: string = null;
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(planosRepository, 'find')
				.mockResolvedValue([mockPlanosEntity]);

			jest.spyOn(PlanosMapper, 'toPlanosDomain').mockReturnValue(mockPlanos);

			const result = await findPlanosGatewayImpl.searchPlanos(
				name,
				companyId,
				limit,
			);

			expect(planosRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(), // ILike is hard to test directly
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(1);
			expect(result[0]).toBeInstanceOf(Planos);
			expect(result[0].id).toBe(mockPlanos.id);
			expect(result[0].nome).toBe(mockPlanos.nome);

			// Verificar o objeto TipoContrato
			expect(result[0].tipoContrato).toBeInstanceOf(TipoContrato);
			expect(result[0].tipoContrato.id).toBe(mockPlanos.tipoContrato.id);
			expect(result[0].tipoContrato.nome).toBe(mockPlanos.tipoContrato.nome);
			expect(result[0].tipoContrato.codigoInterno).toBe(
				mockPlanos.tipoContrato.codigoInterno,
			);
		});

		it('deve retornar uma lista vazia quando não encontrar planos', async () => {
			const name = 'Plano Inexistente';
			const companyId = 100;
			const limit = 10;

			jest.spyOn(planosRepository, 'find').mockResolvedValue([]);

			const result = await findPlanosGatewayImpl.searchPlanos(
				name,
				companyId,
				limit,
			);

			expect(planosRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeDefined();
			expect(result).toHaveLength(0);
		});

		it('deve retornar null quando o resultado for null', async () => {
			const name = 'Plano';
			const companyId = 100;
			const limit = 10;

			jest.spyOn(planosRepository, 'find').mockResolvedValue(null);

			const result = await findPlanosGatewayImpl.searchPlanos(
				name,
				companyId,
				limit,
			);

			expect(planosRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});

			expect(result).toBeNull();
		});

		it('deve lidar com erros do banco de dados', async () => {
			const name = 'Plano';
			const companyId = 100;
			const limit = 10;

			jest
				.spyOn(planosRepository, 'find')
				.mockRejectedValue(new Error('DB Error'));

			await expect(
				findPlanosGatewayImpl.searchPlanos(name, companyId, limit),
			).rejects.toThrow('DB Error');

			expect(planosRepository.find).toHaveBeenCalledWith({
				where: {
					name: expect.anything(),
					companyId,
					enabled: true,
				},
				take: limit,
			});
		});
	});
});
