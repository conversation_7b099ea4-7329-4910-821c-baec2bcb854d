import { FindCompanyConfigGateway } from 'src/core/application/gateway/companyConfig/findCompanyConfig.gateway';
import { Repository } from 'typeorm';
import { CompanyConfigEntity } from '../entities/companyConfig.entity';
import { CompanyConfig } from 'src/core/domain/CompanyConfig';

export class FindCompanyConfigGatewayImpl implements FindCompanyConfigGateway {
	constructor(
		private readonly companyConfigRepository: Repository<CompanyConfigEntity>,
	) {}

	public async findCompanyConfigByCompanyId(
		companyId: number,
	): Promise<CompanyConfig> {
		const companyConfig = await this.companyConfigRepository.find({
			where: {
				company: {
					id: companyId,
				},
				enabled: true,
			},
		});
		return companyConfig.reduce((acc, config) => {
			const category = config.configFunction.categoryId;
			const configData = {
				id: config.id,
				value: config.value,
				enabled: config.enabled,
				name: config.configFunction.id,
			};

			if (!acc[category]) {
				acc[category] = [];
			}

			acc[category].push(configData);
			return acc;
		}, {} as CompanyConfig);
	}
}
