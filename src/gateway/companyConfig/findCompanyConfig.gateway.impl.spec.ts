import { Test, TestingModule } from '@nestjs/testing';
import { Repository } from 'typeorm';
import { FindCompanyConfigGatewayImpl } from './findCompanyConfig.gateway.impl';
import { CompanyConfigEntity } from '../entities/companyConfig.entity';

describe('FindCompanyConfigGatewayImpl', () => {
	let findCompanyConfigGatewayImpl: FindCompanyConfigGatewayImpl;
	let companyConfigRepository: Repository<CompanyConfigEntity>;

	// Mock CompanyConfigEntity
	const mockConfigFunction = {
		id: 'function1',
		categoryId: 'category1',
	};

	const mockCompanyConfigEntity1 = {
		id: 1,
		value: 'value1',
		enabled: true,
		configFunction: mockConfigFunction,
		company: {
			id: 100,
		},
	} as CompanyConfigEntity;

	const mockCompanyConfigEntity2 = {
		id: 2,
		value: 'value2',
		enabled: true,
		configFunction: mockConfigFunction,
		company: {
			id: 100,
		},
	} as CompanyConfigEntity;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'COMPANY_CONFIG_REPOSITORY',
					useValue: {
						find: jest.fn(),
					},
				},
				{
					provide: 'FindCompanyConfigGateway',
					useFactory: (
						companyConfigRepository: Repository<CompanyConfigEntity>,
					) => new FindCompanyConfigGatewayImpl(companyConfigRepository),
					inject: ['COMPANY_CONFIG_REPOSITORY'],
				},
			],
		}).compile();

		findCompanyConfigGatewayImpl = module.get<FindCompanyConfigGatewayImpl>(
			'FindCompanyConfigGateway',
		);
		companyConfigRepository = module.get<Repository<CompanyConfigEntity>>(
			'COMPANY_CONFIG_REPOSITORY',
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('findCompanyConfigByCompanyId', () => {
		it('deve encontrar configurações da empresa pelo companyId', async () => {
			const companyId = 100;
			const mockEntities = [mockCompanyConfigEntity1, mockCompanyConfigEntity2];

			jest
				.spyOn(companyConfigRepository, 'find')
				.mockResolvedValue(mockEntities);

			const result =
				await findCompanyConfigGatewayImpl.findCompanyConfigByCompanyId(
					companyId,
				);

			expect(companyConfigRepository.find).toHaveBeenCalledWith({
				where: {
					company: {
						id: companyId,
					},
					enabled: true,
				},
			});

			// Verificar se o resultado foi transformado corretamente
			expect(result).toBeDefined();
			expect(result['category1']).toBeDefined();
			expect(result['category1']).toHaveLength(2);
			expect(result['category1'][0]).toEqual({
				id: 1,
				value: 'value1',
				enabled: true,
				name: 'function1',
			});
			expect(result['category1'][1]).toEqual({
				id: 2,
				value: 'value2',
				enabled: true,
				name: 'function1',
			});
		});

		it('deve retornar um objeto vazio quando não encontrar configurações', async () => {
			const companyId = 999;

			jest.spyOn(companyConfigRepository, 'find').mockResolvedValue([]);

			const result =
				await findCompanyConfigGatewayImpl.findCompanyConfigByCompanyId(
					companyId,
				);

			expect(companyConfigRepository.find).toHaveBeenCalledWith({
				where: {
					company: {
						id: companyId,
					},
					enabled: true,
				},
			});

			expect(result).toBeDefined();
			expect(Object.keys(result).length).toBe(0);
		});

		it('deve lidar com erros do banco de dados', async () => {
			const companyId = 100;
			const errorMessage = 'Database error';

			jest
				.spyOn(companyConfigRepository, 'find')
				.mockRejectedValue(new Error(errorMessage));

			await expect(
				findCompanyConfigGatewayImpl.findCompanyConfigByCompanyId(companyId),
			).rejects.toThrow(errorMessage);

			expect(companyConfigRepository.find).toHaveBeenCalledWith({
				where: {
					company: {
						id: companyId,
					},
					enabled: true,
				},
			});
		});
	});
});
