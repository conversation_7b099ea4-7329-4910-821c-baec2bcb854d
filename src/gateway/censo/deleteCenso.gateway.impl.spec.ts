import { DeleteResult, Repository, UpdateResult } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { DeleteCensoGateway } from 'src/core/application/gateway/censo/deleteCenso.gateway';
import { DeleteCensoGatewayImpl } from './deleteCenso.gateway.impl';

describe('DeleteCensoGatewayImpl', () => {
	let censoRepositoryMock: jest.Mocked<Repository<CensoEntity>>;
	let deleteCensoGateway: DeleteCensoGateway;

	beforeEach(() => {
		censoRepositoryMock = {
			update: jest.fn().mockResolvedValueOnce({ affected: 1 } as DeleteResult),
		} as unknown as jest.Mocked<Repository<CensoEntity>>;

		deleteCensoGateway = new DeleteCensoGatewayImpl(censoRepositoryMock);
	});

	it('deve deletar o censo de id passado ', async () => {
		const id = 1;
		await deleteCensoGateway.delete(id);

		expect(censoRepositoryMock.update).toHaveBeenCalledWith(
			{ id: String(id) },
			{ dataExclusao: expect.any(Date), hashArquivo: null },
		);
	});

	it('deve lançar uma exceção caso o censo não seja encontrado', async () => {
		censoRepositoryMock = {
			update: jest.fn().mockResolvedValueOnce({ affected: 0 } as UpdateResult),
		} as unknown as jest.Mocked<Repository<CensoEntity>>;

		deleteCensoGateway = new DeleteCensoGatewayImpl(censoRepositoryMock);
		await expect(deleteCensoGateway.delete(1)).rejects.toThrow();
	});
});
