import { Test, TestingModule } from '@nestjs/testing';
import { Brackets, In, IsNull, Repository, SelectQueryBuilder } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { FindCensoGatewayImpl } from './findCenso.gateway.Impl';
import { CensoMapper } from 'src/shared/mapper/censo/censo.mapper';
import { CensoNotFoundException } from 'src/shared/exceptions/rule/censoNotFound.exception';
import { HttpStatus } from '@nestjs/common';
import { OrdenacaoEnum } from 'src/core/application/dto/OrdenacaoEnum';

jest.mock('typeorm', () => ({
	...jest.requireActual('typeorm'),
	Repository: jest.fn().mockImplementation(() => ({
		find: jest.fn(),
		findOneBy: jest.fn(),
		createQueryBuilder: jest.fn(),
	})),
	Brackets: jest.fn(),
}));

const mockCensos = {
	id: '1',
	dataCriacao: new Date(),
	companyId: '3',
	operadora: { name: 'Operadora X' },
	totalLinhas: 10,
	userId: 'user123',
	status: {
		id: 'status1',
		descricao: 'Ativo',
		cor: 'verde',
		designStatus: 'ativo',
	},
	user: { name: 'Usuario 1' },
	hashArquivo: 'hash-value',
	diretorioSalvo: 'path/to/censo',
	nomeArquivo: 'censo',
};

describe('FindCensoGatewayImpl', () => {
	let findCensoGatewayImpl: FindCensoGatewayImpl;
	let censoRepository: Repository<CensoEntity>;
	const mockCompanyId = 3;
	const mockPagination: Paginacao = { page: 10, limit: 10, search: '' };
	let queryBuilderMock: SelectQueryBuilder<CensoEntity>;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				{
					provide: 'CENSO_REPOSITORY',
					useValue: {
						find: jest.fn(),
						findOneBy: jest.fn(),
						findOne: jest.fn(),
						createQueryBuilder: jest.fn(),
					},
				},
				{
					provide: 'FindCensoGateway',
					useFactory: (censoRepository: Repository<CensoEntity>) =>
						new FindCensoGatewayImpl(censoRepository),
					inject: ['CENSO_REPOSITORY'],
				},
			],
		}).compile();

		findCensoGatewayImpl = module.get<FindCensoGatewayImpl>('FindCensoGateway');
		censoRepository = module.get<Repository<CensoEntity>>('CENSO_REPOSITORY');

		queryBuilderMock = {
			leftJoinAndSelect: jest.fn().mockReturnThis(),
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			orWhere: jest.fn().mockReturnThis(),
			select: jest.fn().mockReturnThis(),
			getMany: jest.fn().mockResolvedValue([mockCensos]),
			getCount: jest.fn().mockResolvedValue(10),
			addOrderBy: jest.fn().mockReturnThis(),
			skip: jest.fn().mockReturnThis(),
			take: jest.fn().mockReturnThis(),
			orderBy: jest.fn().mockReturnThis(),
		} as unknown as SelectQueryBuilder<CensoEntity>;

		(censoRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);
	});

	it('deve encontrar censos por companyId', async () => {
		(censoRepository.find as jest.Mock).mockResolvedValueOnce([mockCensos]);

		const mockCompanyId = 3;

		jest.spyOn(CensoMapper, 'toCensoDomain').mockReturnValueOnce(mockCensos);

		const result = await findCensoGatewayImpl.findByCompanyId(mockCompanyId);

		expect(censoRepository.find).toHaveBeenCalledWith({
			where: { companyId: mockCompanyId.toString(), dataExclusao: null },
			relations: ['user', 'status'],
			select: expect.anything(),
		});
		expect(result).toHaveLength(1);
		expect(result[0].id).toBe(mockCensos.id);
		expect(result[0].operadora.name).toBe(mockCensos.operadora.name);
		expect(result[0].status.descricao).toBe(mockCensos.status.descricao);
	});

	it('deve encontrar censo para verificar estado por userID', async () => {
		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(mockCensos);

		const mockUserId = 3;

		jest.spyOn(CensoMapper, 'toCensoDomain').mockReturnValueOnce(mockCensos);

		const result = await findCensoGatewayImpl.findModalStateByUser(mockUserId);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: {
				userId: mockUserId.toString(),
				dataExclusao: IsNull(),
				status: { id: In(['aguardando', 'processando', 'verificar']) },
			},
			relations: ['user', 'status'],
			select: expect.anything(),
		});

		expect(result.id).toBe(mockCensos.id);
		expect(result.operadora.name).toBe(mockCensos.operadora.name);
		expect(result.status.descricao).toBe(mockCensos.status.descricao);
		expect(result).toStrictEqual(mockCensos);
	});

	it('deve encontrar censo para verificar estado por userID e retornar erro por não ter nada em andamento', async () => {
		const mockUserId = 3;

		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(null);

		await expect(
			findCensoGatewayImpl.findModalStateByUser(mockUserId),
		).rejects.toThrow(
			new CensoNotFoundException(
				HttpStatus.NOT_FOUND,
				'Nenhum Censo em andamento.',
			),
		);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: {
				userId: mockUserId.toString(),
				dataExclusao: IsNull(),
				status: { id: In(['aguardando', 'processando', 'verificar']) },
			},
			relations: ['user', 'status'],
			select: expect.anything(),
		});
	});

	it('deve encontrar censos por hash do arquivo', async () => {
		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(mockCensos);

		const mockHashValue = 'hash-value';

		jest.spyOn(CensoMapper, 'toCensoDomain').mockReturnValueOnce(mockCensos);

		const result = await findCensoGatewayImpl.findByFileHash(mockHashValue);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: {
				hashArquivo: mockHashValue,
				dataExclusao: IsNull(),
			},
			relations: ['user'],
		});

		expect(result).toBeDefined();
		expect(result.id).toBe(mockCensos.id);
		expect(result.operadora.name).toBe(mockCensos.operadora.name);
		expect(result.status.descricao).toBe(mockCensos.status.descricao);
	});

	it('deve retornar nulo por hash do arquivo nao encontrado', async () => {
		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(null);

		const mockHashValue = '';

		const result = await findCensoGatewayImpl.findByFileHash(mockHashValue);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: { hashArquivo: mockHashValue, dataExclusao: IsNull() },
			relations: ['user'],
		});

		expect(result).toBeNull();
	});

	it('deve encontrar a listagem de censos', async () => {
		const mockCompanyId = 3;
		const mockPagination: Paginacao = { page: 10, limit: 10, search: '' };

		const mockCount = 10;
		const queryBuilderMock = {
			leftJoinAndSelect: jest.fn().mockReturnThis(),
			where: jest.fn().mockReturnThis(),
			andWhere: jest.fn().mockReturnThis(),
			orderBy: jest.fn().mockReturnThis(),
			skip: jest.fn().mockReturnThis(),
			take: jest.fn().mockReturnThis(),
			select: jest.fn().mockReturnThis(),
			getMany: jest.fn().mockResolvedValue([mockCensos]),
			getCount: jest.fn().mockResolvedValue(mockCount),
			addOrderBy: jest.fn().mockResolvedValue(OrdenacaoEnum.ASC),
		};

		(censoRepository.createQueryBuilder as jest.Mock).mockReturnValue(
			queryBuilderMock,
		);

		jest.spyOn(CensoMapper, 'toCensoListagemDomain').mockReturnValueOnce({
			quantidadeTotal: 10,
			pagina: 10,
			censos: [{ ...mockCensos }],
		});

		const result: CensoListagem = await findCensoGatewayImpl.findListagem(
			mockCompanyId,
			mockPagination,
		);

		expect(result).toBeDefined();
		expect(result.pagina).toBe(mockPagination.page);
		expect(result.quantidadeTotal).toBe(mockCount);
		expect(result.censos).toHaveLength(1);
		expect(result.censos[0].id).toBe(mockCensos.id);
		expect(result.censos[0].user.name).toBe(mockCensos.user.name);
	});

	it('deve adicionar a ordenação por "adicionadoPor" se o parâmetro for fornecido', () => {
		const adicionadoPor = OrdenacaoEnum.ASC;

		jest
			.spyOn(findCensoGatewayImpl['censoRepository'], 'createQueryBuilder')
			.mockReturnValue(queryBuilderMock);

		findCensoGatewayImpl['buildListagemQuery'](
			mockCompanyId,
			mockPagination.page,
			mockPagination.limit,
			mockPagination.search,
			adicionadoPor,
		);

		expect(queryBuilderMock.addOrderBy).toHaveBeenCalledWith(
			'user.name',
			adicionadoPor,
		);
	});

	it('deve adicionar a ordenação por "adicionadoPor" se o parâmetro for fornecido', () => {
		const adicionadoPor = OrdenacaoEnum.ASC;

		jest
			.spyOn(findCensoGatewayImpl['censoRepository'], 'createQueryBuilder')
			.mockReturnValue(queryBuilderMock);

		findCensoGatewayImpl['buildListagemQuery'](
			mockCompanyId,
			mockPagination.page,
			mockPagination.limit,
			mockPagination.search,
			adicionadoPor,
		);

		expect(queryBuilderMock.addOrderBy).toHaveBeenCalledWith(
			'user.name',
			adicionadoPor,
		);
	});

	it('deve adicionar a ordenação por "dataEnvio" se o parâmetro for fornecido', () => {
		const dataEnvio = OrdenacaoEnum.DESC;

		jest
			.spyOn(findCensoGatewayImpl['censoRepository'], 'createQueryBuilder')
			.mockReturnValue(queryBuilderMock);

		findCensoGatewayImpl['buildListagemQuery'](
			mockCompanyId,
			mockPagination.page,
			mockPagination.limit,
			mockPagination.search,
			undefined, // adicionadoPor não fornecido
			dataEnvio, // dataEnvio fornecido
		);

		expect(queryBuilderMock.addOrderBy).toHaveBeenCalledWith(
			'censo.dataCriacao',
			dataEnvio,
		);
	});

	it('deve adicionar a ordenação por "status" se o parâmetro for fornecido', () => {
		const status = OrdenacaoEnum.ASC;

		jest
			.spyOn(findCensoGatewayImpl['censoRepository'], 'createQueryBuilder')
			.mockReturnValue(queryBuilderMock);

		findCensoGatewayImpl['buildListagemQuery'](
			mockCompanyId,
			mockPagination.page,
			mockPagination.limit,
			mockPagination.search,
			undefined, // adicionadoPor não fornecido
			undefined, // dataEnvio não fornecido
			status, // status fornecido
		);

		expect(queryBuilderMock.addOrderBy).toHaveBeenCalledWith(
			'status.id',
			status,
		);
	});

	it('deve adicionar a ordenação por "totalLinhas" se o parâmetro for fornecido', () => {
		const totalLinhas = OrdenacaoEnum.ASC;

		jest
			.spyOn(findCensoGatewayImpl['censoRepository'], 'createQueryBuilder')
			.mockReturnValue(queryBuilderMock);

		findCensoGatewayImpl['buildListagemQuery'](
			mockCompanyId,
			mockPagination.page,
			mockPagination.limit,
			mockPagination.search,
			undefined, // adicionadoPor não fornecido
			undefined, // dataEnvio não fornecido
			undefined, // status não fornecido
			totalLinhas, // totalLinhas fornecido
		);

		expect(queryBuilderMock.addOrderBy).toHaveBeenCalledWith(
			'censo.totalLinhas',
			totalLinhas,
		);
	});

	it('deve aplicar o filtro de busca corretamente no queryBuilder', async () => {
		const searchQuery = 'Operadora X';

		findCensoGatewayImpl['applySearchFilter'](queryBuilderMock, searchQuery);

		expect(queryBuilderMock.andWhere).toHaveBeenCalledWith(
			expect.any(Brackets),
			{ search: `%Operadora X%` },
		);
	});

	it('deve encontrar censo por id valido ', async () => {
		const mockId = 1;

		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(mockCensos);

		jest.spyOn(CensoMapper, 'toCensoDomain').mockReturnValueOnce(mockCensos);

		const result = await findCensoGatewayImpl.findById(mockId);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: {
				id: mockId.toString(),
				dataExclusao: IsNull(),
			},
			relations: ['user'],
		});

		expect(result).toBeDefined();
		expect(result.id).toBe(mockCensos.id);
		expect(result.operadora.name).toBe(mockCensos.operadora.name);
		expect(result.status.descricao).toBe(mockCensos.status.descricao);
	});

	it('deve retornar nulo por id nao encontrado', async () => {
		const mockId = 1;

		(censoRepository.findOne as jest.Mock).mockResolvedValueOnce(null);

		const result = await findCensoGatewayImpl.findById(mockId);

		expect(censoRepository.findOne).toHaveBeenCalledWith({
			where: {
				id: mockId.toString(),
				dataExclusao: IsNull(),
			},
			relations: ['user'],
		});

		expect(result).toBeNull();
	});
});
