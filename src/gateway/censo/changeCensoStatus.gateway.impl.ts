import { ChangeCensoStatusGateway } from 'src/core/application/gateway/censo/changeCensoStatus.gateway';
import { Repository } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { CensoNotFoundException } from 'src/shared/exceptions/rule/censoNotFound.exception';
import { HttpStatus } from '@nestjs/common';

export class ChangeCensoStatusGatewayImpl implements ChangeCensoStatusGateway {
	constructor(private readonly censoRepository: Repository<CensoEntity>) {}

	public async changeStatus(censoId: string, status: string): Promise<void> {
		const rowsAffected = await this.censoRepository.update(censoId, {
			status: { id: status },
		});
		if (rowsAffected.affected === 0) {
			throw new CensoNotFoundException(
				HttpStatus.BAD_REQUEST,
				'Censo não encontrado',
			);
		}
	}
}
