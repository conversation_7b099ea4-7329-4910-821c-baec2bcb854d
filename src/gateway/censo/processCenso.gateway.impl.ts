import { parseFile } from 'fast-csv';
import { ProducerMqttConfig } from 'src/configuration/amqp/abstract/producer.mqtt.config';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';
import { RetornoProcessaCenso } from 'src/core/application/dto/RetornoProcessaCenso';
import { ProcessCensoGateway } from 'src/core/application/gateway/censo/processCenso.gateway';
import { detectDelimiter } from 'src/helpers/detectDelimiter';
import * as crypto from 'crypto';
export class ProcessCensoGatewayImpl implements ProcessCensoGateway {
	constructor(private readonly producerMqttConfig: ProducerMqttConfig) {}

	async process(processaCenso: ProcessaCenso): Promise<RetornoProcessaCenso> {
		return await this.parseAndSend(processaCenso);
	}

	async parseAndSend(
		processaCenso: ProcessaCenso,
	): Promise<RetornoProcessaCenso> {
		processaCenso.idSocket = crypto
			.createHash('md5')
			.update(String(processaCenso.userId))
			.digest('hex');

		const retornoProcessaCenso = new RetornoProcessaCenso(
			processaCenso.idSocket,
		);
		let lineCount = 0;
		return new Promise(async (resolve, reject) => {
			parseFile(processaCenso.censoInternalPath, {
				delimiter: await detectDelimiter(processaCenso.censoInternalPath),
				headers: true,
				trim: true,
			})
				.on('data', (row: Record<string, string>) => {
					Object.keys(row).forEach((key) => {
						if (typeof row[key] === 'string') {
							row[key] = row[key].replace(/^(?:'')*'|'(?:'')*$/g, '');
						}
					});

					processaCenso.linhaCenso = row;
					processaCenso.linhaAtual = ++lineCount;
					this.producerMqttConfig.sendMessage(JSON.stringify(processaCenso));
				})
				.on('end', () => {
					resolve(retornoProcessaCenso);
				})
				.on('error', (error) => {
					reject(error);
				});
		});
	}
}
