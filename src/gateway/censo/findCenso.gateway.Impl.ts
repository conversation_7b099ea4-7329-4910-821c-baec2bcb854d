import { Brackets, In, <PERSON><PERSON><PERSON>, Repository, SelectQueryBuilder } from 'typeorm';
import { FindCensoGateway } from 'src/core/application/gateway/censo/findCenso.gateway';
import { Censo } from 'src/core/domain/Censo';
import { CensoEntity } from '../entities/censo.entity';
import { CensoMapper } from 'src/shared/mapper/censo/censo.mapper';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { CensoNotFoundException } from 'src/shared/exceptions/rule/censoNotFound.exception';
import { HttpStatus } from '@nestjs/common';
import { OrdenacaoCensoListagem } from 'src/core/application/dto/OrdenacaoCensoListagem';
import { OrdenacaoEnum } from 'src/core/application/dto/OrdenacaoEnum';

export class FindCensoGatewayImpl implements FindCensoGateway {
	constructor(private censoRepository: Repository<CensoEntity>) {}
	private showStatusList: Array<string> = ['concluido', 'verificar'];

	public async findByCompanyId(companyId: number): Promise<Censo[]> {
		const censos = await this.censoRepository.find({
			where: { companyId: companyId.toString(), dataExclusao: null },
			relations: ['user', 'status'],
			select: {
				id: true,
				dataCriacao: true,
				companyId: true,
				operadora: {
					name: true,
				},
				totalLinhas: true,
				userId: true,
				status: {
					id: true,
					cor: true,
					descricao: true,
					designStatus: true,
				},
				user: {
					name: true,
				},
			},
		});

		return censos.map((censo) => CensoMapper.toCensoDomain(censo));
	}

	public async findModalStateByUser(userId: number): Promise<Censo> {
		const censo = await this.censoRepository.findOne({
			where: {
				userId: userId.toString(),
				dataExclusao: IsNull(),
				status: { id: In(['aguardando', 'processando', 'verificar']) },
			},
			relations: ['user', 'status'],
			select: {
				id: true,
				dataCriacao: true,
				companyId: true,
				operadora: {
					name: true,
				},
				totalLinhas: true,
				userId: true,
				diretorioSalvo: true,
				nomeArquivo: true,
			},
		});

		if (!censo) {
			throw new CensoNotFoundException(
				HttpStatus.NOT_FOUND,
				'Nenhum Censo em andamento.',
			);
		}

		return CensoMapper.toCensoDomain(censo);
	}

	public async findListagem(
		companyId: number,
		query: Paginacao & OrdenacaoCensoListagem,
	): Promise<CensoListagem> {
		const skip = (query.page - 1) * query.limit;

		const censos = await this.buildListagemQuery(
			companyId,
			skip,
			query.limit,
			query.search,
			query.adicionadoPor,
			query.dataEnvio,
			query.status,
			query.totalLinhas,
		).getMany();

		const quantidadeTotal = await this.buildCountQuery(
			companyId,
			query.search,
		).getCount();

		return CensoMapper.toCensoListagemDomain({
			pagina: query.page,
			quantidadeTotal,
			censos: censos.map((censo) => CensoMapper.toCensoDomain(censo)),
		});
	}

	public async findByFileHash(hash: string): Promise<Censo> {
		const censo = await this.censoRepository.findOne({
			where: { hashArquivo: hash, dataExclusao: IsNull() },
			relations: ['user'],
		});
		if (!censo) {
			return null;
		}

		return CensoMapper.toCensoDomain(censo);
	}

	public async findById(id: number): Promise<Censo> {
		const censo = await this.censoRepository.findOne({
			where: {
				id: id.toString(),
				dataExclusao: IsNull(),
			},
			relations: ['user'],
		});

		if (!censo) {
			return null;
		}
		return CensoMapper.toCensoDomain(censo);
	}

	private buildListagemQuery(
		companyId: number,
		skip: number,
		limit: number,
		search?: string,
		adicionadoPor?: OrdenacaoEnum,
		dataEnvio?: OrdenacaoEnum,
		status?: OrdenacaoEnum,
		totalLinhas?: OrdenacaoEnum,
	): SelectQueryBuilder<CensoEntity> {
		const query = this.censoRepository
			.createQueryBuilder('censo')
			.leftJoinAndSelect('censo.operadora', 'operadora')
			.leftJoinAndSelect('censo.user', 'user')
			.leftJoinAndSelect('censo.status', 'status')
			.where('censo.companyId = :companyId', { companyId })
			.andWhere('censo.dataExclusao IS NULL')
			.andWhere('status.id IN (:...status)', {
				status: this.showStatusList,
			})
			.orderBy('censo.dataCriacao', 'DESC')
			.skip(skip)
			.take(limit);

		if (adicionadoPor) {
			query.addOrderBy('user.name', adicionadoPor);
		}
		if (dataEnvio) {
			query.addOrderBy('censo.dataCriacao', dataEnvio);
		}
		if (status) {
			query.addOrderBy('status.id', status);
		}
		if (totalLinhas) {
			query.addOrderBy('censo.totalLinhas', totalLinhas);
		}

		query.select([
			'censo.id',
			'censo.dataCriacao',
			'censo.totalLinhas',
			'censo.companyId',
			'operadora.name',
			'user.name',
			'user.id',
			'status.descricao',
			'status.cor',
			'status.designStatus',
			'status.id',
		]);

		this.applySearchFilter(query, search);

		return query;
	}

	private buildCountQuery(
		companyId: number,
		search?: string,
	): SelectQueryBuilder<CensoEntity> {
		const query = this.censoRepository
			.createQueryBuilder('censo')
			.leftJoinAndSelect('censo.operadora', 'operadora')
			.leftJoinAndSelect('censo.user', 'user')
			.leftJoinAndSelect('censo.status', 'status')
			.where('censo.companyId = :companyId', { companyId })
			.andWhere('censo.dataExclusao IS NULL')
			.andWhere('status.id IN (:...status)', {
				status: this.showStatusList,
			});

		this.applySearchFilter(query, search);

		return query;
	}

	/* istanbul ignore next */
	private applySearchFilter(
		query: SelectQueryBuilder<CensoEntity>,
		search?: string,
	): void {
		if (search) {
			query.andWhere(
				new Brackets((qb) => {
					qb.where('operadora.name LIKE :search')
						.orWhere('user.name LIKE :search')
						.orWhere('status.descricao LIKE :search');
				}),
				{ search: `%${search}%` },
			);
		}
	}
}
