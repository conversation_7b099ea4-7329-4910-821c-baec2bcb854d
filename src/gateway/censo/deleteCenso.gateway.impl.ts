import { DeleteCensoGateway } from 'src/core/application/gateway/censo/deleteCenso.gateway';
import { Repository } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { RetornoDeleteCenso } from 'src/core/application/dto/RetornoDeleteCenso';
import { CensoNotFoundException } from 'src/shared/exceptions/rule/censoNotFound.exception';
import { HttpStatus } from '@nestjs/common';

export class DeleteCensoGatewayImpl implements DeleteCensoGateway {
	constructor(private censoRepository: Repository<CensoEntity>) {}

	public async delete(censoId: number): Promise<RetornoDeleteCenso> {
		const censoDeletado = await this.censoRepository.update(
			{ id: String(censoId) },
			{ dataExclusao: new Date(), hashArquivo: null },
		);

		if (censoDeletado.affected === 0) {
			throw new CensoNotFoundException(
				HttpStatus.BAD_REQUEST,
				'Censo não encontrado',
			);
		}

		return new RetornoDeleteCenso('Censo deletado com sucesso!');
	}
}
