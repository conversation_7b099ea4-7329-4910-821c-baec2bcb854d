import { InsereCenso } from 'src/core/application/dto/InsereCenso';
import { CensoEntity } from '../entities/censo.entity';
import { Repository } from 'typeorm';
import { InsertCensoGatewayImpl } from './insertCenso.gateway.impl';
import { FindCensoGateway } from 'src/core/application/gateway/censo/findCenso.gateway';

describe('InsertCensoGatewayImpl', () => {
	let mockInsereCenso: InsereCenso;
	let mockCensoEntity: CensoEntity;
	let mockCensoRepository: jest.Mocked<Repository<CensoEntity>>;
	let insertCensoGatewayImpl: InsertCensoGatewayImpl;
	let mockFindCensoGateway: jest.Mocked<FindCensoGateway>;

	beforeEach(() => {
		mockInsereCenso = {
			userId: 1,
			diretorioSalvo: '/path/to/file',
			companyId: 1,
			dataCriacao: new Date(),
			hashArquivo: 'hash-value',
			nomeArquivo: 'nanica',
		};

		mockCensoEntity = {
			id: undefined,
			userId: mockInsereCenso.userId.toString(),
			diretorioSalvo: mockInsereCenso.diretorioSalvo,
			status: null,
			censoStatusId: 'aguardando',
			companyId: '1',
			dataCriacao: mockInsereCenso.dataCriacao,
			totalLinhas: 0,
			dataExclusao: null,
			operadora: null,
			user: null,
			hashArquivo: mockInsereCenso.hashArquivo,
			nomeArquivo: 'nome',
		};

		mockCensoRepository = {
			create: jest.fn().mockReturnValue(mockCensoEntity),
			insert: jest.fn().mockResolvedValue({
				identifiers: [{ id: 1 }],
			}),
		} as unknown as jest.Mocked<Repository<CensoEntity>>;

		mockFindCensoGateway = {
			findByFileHash: jest.fn(),
			findByCompanyId: jest.fn(),
			findListagem: jest.fn(),
			findById: jest.fn(),
			findModalStateByUser: jest.fn(),
		};

		insertCensoGatewayImpl = new InsertCensoGatewayImpl(
			mockCensoRepository,
			mockFindCensoGateway,
		);
	});

	it('deve inserir um censo no repositorio', async () => {
		await insertCensoGatewayImpl.insert(mockInsereCenso);

		expect(mockCensoRepository.create).toHaveBeenCalled();
		expect(mockCensoRepository.insert).toHaveBeenCalledWith(mockCensoEntity);
	});

	it('deve lançar uma exceção caso já exista um censo com o mesmo hash', async () => {
		mockFindCensoGateway.findByFileHash.mockResolvedValueOnce(mockCensoEntity);

		await expect(
			insertCensoGatewayImpl.insert(mockInsereCenso),
		).rejects.toThrow('Já existe um censo com esse hash');

		expect(mockFindCensoGateway.findByFileHash).toHaveBeenCalledWith(
			mockInsereCenso.hashArquivo,
		);
	});
});
