import { ProcessCensoGatewayImpl } from 'src/gateway/censo/processCenso.gateway.impl';
import { ProducerMqttConfig } from 'src/configuration/amqp/abstract/producer.mqtt.config';
import { parseFile } from 'fast-csv';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';

jest.mock('fast-csv', () => ({
	parseFile: jest.fn(),
}));

jest.mock('uuid', () => ({
	v4: jest.fn(),
}));

jest.mock('fs', () => ({
	createReadStream: jest.fn(() => ({
		on: jest.fn().mockImplementation(function (_event, handler) {
			handler();
			return this;
		}),
		close: jest.fn(),
	})),
}));

describe('ProcessCensoGatewayImpl', () => {
	let gateway: ProcessCensoGatewayImpl;
	let producerMqttConfigMock: ProducerMqttConfig;
	let processaCensoMock: ProcessaCenso;
	beforeEach(() => {
		producerMqttConfigMock = {
			sendMessage: jest.fn(),
		} as unknown as ProducerMqttConfig;
		processaCensoMock = new ProcessaCenso(1, 'path/to/file', 1, 1);
		gateway = new ProcessCensoGatewayImpl(producerMqttConfigMock);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should call sendMessage for each row in the CSV file', async () => {
		const mockRows = [
			{ column1: 'value1', column2: 'value2' },
			{ column1: 'value3', column2: 'value4' },
		];

		(parseFile as jest.Mock).mockImplementation(() => {
			const mockParseStream = {
				on: (event: string, callback: (data?: object) => void): object => {
					if (event === 'data') {
						mockRows.forEach((row) => callback(row));
					} else if (event === 'end') {
						callback();
					}
					return mockParseStream;
				},
			};
			return mockParseStream;
		});

		await gateway.process(processaCensoMock);

		expect(producerMqttConfigMock.sendMessage).toHaveBeenCalledTimes(
			mockRows.length,
		);
		let linhaCenso = 0;
		mockRows.forEach((row) => {
			processaCensoMock.linhaCenso = row;
			processaCensoMock.linhaAtual = ++linhaCenso;
			expect(producerMqttConfigMock.sendMessage).toHaveBeenCalledWith(
				JSON.stringify(processaCensoMock),
			);
		});
	});

	it('should reject with an error if parsing the CSV file fails', async () => {
		const mockError = new Error('Parsing failed');

		(parseFile as jest.Mock).mockImplementation(() => {
			const mockParseStream = {
				on: (event: string, callback: (data?: object) => void): object => {
					if (event === 'error') {
						callback(mockError);
					}
					return mockParseStream;
				},
			};
			return mockParseStream;
		});

		await expect(gateway.process(processaCensoMock)).rejects.toThrow(
			'Parsing failed',
		);
	});
});
