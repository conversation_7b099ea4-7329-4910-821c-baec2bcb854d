import { InsereCenso } from 'src/core/application/dto/InsereCenso';
import { InsertCensoGateway } from 'src/core/application/gateway/censo/insertCenso.gateway';
import { Repository } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { FindCensoGateway } from 'src/core/application/gateway/censo/findCenso.gateway';
import { DuplicatedCensoException } from 'src/shared/exceptions/rule/duplicatedCenso.exception';
import { HttpStatus } from '@nestjs/common';

export class InsertCensoGatewayImpl implements InsertCensoGateway {
	constructor(
		private censoRepository: Repository<CensoEntity>,
		private findCensoGateway: FindCensoGateway,
	) {}

	public async insert(insereCenso: InsereCenso): Promise<number> {
		const censoEntity = this.censoRepository.create({
			userId: insereCenso.userId.toString(),
			diretorioSalvo: insereCenso.diretorioSalvo,
			status: null,
			censoStatusId: 'aguardando',
			companyId: insereCenso.companyId.toString(),
			dataCriacao: insereCenso.dataCriacao,
			totalLinhas: insereCenso.totalLinhas,
			hashArquivo: insereCenso.hashArquivo,
			nomeArquivo: insereCenso.nomeArquivo,
		});

		await this.validateExistOne(censoEntity);

		return (await this.censoRepository.insert(censoEntity)).identifiers[0].id;
	}

	private async validateExistOne(censoEntity: CensoEntity): Promise<void> {
		const result = await this.findCensoGateway.findByFileHash(
			censoEntity.hashArquivo,
		);
		if (result) {
			throw new DuplicatedCensoException(
				HttpStatus.BAD_REQUEST,
				'Já existe um censo com esse hash',
			);
		}
	}
}
