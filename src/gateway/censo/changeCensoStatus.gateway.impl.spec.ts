import { Repository, UpdateResult } from 'typeorm';
import { CensoEntity } from '../entities/censo.entity';
import { ChangeCensoStatusGateway } from 'src/core/application/gateway/censo/changeCensoStatus.gateway';
import { ChangeCensoStatusGatewayImpl } from './changeCensoStatus.gateway.impl';

describe('ChangeCensoStatusGateway', () => {
	let censoRepositoryMock: jest.Mocked<Repository<CensoEntity>>;
	let changeCensoStatusGateway: ChangeCensoStatusGateway;

	beforeEach(() => {
		censoRepositoryMock = {
			update: jest.fn().mockResolvedValueOnce({ affected: 1 } as UpdateResult),
		} as unknown as jest.Mocked<Repository<CensoEntity>>;

		changeCensoStatusGateway = new ChangeCensoStatusGatewayImpl(
			censoRepositoryMock,
		);
	});

	it('deve mudar o status do censo de id passado ', async () => {
		const id = 'id';
		const status = 'verificar';
		await changeCensoStatusGateway.changeStatus('id', 'verificar');
		expect(censoRepositoryMock.update).toHaveBeenCalledWith(id, {
			status: { id: status },
		});
		expect(censoRepositoryMock.update).toHaveBeenCalledTimes(1);
	});

	it('deve lançar uma exceção caso o censo não seja encontrado', async () => {
		censoRepositoryMock = {
			update: jest.fn().mockResolvedValueOnce({ affected: 0 } as UpdateResult),
		} as unknown as jest.Mocked<Repository<CensoEntity>>;

		changeCensoStatusGateway = new ChangeCensoStatusGatewayImpl(
			censoRepositoryMock,
		);
		await expect(
			changeCensoStatusGateway.changeStatus('id', 'verificar'),
		).rejects.toThrow();
	});
});
