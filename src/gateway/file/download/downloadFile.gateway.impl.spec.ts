import { StorageApiWrapper } from 'src/configuration/apis/abstract/storageApiWrapper';
import { DownloadFileGatewayImpl } from './downloadFile.gateway.impl';
import { writeToString } from 'fast-csv';

jest.mock('fast-csv', () => ({
	writeToString: jest.fn().mockResolvedValue('mocked,csv,content'),
}));

describe('DownloadFileGatewayImpl', () => {
	let storageApiWrapper: StorageApiWrapper;
	let gateway: DownloadFileGatewayImpl;

	beforeEach(() => {
		storageApiWrapper = {
			getFile: jest.fn(),
		} as unknown as StorageApiWrapper;

		gateway = new DownloadFileGatewayImpl(storageApiWrapper);
	});

	it('deve baixar arquivo existente', async () => {
		const file = 'file';
		storageApiWrapper.getFile = jest.fn().mockResolvedValue('file');

		const result = await gateway.downloadFile(file);

		expect(result).toEqual(expect.any(String));
		expect(storageApiWrapper.getFile).toHaveBeenCalledWith(file);
	});

	it('deve soltar erro ao tentar baixar arquivo inexistente', async () => {
		const file = 'arquivoInexistente';

		storageApiWrapper.getFile = jest
			.fn()
			.mockRejectedValue(new Error('Arquivo não encontrado'));

		await expect(gateway.downloadFile(file)).rejects.toThrow(
			'Arquivo não encontrado',
		);

		expect(storageApiWrapper.getFile).toHaveBeenCalledWith(file);
	});

	it('deve chamar writeToString com os parâmetros corretos', async () => {
		const template = ['header1', 'header2', 'header3'];
		const expectedResult = 'mocked,csv,content';

		const result = await gateway.downloadCsvToTemplate(template);

		expect(writeToString).toHaveBeenCalledWith([[]], {
			headers: template,
			writeHeaders: true,
		});

		expect(result).toBe(expectedResult);
	});

	it('deve funcionar com um array de template vazio', async () => {
		const template: string[] = [];
		await gateway.downloadCsvToTemplate(template);

		expect(writeToString).toHaveBeenCalledWith([[]], {
			headers: [],
			writeHeaders: true,
		});
	});

	it('deve retornar o conteúdo do CSV como string', async () => {
		const template = ['col1', 'col2'];
		const result = await gateway.downloadCsvToTemplate(template);

		expect(typeof result).toBe('string');
		expect(result).toContain(',');
	});
});
