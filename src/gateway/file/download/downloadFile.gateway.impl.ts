import { writeToString } from 'fast-csv';
import { StorageApiWrapper } from 'src/configuration/apis/abstract/storageApiWrapper';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';

export class DownloadFileGatewayImpl implements DownloadFileGateway {
	constructor(private readonly storageApiWrapper: StorageApiWrapper) {}

	async downloadFile(file: string): Promise<string> {
		const fileLocation = await this.storageApiWrapper.getFile(file);
		return Promise.resolve(fileLocation);
	}

	async downloadCsvToTemplate(template: string[]): Promise<string> {
		return await writeToString([[]], {
			headers: template,
			writeHeaders: true,
		});
	}
}
