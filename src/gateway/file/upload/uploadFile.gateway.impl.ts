import { StorageApiWrapper } from 'src/configuration/apis/abstract/storageApiWrapper';
import { UploadFileGateway } from 'src/core/application/gateway/file/upload/uploadFile.gateway';
import { File } from 'src/core/domain/File';
export class UploadFileGatewayImpl implements UploadFileGateway {
	constructor(private uploadApiWrapper: StorageApiWrapper) {}

	async upload(file: File): Promise<string> {
		return await this.uploadApiWrapper.insertFile(file);
	}
}
