import { File } from 'src/core/domain/File';
import { UploadFileGatewayImpl } from './uploadFile.gateway.impl';
import { StorageApiWrapper } from 'src/configuration/apis/abstract/storageApiWrapper';

describe('UploadFileGatewayImpl', () => {
	let mockFile: File;
	let mockGateway: UploadFileGatewayImpl;
	let mockApiWrapper: jest.Mocked<StorageApiWrapper>;

	beforeEach(async () => {
		mockFile = new File(
			'test-file.csv',
			'text/csv',
			1024,
			'./uploads/test-file.csv',
			10,
			null,
			'hash_value',
		);

		mockApiWrapper = {
			insertFile: jest.fn(),
		} as unknown as jest.Mocked<StorageApiWrapper>;

		mockGateway = new UploadFileGatewayImpl(mockApiWrapper);
	});

	it('should upload file successfully', async () => {
		const mockReturnValue = 'mocked-file-url';
		jest.spyOn(mockGateway, 'upload').mockResolvedValue(mockReturnValue);
		jest.spyOn(mockApiWrapper, 'insertFile').mockResolvedValue(mockReturnValue);
		const result = await mockGateway.upload(mockFile);

		expect(result).toBe(mockReturnValue);
	});

	it('should handle error when file upload fails', async () => {
		const errorMessage = 'File upload failed';
		jest
			.spyOn(mockApiWrapper, 'insertFile')
			.mockRejectedValue(new Error(errorMessage));

		await expect(mockGateway.upload(mockFile)).rejects.toThrow(errorMessage);
		expect(mockApiWrapper.insertFile).toHaveBeenCalledWith(mockFile);
	});
});
