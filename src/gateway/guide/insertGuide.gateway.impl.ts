import { InsertGuideGateway } from 'src/core/application/gateway/guide/insertGuide.gateway';
import { Guide } from 'src/core/domain/Guide';
import { Repository } from 'typeorm';
import { GuideEntity } from '../entities/guide.entity';
import { GuideMapper } from 'src/shared/mapper/guide/GuideMapper';

export class InsertGuideGatewayImpl implements InsertGuideGateway {
	constructor(private guideRepository: Repository<GuideEntity>) {}

	public async insertGuide(guide: Guide): Promise<Guide> {
		const guideEntity = this.createGuideEntity(guide);
		const result = await this.guideRepository.save(guideEntity);
		return GuideMapper.toDomain(result);
	}

	private createGuideEntity(guide: Guide): GuideEntity {
		return this.guideRepository.create({
			id: guide.id,
			patient: guide.patient,
			patientWay: guide.patientWay,
			created: guide.created,
			updated: guide.updated,
			numeroSenha: guide.numeroSenha,
			numeroGuia: guide.numeroGuia,
			dataEmissaoGuia: guide.dataEmissaoGuia,
			dataAutorizacaoGuia: guide.dataAutorizacaoGuia,
			speciality: guide.speciality,
			hospital: guide.hospital,
			type: guide.type,
			obs: guide.obs,
			obsDaily: guide.obsDaily,
			obsProcedure: guide.obsProcedure,
			obsDrugs: guide.obsDrugs,
			obsMaterials: guide.obsMaterials,
			inicioPeriodo: guide.inicioPeriodo,
			finalPeriodo: guide.finalPeriodo,
			valorTotal: guide.valorTotal,
			enabled: guide.enabled,
			userId: guide.userId,
			auditorId: guide.auditorId,
			tissLink: guide.tissLink,
			isViewed: guide.isViewed,
			valueRequest: guide.valueRequest,
			valueSaved: guide.valueSaved,
			valueAuthorized: guide.valueAuthorized,
			isCenso: guide.isCenso,
			reanalysis: guide.reanalysis,
			codIntegration: guide.codIntegration,
			obsDailyProvider: guide.obsDailyProvider,
			obsProcedureProvider: guide.obsProcedureProvider,
			obsDrugsProvider: guide.obsDrugsProvider,
			obsMaterialsProvider: guide.obsMaterialsProvider,
			motivoNegativa: guide.motivoNegativa,
			dataExpiracaoEdicao: guide.dataExpiracaoEdicao,
		});
	}
}
