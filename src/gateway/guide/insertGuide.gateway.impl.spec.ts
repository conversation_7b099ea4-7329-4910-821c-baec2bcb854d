import { Repository } from 'typeorm';
import { GuideEntity } from '../entities/guide.entity';
import { Guide } from 'src/core/domain/Guide';
import { InsertGuideGatewayImpl } from './insertGuide.gateway.impl';
import { PatientEntity } from '../entities/patient.entity';
import { HospitalEntity } from '../entities/hospital.entity';

describe('InsertGuideGatewayImpl', () => {
	let guideRepositoryMock: jest.Mocked<Repository<GuideEntity>>;
	let insertGuideGatewayImpl: InsertGuideGatewayImpl;

	beforeEach(() => {
		guideRepositoryMock = {
			create: jest.fn(),
			save: jest.fn(),
		} as unknown as jest.Mocked<Repository<GuideEntity>>;

		insertGuideGatewayImpl = new InsertGuideGatewayImpl(guideRepositoryMock);
	});

	it('deve criar uma GuideEntity a partir de um Guide, salvar e retornar o Guide convertido', async () => {
		const dummyGuide = new Guide(
			null,
			{ id: 1, isCenso: 1 },
			{ id: 2 },
			{ id: 3 },
		);

		const dummyGuideEntity: GuideEntity = {
			id: 123,
			patient: dummyGuide.patient as PatientEntity,
			hospital: dummyGuide.hospital as HospitalEntity,
			created: dummyGuide.created,
			updated: dummyGuide.updated,
			numeroSenha: dummyGuide.numeroSenha,
			numeroGuia: dummyGuide.numeroGuia,
			dataEmissaoGuia: dummyGuide.dataEmissaoGuia,
			dataAutorizacaoGuia: dummyGuide.dataAutorizacaoGuia,
			speciality: dummyGuide.speciality,
			type: dummyGuide.type,
			obs: dummyGuide.obs,
			obsDaily: dummyGuide.obsDaily,
			obsProcedure: dummyGuide.obsProcedure,
			obsDrugs: dummyGuide.obsDrugs,
			obsMaterials: dummyGuide.obsMaterials,
			inicioPeriodo: dummyGuide.inicioPeriodo,
			finalPeriodo: dummyGuide.finalPeriodo,
			valorTotal: dummyGuide.valorTotal,
			enabled: dummyGuide.enabled,
			userId: dummyGuide.userId,
			auditorId: dummyGuide.auditorId,
			tissLink: dummyGuide.tissLink,
			isViewed: dummyGuide.isViewed,
			valueRequest: dummyGuide.valueRequest,
			valueSaved: dummyGuide.valueSaved,
			valueAuthorized: dummyGuide.valueAuthorized,
			isCenso: dummyGuide.isCenso,
			reanalysis: dummyGuide.reanalysis,
			codIntegration: dummyGuide.codIntegration,
			obsDailyProvider: dummyGuide.obsDailyProvider,
			obsProcedureProvider: dummyGuide.obsProcedureProvider,
			obsDrugsProvider: dummyGuide.obsDrugsProvider,
			obsMaterialsProvider: dummyGuide.obsMaterialsProvider,
			motivoNegativa: dummyGuide.motivoNegativa,
			dataExpiracaoEdicao: dummyGuide.dataExpiracaoEdicao,
		};

		guideRepositoryMock.create.mockReturnValue(dummyGuideEntity);
		guideRepositoryMock.save.mockResolvedValue(dummyGuideEntity);

		await insertGuideGatewayImpl.insertGuide(dummyGuide);

		expect(guideRepositoryMock.create).toHaveBeenCalledWith({
			id: dummyGuide.id,
			patient: dummyGuide.patient,
			patientWay: dummyGuide.patientWay,
			created: dummyGuide.created,
			updated: dummyGuide.updated,
			numeroSenha: dummyGuide.numeroSenha,
			numeroGuia: dummyGuide.numeroGuia,
			dataEmissaoGuia: dummyGuide.dataEmissaoGuia,
			dataAutorizacaoGuia: dummyGuide.dataAutorizacaoGuia,
			speciality: dummyGuide.speciality,
			hospital: dummyGuide.hospital,
			type: dummyGuide.type,
			obs: dummyGuide.obs,
			obsDaily: dummyGuide.obsDaily,
			obsProcedure: dummyGuide.obsProcedure,
			obsDrugs: dummyGuide.obsDrugs,
			obsMaterials: dummyGuide.obsMaterials,
			inicioPeriodo: dummyGuide.inicioPeriodo,
			finalPeriodo: dummyGuide.finalPeriodo,
			valorTotal: dummyGuide.valorTotal,
			enabled: dummyGuide.enabled,
			userId: dummyGuide.userId,
			auditorId: dummyGuide.auditorId,
			tissLink: dummyGuide.tissLink,
			isViewed: dummyGuide.isViewed,
			valueRequest: dummyGuide.valueRequest,
			valueSaved: dummyGuide.valueSaved,
			valueAuthorized: dummyGuide.valueAuthorized,
			isCenso: dummyGuide.isCenso,
			reanalysis: dummyGuide.reanalysis,
			codIntegration: dummyGuide.codIntegration,
			obsDailyProvider: dummyGuide.obsDailyProvider,
			obsProcedureProvider: dummyGuide.obsProcedureProvider,
			obsDrugsProvider: dummyGuide.obsDrugsProvider,
			obsMaterialsProvider: dummyGuide.obsMaterialsProvider,
			motivoNegativa: dummyGuide.motivoNegativa,
			dataExpiracaoEdicao: dummyGuide.dataExpiracaoEdicao,
		});
		expect(guideRepositoryMock.save).toHaveBeenCalledWith(dummyGuideEntity);
	});
});
