import { JwtService } from '@nestjs/jwt';
import { AuthMapper } from 'src/shared/mapper/auth/auth.mapper';
import { Auth } from 'src/core/domain/Auth';
import { LoginGatewayImpl } from './login.gateway.Impl';
import { User } from 'src/core/domain/User';

jest.mock('src/shared/mapper/auth/auth.mapper');

describe('LoginGatewayImpl', () => {
	let loginGateway: LoginGatewayImpl;
	let jwtService: JwtService;

	beforeEach(() => {
		jwtService = new JwtService(null);
		jwtService.sign = jest.fn();

		loginGateway = new LoginGatewayImpl(jwtService);
	});

	it('deve chamar jwtService.sign com o payload correto e retornar o resultado do AuthMapper', async () => {
		const mockToken = 'mock_jwt_token';
		const mockRequest: User = {
			id: 1,
			email: '<EMAIL>',
			password: 'password123',
			name: 'user',
			companyId: '["3"]',
		};

		(jwtService.sign as jest.Mock).mockReturnValue(mockToken);

		const mockAuth: Auth = { token: mockToken };
		(AuthMapper.toAuthDomain as jest.Mock).mockReturnValue(mockAuth);

		const result = await loginGateway.login(mockRequest);

		expect(jwtService.sign).toHaveBeenCalledWith({
			sub: mockRequest.id,
			email: mockRequest.email,
			nome: mockRequest.name,
			companyId: '3',
		});

		expect(AuthMapper.toAuthDomain).toHaveBeenCalledWith(mockToken);

		expect(result).toEqual(mockAuth);
	});
});
