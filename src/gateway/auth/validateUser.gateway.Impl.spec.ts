import { Test, TestingModule } from '@nestjs/testing';
import { FindUserGateway } from 'src/core/application/gateway/user/findUser.gateway';
import { User } from 'src/core/domain/User';
import * as crypto from 'crypto';
import { ValidateUserGatewayImpl } from './validateUser.gateway.Impl';

describe('ValidateUserGatewayImpl', () => {
	let validateUserGateway: ValidateUserGatewayImpl;
	let findUserGateway: FindUserGateway;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				ValidateUserGatewayImpl,
				{
					provide: 'FindUserGateway',
					useValue: {
						findOneOrFail: jest.fn(),
					},
				},
			],
		}).compile();

		validateUserGateway = module.get<ValidateUserGatewayImpl>(
			ValidateUserGatewayImpl,
		);
		findUserGateway = module.get<FindUserGateway>('FindUserGateway');
	});

	it('should return the user if the email and password are correct', async () => {
		const mockUser: User = {
			id: 1,
			email: '<EMAIL>',
			password: crypto
				.createHash('md5')
				.update('correct_password')
				.digest('hex'),
			name: 'John Doe',
			companyId: '3',
		};

		(findUserGateway.findOneOrFail as jest.Mock).mockResolvedValue(mockUser);

		const result = await validateUserGateway.validateUser(
			'<EMAIL>',
			'correct_password',
		);

		expect(findUserGateway.findOneOrFail).toHaveBeenCalledWith({
			where: { email: '<EMAIL>', enabled: 1 },
		});
		expect(result).toEqual(mockUser);
	});

	it('should return null if the user is not found', async () => {
		(findUserGateway.findOneOrFail as jest.Mock).mockRejectedValue(
			new Error('User not found'),
		);

		const result = await validateUserGateway.validateUser(
			'<EMAIL>',
			'any_password',
		);

		expect(findUserGateway.findOneOrFail).toHaveBeenCalledWith({
			where: { email: '<EMAIL>', enabled: 1 },
		});
		expect(result).toBeNull();
	});

	it('should return null if the password is incorrect', async () => {
		const mockUser: User = {
			id: 1,
			email: '<EMAIL>',
			password: crypto
				.createHash('md5')
				.update('correct_password')
				.digest('hex'),
			name: 'John Doe',
			companyId: '3',
		};

		(findUserGateway.findOneOrFail as jest.Mock).mockResolvedValue(mockUser);

		const result = await validateUserGateway.validateUser(
			'<EMAIL>',
			'wrong_password',
		);

		expect(findUserGateway.findOneOrFail).toHaveBeenCalledWith({
			where: { email: '<EMAIL>', enabled: 1 },
		});
		expect(result).toBeNull();
	});
});
