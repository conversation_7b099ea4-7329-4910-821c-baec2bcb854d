import { User } from 'src/core/domain/User';
import { FindUserGateway } from 'src/core/application/gateway/user/findUser.gateway';
import { Inject, Injectable } from '@nestjs/common';
import * as crypto from 'crypto';
import { ValidateUserGateway } from 'src/core/application/gateway/auth/validateUser.gateway';

@Injectable()
export class ValidateUserGatewayImpl implements ValidateUserGateway {
	constructor(
		@Inject('FindUserGateway')
		private readonly findUserGateway: FindUserGateway,
	) {}
	async validateUser(email: string, password: string): Promise<User> {
		let user: User;

		try {
			user = await this.findUserGateway.findOneOrFail({
				where: { email, enabled: 1 },
			});
		} catch {
			return null;
		}
		const hashedInput = crypto.createHash('md5').update(password).digest('hex');

		if (hashedInput != user.password) return null;

		return user;
	}
}
