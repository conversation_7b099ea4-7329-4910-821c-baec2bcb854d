import { LoginGateway } from 'src/core/application/gateway/auth/login.gateway';
import { JwtService } from '@nestjs/jwt';
import { Auth } from 'src/core/domain/Auth';
import { AuthMapper } from 'src/shared/mapper/auth/auth.mapper';
import { User } from 'src/core/domain/User';

export class LoginGatewayImpl implements LoginGateway {
	constructor(private readonly jwtService: JwtService) {}

	async login(user: User): Promise<Auth> {
		if (JSON.parse(user.companyId)[0]) {
			user.companyId = JSON.parse(user.companyId)[0];
		}

		const payload = {
			sub: user.id,
			email: user.email,
			companyId: user.companyId,
			nome: user.name,
			role: user.role,
		};

		return AuthMapper.toAuthDomain(this.jwtService.sign(payload));
	}
}
