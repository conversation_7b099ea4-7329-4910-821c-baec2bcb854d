import { Channel } from 'amqplib';
import { ConsumerFactory } from 'src/entrypoint/consumer/consumer.factory';
import { Queue, QUEUES } from 'src/shared/queues';

export class AmqpConsumerConfig {
	constructor(
		private channel: Channel,
		private consumerFactory: ConsumerFactory,
	) {}

	public async createConsumers(): Promise<void> {
		await this.createQueues();
	}

	private async createQueues(): Promise<void> {
		const queues = Array.from(QUEUES.values());
		const censoConsumerRate = parseInt(process.env.CENSO_CONSUMER_RATE);
		this.channel.prefetch(censoConsumerRate);
		await Promise.all(
			queues.map(async (queue) => {
				this.executeCreation(queue);
			}),
		);
	}

	private async executeCreation(queue: Queue): Promise<void> {
		await this.channel.assertQueue(queue.name, {
			durable: true,
		});
		const consumer = this.consumerFactory.execute(queue.name, this.channel);

		const consumerWorkers = process.env.CENSO_CONSUMER_WORKERS;
		for (let i = 0; i < parseInt(consumerWorkers); i++) {
			await this.channel.consume(
				queue.name,
				consumer.handleMessage.bind(consumer),
			);
		}
	}
}
