import { QUEUES } from 'src/shared/queues';
import { ProducerMqttConfig } from './abstract/producer.mqtt.config';
import { AmqpProducerConfig } from './amqp.producer.config';

export class ProducerMqttConfigImpl implements ProducerMqttConfig {
	constructor(
		private readonly client: AmqpProducerConfig,
		private readonly pattern: string,
	) {}

	public async sendMessage(message: string): Promise<void> {
		this.client.sendToQueue(message, QUEUES.get(this.pattern));
	}
}
