import { Injectable } from '@nestjs/common';
import {
	ClientProxy,
	ClientProxyFactory,
	Transport,
} from '@nestjs/microservices';

@Injectable()
export class RabbitMQClient {
	private client: ClientProxy;

	constructor(private queue: string) {
		this.client = ClientProxyFactory.create({
			transport: Transport.RMQ,
			options: {
				urls: [
					`amqp://${process.env.CAREFY_RABBIT_MQ_PRODUCER_USER}:${process.env.CAREFY_RABBIT_MQ_PRODUCER_PASS}@${process.env.CAREFY_RABBIT_MQ_HOST}:${process.env.CAREFY_RABBIT_MQ_PORT}`,
				],
				queueOptions: {
					durable: true,
				},
				queue: this.queue,
			},
		});
	}

	getClient(): ClientProxy {
		return this.client;
	}
}
