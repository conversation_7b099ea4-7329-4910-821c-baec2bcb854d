import { EventBus } from './abstract/event-bus';

export class EventBusImpl implements EventBus {
	private listeners: Map<string, ((...args: object[]) => void)[]>;

	constructor() {
		this.listeners = new Map();
	}

	public on<T>(event: string, callback: (data: T) => void): void {
		const callbacks = this.listeners.get(event) || [];
		callbacks.push(callback as (...args: object[]) => void);
		this.listeners.set(event, callbacks);
	}

	public emit<T>(event: string, ...args: T[]): void {
		const callbacks = this.listeners.get(event) || [];
		callbacks.forEach((callback) => callback(...(args as object[])));
	}
}
