import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/configuration/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { empresaProviders } from '../../providers/empresa/empresa.providers';
import { EmpresaController } from '../../../entrypoint/controller/empresa/empresa.controller';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	controllers: [EmpresaController],
	providers: [...empresaProviders],
})
export class EmpresaModule {}
