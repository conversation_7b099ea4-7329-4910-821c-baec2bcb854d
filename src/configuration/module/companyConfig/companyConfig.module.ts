import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { companyConfigProvider } from 'src/configuration/providers/companyConfig/companyConfig.providers';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	providers: [...companyConfigProvider],
	exports: [...companyConfigProvider],
})
export class CompanyConfigModule {}
