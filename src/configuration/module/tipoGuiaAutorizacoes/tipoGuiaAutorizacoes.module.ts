import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/configuration/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { TipoGuiaAutorizacoesController } from 'src/entrypoint/controller/tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.controller';
import { tipoGuiaAutorizacoesProviders } from 'src/configuration/providers/tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.providers';
import { CriptografiaModule } from '../criptografia/criptografia.module';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule, CriptografiaModule],
	controllers: [TipoGuiaAutorizacoesController],
	providers: [...tipoGuiaAutorizacoesProviders],
})
export class TipoGuiaAutorizacoesModule {}
