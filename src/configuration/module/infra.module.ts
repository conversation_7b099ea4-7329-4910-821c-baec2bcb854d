import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { infraProviders } from '../providers/infra.providers';
import { DatabaseModule } from '../database/database.module';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	providers: [...infraProviders],
	exports: [
		'EventBus',
		'CENSO_REPOSITORY',
		'HOSPITALIZATION_REPOSITORY',
		'PATIENT_REPOSITORY',
		'HOSPITAL_REPOSITORY',
		'HOSPITAL_COMPANY_REPOSITORY',
		'GUIDE_REPOSITORY',
		'FORM_CIDS_REPOSITORY',
		'BED_HOSPITAL_RESPOSITORY',
		'GUIDE_DAILY_REPOSITORY',
		'GUIDE_STATUS_REPOSITORY',
		'CNES_MUNICIPIO_OLD_REPOSITORY',
		'CITY_REPOSITORY',
		'AUTORIZACOES_REPOSITORY',
	],
})
export class InfraModule {}
