import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { hospitalizationProvider } from 'src/configuration/providers/hospitalization/hospitalization.providers';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	providers: [...hospitalizationProvider],
	exports: [...hospitalizationProvider],
})
export class HospitalizationModule {}
