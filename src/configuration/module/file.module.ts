import { Module } from '@nestjs/common';
import { UploadController } from 'src/entrypoint/controller/upload/upload.controller';
import { uploadProviders } from '../providers/upload.providers';
import { DatabaseModule } from '../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { CensoModule } from './censo/censo.module';
import { fileProviders } from '../providers/file.providers';
import { CensoValidationModule } from './censo/censoValidation.module';
import { DownloadController } from 'src/entrypoint/controller/download/download.controller';
import { downloadProviders } from '../providers/download.providers';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		DatabaseModule,
		CensoModule,
		CensoValidationModule,
	],
	controllers: [UploadController, DownloadController],
	providers: [...uploadProviders, ...fileProviders, ...downloadProviders],
})
export class FileModule {}
