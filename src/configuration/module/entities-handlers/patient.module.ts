import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { patientProvider } from 'src/configuration/providers/entities-handlers/patient.provider';
import { InfraModule } from '../infra.module';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule, InfraModule],
	providers: [...patientProvider],
	exports: [
		FindPatientService.name,
		'findPatientGateway',
		'InsertPatientGateway',
		'InsertHospitalizationGateway',
	],
})
export class PatientModule {}
