import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { PlanosController } from '../../../entrypoint/controller/planos/planos.controller';
import { planosProviders } from '../../providers/planos/planos.providers';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	controllers: [PlanosController],
	providers: [...planosProviders],
})
export class PlanosModule {}
