import { Module } from '@nestjs/common';
import { AppController } from '../../entrypoint/controller/app.controller';
import { PrometheusModule } from '@willsoto/nestjs-prometheus';
import { UsersModule } from './users.module';
import { ConfigModule } from '@nestjs/config';
import { AuthModule } from './auth.module';
import { FileModule } from './file.module';
import { CensoModule } from './censo/censo.module';
import { CensoDadosModule } from './censo/censoDados.module';
import { AutorizacoesModule } from './autorizacoes/autorizacoes.module';
import { TagsModule } from './tags/tags.module';
import { TipoGuiaAutorizacoesModule } from './tipoGuiaAutorizacoes/tipoGuiaAutorizacoes.module';
import { PlanosModule } from './planos/planos.module';
import { EmpresaModule } from './empresa/empresa.module';
import { CompanyConfigModule } from './companyConfig/companyConfig.module';
@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		PrometheusModule.register(),
		UsersModule,
		AuthModule,
		FileModule,
		CensoModule,
		CensoDadosModule,
		AutorizacoesModule,
		TagsModule,
		TipoGuiaAutorizacoesModule,
		PlanosModule,
		EmpresaModule,
		CompanyConfigModule,
	],
	controllers: [AppController],
	providers: [],
	exports: [],
})
export class AppModule {}
