import { Modu<PERSON> } from '@nestjs/common';
import { CensoController } from 'src/entrypoint/controller/censo/censo.controller';
import { DatabaseModule } from '../../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { censoProviders } from '../../providers/censo/censo.providers';
import { InsertCensoService } from 'src/core/application/services/censo/insertCenso.service';
import { fileProviders } from '../../providers/file.providers';
import { CensoDadosModule } from './censoDados.module';
import { InfraModule } from '../infra.module';
import { CensoValidationModule } from './censoValidation.module';
import { AmqpModule } from '../amqp.module';
import { PatientModule } from '../entities-handlers/patient.module';
import { CensoStatusModule } from './censoStatus.module';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		DatabaseModule,
		CensoDadosModule,
		InfraModule,
		CensoValidationModule,
		AmqpModule,
		PatientModule,
		CensoStatusModule,
	],
	controllers: [CensoController],
	providers: [...censoProviders, ...fileProviders],
	exports: ['FindCensoGateway', InsertCensoService.InsertCensoService],
})
export class CensoModule {}
