import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { censoDadosProviders } from '../../providers/censo/censoDados.providers';
import { CensoDadosController } from 'src/entrypoint/controller/censoDados/censoDados.controller';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { CensoValidationModule } from './censoValidation.module';
import { PatientModule } from '../entities-handlers/patient.module';
import { HospitalModule } from '../hospital/hospital.module';
import { BedHospitalModule } from '../bedHospital/bedHospital.module';
import { FormCidsModule } from '../formCids/formCids.module';
import { GuideModule } from '../guide/guide.module';
import { GuideDailyModule } from '../guideDaily/guideDaily.module';
import { GuideStatusModule } from '../guideStatus/guideStatus.module';
import { CensoStatusModule } from './censoStatus.module';
import { HospitalizationModule } from '../hospitalization/hospitalization.module';
import { DeleteCensoDadosService } from 'src/core/application/services/censoDados/deleteCensoDados.service';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		DatabaseModule,
		CensoValidationModule,
		PatientModule,
		HospitalModule,
		BedHospitalModule,
		FormCidsModule,
		GuideModule,
		GuideDailyModule,
		GuideStatusModule,
		CensoStatusModule,
		HospitalizationModule,
	],
	controllers: [CensoDadosController],
	providers: [...censoDadosProviders],
	exports: [
		'FindCensoDadosGateway',
		InsertCensoDadosService.InsertCensoDadosService,
		DeleteCensoDadosService.name,
	],
})
export class CensoDadosModule {}
