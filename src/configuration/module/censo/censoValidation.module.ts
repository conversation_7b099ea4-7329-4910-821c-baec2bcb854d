import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { InfraModule } from '../infra.module';
import { censoValidationProviders } from 'src/configuration/providers/censo/censoValidation.providers';

@Module({
	imports: [ConfigModule.forRoot({}), InfraModule],
	providers: [...censoValidationProviders],
	exports: ['ValidationPolicyFactory'],
})
export class CensoValidationModule {}
