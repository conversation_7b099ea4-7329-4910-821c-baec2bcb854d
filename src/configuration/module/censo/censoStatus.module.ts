import { Module } from '@nestjs/common';
import { DatabaseModule } from '../../database/database.module';
import { ConfigModule } from '@nestjs/config';
import { censoStatusProvider } from 'src/configuration/providers/censo/censoStatus.provider';
import { ChangeCensoStatusService } from 'src/core/application/services/censo/changeCensoStatus.service';
import { InfraModule } from '../infra.module';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule, InfraModule],
	providers: [...censoStatusProvider],
	exports: [ChangeCensoStatusService.ChangeCensoStatusService],
})
export class CensoStatusModule {}
