import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { guideProviders } from 'src/configuration/providers/guide/guide.providers';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	providers: [...guideProviders],
	exports: ['InsertGuideService'],
})
export class GuideModule {}
