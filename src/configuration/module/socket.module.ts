import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../database/database.module';
import { socketProviders } from '../providers/socket.providers';
import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { InfraModule } from './infra.module';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule, InfraModule],
	providers: [...socketProviders],
	exports: [NotificationCensoService.NotificationCensoService],
})
export class SocketModule {}
