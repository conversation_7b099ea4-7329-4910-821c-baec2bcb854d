import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { autorizacoesProvider } from 'src/configuration/providers/autorizacoes/autorizacoes.provider';
import { AutorizacoesController } from 'src/entrypoint/controller/autorizacoes/autorizacoes.controller';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	controllers: [AutorizacoesController],
	providers: [...autorizacoesProvider],
})
export class AutorizacoesModule {}
