import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from '../database/database.module';
import { CensoSocket } from 'src/entrypoint/socket/censo.socket';
import { censoDadosProviders } from '../providers/censo/censoDados.providers';
import { InfraModule } from './infra.module';
import { AmqpModule } from './amqp.module';
import { SocketModule } from './socket.module';
import { CensoValidationModule } from './censo/censoValidation.module';
import { AppController } from 'src/entrypoint/controller/app.controller';
import { PatientModule } from './entities-handlers/patient.module';
import { HospitalModule } from './hospital/hospital.module';
import { BedHospitalModule } from './bedHospital/bedHospital.module';
import { FormCidsModule } from './formCids/formCids.module';
import { GuideModule } from './guide/guide.module';
import { GuideDailyModule } from './guideDaily/guideDaily.module';
import { GuideStatusModule } from './guideStatus/guideStatus.module';
import { CensoStatusModule } from './censo/censoStatus.module';
import { HospitalizationModule } from './hospitalization/hospitalization.module';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		DatabaseModule,
		InfraModule,
		AmqpModule,
		SocketModule,
		CensoValidationModule,
		PatientModule,
		HospitalModule,
		BedHospitalModule,
		FormCidsModule,
		GuideModule,
		GuideDailyModule,
		GuideStatusModule,
		CensoStatusModule,
		HospitalizationModule,
	],
	controllers: [AppController],
	providers: [CensoSocket, ...censoDadosProviders],
})
export class consumerModule {}
