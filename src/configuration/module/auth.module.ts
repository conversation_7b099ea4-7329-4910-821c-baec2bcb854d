import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { JwtModule } from '@nestjs/jwt';
import { AuthController } from 'src/entrypoint/controller/auth/auth.controller';
import { authProviders } from '../providers/auth.providers';
import { LocalStrategy } from '../strategies/auth/local.strategy';
import { UsersModule } from './users.module';
import { JwtStrategy } from '../strategies/auth/jwt.strategy';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		JwtModule.register({
			global: true,
			secret: process.env.SECRET_JWT,
			signOptions: { expiresIn: '1h' },
		}),
		UsersModule,
	],
	controllers: [AuthController],
	providers: [...authProviders, LocalStrategy, JwtStrategy],
})
export class AuthModule {}
