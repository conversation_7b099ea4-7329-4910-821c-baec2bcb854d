import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { permissionsProvider } from 'src/configuration/providers/permissions/permissions.provider';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	providers: [...permissionsProvider],
	exports: [...permissionsProvider],
})
export class PermissionsModule {}
