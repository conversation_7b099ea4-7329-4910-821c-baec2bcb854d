import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { bedHospitalProvider } from 'src/configuration/providers/bedHospital/bedHospital.provider';
import { FindBedHospitalService } from 'src/core/application/services/bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from 'src/core/application/services/bedHospital/updateBedHospital.service';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	providers: [...bedHospitalProvider],
	exports: [
		'InsertBedHospitalService',
		FindBedHospitalService.name,
		UpdateBedHospitalService.name,
	],
})
export class BedHospitalModule {}
