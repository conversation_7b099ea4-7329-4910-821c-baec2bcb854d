import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { guideStatusProviders } from 'src/configuration/providers/guideStatus/guideStatus.providers';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	providers: [...guideStatusProviders],
	exports: ['InsertGuideStatusService'],
})
export class GuideStatusModule {}
