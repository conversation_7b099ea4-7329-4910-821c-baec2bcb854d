import { Module } from '@nestjs/common';

import { DatabaseModule } from 'src/configuration/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { TagsController } from 'src/entrypoint/controller/tags/tags.controller';
import { tagsProviders } from 'src/configuration/providers/tags/tags.providers';

@Module({
	imports: [ConfigModule.forRoot({}), DatabaseModule],
	controllers: [TagsController],
	providers: [...tagsProviders],
})
export class TagsModule {}
