import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { formCidsProviders } from 'src/configuration/providers/formCids/formCids.providers';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	providers: [...formCidsProviders],
	exports: ['InsertFormCidsService'],
})
export class FormCidsModule {}
