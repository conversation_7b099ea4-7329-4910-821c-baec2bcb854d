import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { amqpProviders } from '../providers/amqp.providers';
import { CensoDadosModule } from './censo/censoDados.module';
import { CensoValidationModule } from './censo/censoValidation.module';
import { SocketModule } from './socket.module';
import { PatientModule } from './entities-handlers/patient.module';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		CensoDadosModule,
		CensoValidationModule,
		SocketModule,
		CensoValidationModule,
		PatientModule,
	],
	providers: [...amqpProviders],
	exports: ['ProducerMqttConfig', 'ConsumerMqttConfig'],
})
export class AmqpModule {}
