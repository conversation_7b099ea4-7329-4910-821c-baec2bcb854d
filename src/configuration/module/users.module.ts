import { Module } from '@nestjs/common';
import { UsersController } from '../../entrypoint/controller/users/users.controller';
import { usersProviders } from '../providers/users.providers';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { ConfigModule } from '@nestjs/config';
import { CompanyConfigModule } from './companyConfig/companyConfig.module';
import { PermissionsModule } from './permissions/permissions.module';

@Module({
	imports: [
		ConfigModule.forRoot({}),
		DatabaseModule,
		CompanyConfigModule,
		PermissionsModule,
	],
	controllers: [UsersController],
	providers: [...usersProviders],
	exports: ['FindUserGateway'],
})
export class UsersModule {}
