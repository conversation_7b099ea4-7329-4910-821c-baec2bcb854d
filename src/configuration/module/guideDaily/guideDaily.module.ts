import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { guideDailyProviders } from 'src/configuration/providers/guideDaily/guideDaily.providers';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	providers: [...guideDailyProviders],
	exports: ['InsertGuideDailyService'],
})
export class GuideDailyModule {}
