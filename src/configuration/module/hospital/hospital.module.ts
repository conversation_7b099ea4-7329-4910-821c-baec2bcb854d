import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from 'src/configuration/database/database.module';
import { InfraModule } from '../infra.module';
import { hospitalProvider } from 'src/configuration/providers/hospital/hospital.provider';
import { HospitalController } from '../../../entrypoint/controller/hospital/hospital.controller';

@Module({
	imports: [
		ConfigModule.forRoot({
			isGlobal: true,
		}),
		DatabaseModule,
		InfraModule,
	],
	controllers: [HospitalController],
	providers: [...hospitalProvider],
	exports: ['InsertHospitalService'],
})
export class HospitalModule {}
