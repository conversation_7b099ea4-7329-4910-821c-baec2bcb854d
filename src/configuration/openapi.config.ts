import { INestApplication } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';

export class OpenApiConfig {
	public static executeConfig(app: INestApplication): void {
		if (process.env.NODE_ENV !== 'dev') {
			return;
		}
		const config = new DocumentBuilder()
			.setTitle('Censo 2.0')
			.setDescription('Censo 2.0 API')
			.setVersion('1.0')
			.addBearerAuth(
				{
					type: 'http',
					scheme: 'Bearer',
					bearerFormat: 'JWT',
					in: 'Header',
				},
				'access-token',
			)
			.build();

		const document = SwaggerModule.createDocument(app, config);
		SwaggerModule.setup('api', app, document);
	}
}
