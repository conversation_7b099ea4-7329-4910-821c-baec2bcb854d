import { ExceptionFilter, Catch, ArgumentsHost } from '@nestjs/common';
import { Request, Response } from 'express';
import { ExceptionStrategyManager } from './strategies/exception/ExceptionStrategyManager';

@Catch()
export class HttpExceptionFilter implements ExceptionFilter {
	private strategyManager = new ExceptionStrategyManager();

	catch(exception: <PERSON>rror, host: ArgumentsHost): void {
		const ctx = host.switchToHttp();
		const response = ctx.getResponse<Response>();
		const request = ctx.getRequest<Request>();

		const strategy = this.strategyManager.getStrategy(exception);
		const exceptionResponse = strategy.handle(exception);

		console.error('erro capturado:  ', exception);
		response.status(exceptionResponse.status).json({
			statusCode: exceptionResponse.status,
			timestamp: new Date().toISOString(),
			path: request.url,
			error: exception.message,
			detail: exceptionResponse.detailList,
			tagException: exceptionResponse.tag,
		});
	}
}
