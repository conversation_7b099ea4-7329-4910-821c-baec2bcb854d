import { INestApplication } from '@nestjs/common';
import { SwaggerModule, DocumentBuilder } from '@nestjs/swagger';
import { OpenApiConfig } from './openapi.config';

jest.mock('@nestjs/swagger', () => ({
	SwaggerModule: {
		createDocument: jest.fn(),
		setup: jest.fn(),
	},
	DocumentBuilder: jest.fn(() => ({
		setTitle: jest.fn().mockReturnThis(),
		setDescription: jest.fn().mockReturnThis(),
		setVersion: jest.fn().mockReturnThis(),
		addBearerAuth: jest.fn().mockReturnThis(),
		build: jest.fn().mockReturnValue('mock-config'),
	})),
}));

describe('OpenApiConfig', () => {
	let appMock: INestApplication;
	const originalEnv = process.env.NODE_ENV;

	beforeEach(() => {
		appMock = {} as INestApplication;
		jest.clearAllMocks();
	});

	afterEach(() => {
		process.env.NODE_ENV = originalEnv;
	});

	it('deve executar a configuração do Swagger quando NODE_ENV for "dev"', () => {
		process.env.NODE_ENV = 'dev';
		(SwaggerModule.createDocument as jest.Mock).mockReturnValue('mock-config');

		OpenApiConfig.executeConfig(appMock);

		expect(DocumentBuilder).toHaveBeenCalled();
		expect(SwaggerModule.createDocument).toHaveBeenCalledWith(
			appMock,
			'mock-config',
		);
		expect(SwaggerModule.setup).toHaveBeenCalledWith(
			'api',
			appMock,
			'mock-config',
		);
	});

	it('não deve executar a configuração do Swagger quando NODE_ENV não for "dev"', () => {
		process.env.NODE_ENV = 'prod';

		OpenApiConfig.executeConfig(appMock);

		expect(DocumentBuilder).not.toHaveBeenCalled();
		expect(SwaggerModule.createDocument).not.toHaveBeenCalled();
		expect(SwaggerModule.setup).not.toHaveBeenCalled();
	});
});
