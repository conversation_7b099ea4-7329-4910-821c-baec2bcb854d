import { Test, TestingModule } from '@nestjs/testing';
import { UnauthorizedException } from '@nestjs/common';
import { LocalStrategy } from './local.strategy';
import { ValidateUserService } from 'src/core/application/services/auth/validateUser.service';
import { MessagesHelper } from '../../../helpers/messages.helper';
import { User } from 'src/core/domain/User';

describe('LocalStrategy', () => {
	let localStrategy: LocalStrategy;
	let validateUserService: ValidateUserService.ValidateUserService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [
				LocalStrategy,
				{
					provide: ValidateUserService.ValidateUserService,
					useValue: {
						validateUser: jest.fn(),
					},
				},
			],
		}).compile();

		localStrategy = module.get<LocalStrategy>(LocalStrategy);
		validateUserService = module.get<ValidateUserService.ValidateUserService>(
			ValidateUserService.ValidateUserService,
		);
	});

	it('must successfully authenticate the user', async () => {
		const mockUser: User = {
			id: 1,
			email: '<EMAIL>',
			name: 'User',
			companyId: '3',
		};
		jest.spyOn(validateUserService, 'validateUser').mockResolvedValue(mockUser);

		const result = await localStrategy.validate(
			'<EMAIL>',
			'valid_password',
		);

		expect(result).toEqual(mockUser);
		expect(validateUserService.validateUser).toHaveBeenCalledWith(
			'<EMAIL>',
			'valid_password',
		);
	});

	it('should throw UnauthorizedException if user is not found', async () => {
		jest.spyOn(validateUserService, 'validateUser').mockResolvedValue(null);

		await expect(
			localStrategy.validate('<EMAIL>', 'invalid_password'),
		).rejects.toThrow(
			new UnauthorizedException(MessagesHelper.PASSWORD_OR_EMAIL_INVALID),
		);
		expect(validateUserService.validateUser).toHaveBeenCalledWith(
			'<EMAIL>',
			'invalid_password',
		);
	});
});
