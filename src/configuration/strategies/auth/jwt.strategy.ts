import { Injectable } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { ExtractJwt, Strategy } from 'passport-jwt';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';
import { User } from 'src/core/domain/User';

@Injectable()
export class JwtStrategy extends PassportStrategy(Strategy) {
	constructor() {
		super({
			jwtFromRequest: ExtractJwt.fromAuthHeaderAsBearerToken(),
			ignoreExpiration: false,
			secretOrKey: process.env.SECRET_JWT || 'keyForUnitaryTest',
		});
	}

	async validate(payload: {
		sub: number;
		email: string;
		companyId: string;
		role: UserRoleEnum;
	}): Promise<Partial<User>> {
		return {
			id: payload.sub,
			email: payload.email,
			companyId: payload.companyId,
			role: payload.role,
		};
	}
}
