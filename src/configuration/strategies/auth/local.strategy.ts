import { Injectable, UnauthorizedException } from '@nestjs/common';
import { PassportStrategy } from '@nestjs/passport';
import { Strategy } from 'passport-local';

import { User } from 'src/core/domain/User';
import { MessagesHelper } from '../../../helpers/messages.helper';
import { ValidateUserService } from 'src/core/application/services/auth/validateUser.service';

@Injectable()
export class LocalStrategy extends PassportStrategy(Strategy) {
	constructor(
		private readonly validateUserService: ValidateUserService.ValidateUserService,
	) {
		super({ usernameField: 'email' });
	}

	async validate(email: string, password: string): Promise<User> {
		const user = await this.validateUserService.validateUser(email, password);

		if (!user)
			throw new UnauthorizedException(MessagesHelper.PASSWORD_OR_EMAIL_INVALID);

		return user;
	}
}
