import { Test, TestingModule } from '@nestjs/testing';
import { JwtStrategy } from './jwt.strategy';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';

describe('JwtStrategy', () => {
	let jwtStrategy: JwtStrategy;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			providers: [JwtStrategy],
		}).compile();

		jwtStrategy = module.get<JwtStrategy>(JwtStrategy);
	});

	it('should be defined', () => {
		expect(jwtStrategy).toBeDefined();
	});

	describe('validate', () => {
		it('should return a partial user object based on the payload', async () => {
			const payload = {
				sub: 1,
				email: '<EMAIL>',
				companyId: '10',
				role: UserRoleEnum.ADMIN,
			};
			const result = await jwtStrategy.validate(payload);

			expect(result).toEqual({
				id: payload.sub,
				email: payload.email,
				companyId: payload.companyId,
				role: payload.role,
			});
		});
	});
});
