import { ExceptionStrategy } from './abs/exception.strategy';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

export class DefaultExceptionStrategy implements ExceptionStrategy {
	/* eslint-disable @typescript-eslint/no-unused-vars */
	supports(exception: Error): boolean {
		return true;
	}

	handle(exception: Error): ExceptionResponseDto {
		return new ExceptionResponseDto(
			500,
			'Erro interno do servidor: ' + exception,
			Error.name,
		);
	}
}
