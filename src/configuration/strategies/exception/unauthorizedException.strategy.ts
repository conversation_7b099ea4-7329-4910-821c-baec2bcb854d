import { UnauthorizedException } from '@nestjs/common';
import { ExceptionStrategy } from './abs/exception.strategy';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

export class UnauthorizedExceptionStrategy implements ExceptionStrategy {
	supports(exception: Error): boolean {
		return exception instanceof UnauthorizedException;
	}

	handle(exception: Error): ExceptionResponseDto {
		return new ExceptionResponseDto(
			401,
			exception.message,
			UnauthorizedException.name,
		);
	}
}
