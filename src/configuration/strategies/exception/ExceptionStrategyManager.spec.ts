import { DefaultExceptionStrategy } from './defaultException.strategy';
import { ExceptionStrategyManager } from './ExceptionStrategyManager';

describe('ExceptionStrategyManager', () => {
	it('deve retornar um strategy que suporta a excecao', () => {
		const exception = new Error('Test error');
		const defaultExceptionStrategy = new DefaultExceptionStrategy();

		const exceptionStrategyManager = new ExceptionStrategyManager();

		const strategy = exceptionStrategyManager.getStrategy(exception);

		expect(strategy).toEqual(defaultExceptionStrategy);
	});
});
