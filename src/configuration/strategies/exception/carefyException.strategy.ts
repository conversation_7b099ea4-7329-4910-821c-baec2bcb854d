import { CarefyException } from 'src/shared/exceptions/Carefy.exception';
import { ExceptionStrategy } from './abs/exception.strategy';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

export class CarefyExceptionStrategy implements ExceptionStrategy {
	supports(exception: Error): boolean {
		return exception instanceof CarefyException;
	}

	handle(exception: Error): ExceptionResponseDto {
		const careException = exception as CarefyException;
		return new ExceptionResponseDto(
			careException.statusCode,
			exception.message,
			careException.tag,
			careException.fields,
		);
	}
}
