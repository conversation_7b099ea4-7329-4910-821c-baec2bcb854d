import { CarefyException } from 'src/shared/exceptions/Carefy.exception';
import { CarefyExceptionStrategy } from './carefyException.strategy';
import { InvalidCompanyException } from 'src/shared/exceptions/rule/InvalidCompany.exception';
import { HttpStatus } from '@nestjs/common';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

describe('CarefyExceptionStrategy', () => {
	let carefyExceptionStrategy: CarefyExceptionStrategy;
	let exception: CarefyException;

	beforeEach(() => {
		carefyExceptionStrategy = new CarefyExceptionStrategy();
		exception = new InvalidCompanyException(
			HttpStatus.BAD_REQUEST,
			'Erro de teste',
		);
	});

	it('deve retornar true para suportar a exceção', () => {
		expect(carefyExceptionStrategy.supports(exception)).toBeTruthy();
	});

	it('deve retornar statusCode e message da exceção', () => {
		const result = carefyExceptionStrategy.handle(exception);
		const expected = new ExceptionResponseDto(
			result.status,
			result.message,
			result.tag,
			result.detailList,
		);
		expect(result).toEqual(expected);
	});
});
