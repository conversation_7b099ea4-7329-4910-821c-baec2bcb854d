import { HttpStatus, UnauthorizedException } from '@nestjs/common';
import { UnauthorizedExceptionStrategy } from './unauthorizedException.strategy';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

describe('UnauthorizedExceptionStrategy', () => {
	let unauthorizedExceptionStrategy: UnauthorizedExceptionStrategy;
	let exception: UnauthorizedException;

	beforeEach(() => {
		unauthorizedExceptionStrategy = new UnauthorizedExceptionStrategy();
		exception = new UnauthorizedException(
			HttpStatus.UNAUTHORIZED,
			'Unauthorized Exception',
		);
	});

	it('deve retornar true para suportar a exceção', () => {
		expect(unauthorizedExceptionStrategy.supports(exception)).toBeTruthy();
	});

	it('deve retornar statusCode e message da exceção', () => {
		const result = unauthorizedExceptionStrategy.handle(exception);
		const expected = new ExceptionResponseDto(
			401,
			exception.message,
			UnauthorizedException.name,
		);
		expect(result).toEqual(expected);
	});
});
