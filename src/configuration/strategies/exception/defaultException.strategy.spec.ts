import { HttpStatus, InternalServerErrorException } from '@nestjs/common';
import { DefaultExceptionStrategy } from './defaultException.strategy';
import { ExceptionResponseDto } from './dto/exceptionResponse.dto';

describe('DefaultExceptionStrategy', () => {
	let defaultExceptionStrategy: DefaultExceptionStrategy;
	let exception: InternalServerErrorException;

	beforeEach(() => {
		defaultExceptionStrategy = new DefaultExceptionStrategy();
		exception = new InternalServerErrorException(
			HttpStatus.INTERNAL_SERVER_ERROR,
			'Erro interno do servidor',
		);
	});

	it('deve retornar true para suportar a exceção', () => {
		expect(defaultExceptionStrategy.supports(exception)).toBeTruthy();
	});

	it('deve retornar statusCode e message da exceção', () => {
		const result = defaultExceptionStrategy.handle(exception);
		const expected = new ExceptionResponseDto(
			500,
			'Erro interno do servidor: ' + exception,
			Error.name,
		);
		expect(result).toEqual(expected);
	});
});
