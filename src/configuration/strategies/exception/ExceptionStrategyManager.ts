import { ExceptionStrategy } from './abs/exception.strategy';
import { CarefyExceptionStrategy } from './carefyException.strategy';
import { DefaultExceptionStrategy } from './defaultException.strategy';
import { UnauthorizedExceptionStrategy } from './unauthorizedException.strategy';

export class ExceptionStrategyManager {
	private strategies: ExceptionStrategy[];

	constructor() {
		this.strategies = [
			new CarefyExceptionStrategy(),
			new UnauthorizedExceptionStrategy(),
			new DefaultExceptionStrategy(),
		];
	}

	getStrategy(exception: Error): ExceptionStrategy {
		return this.strategies.find((strategy) => strategy.supports(exception))!;
	}
}
