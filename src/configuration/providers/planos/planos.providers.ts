import { Provider } from '@nestjs/common';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { PlanosEntity } from '../../../gateway/entities/planos.entity';
import { FindPlanosGatewayImpl } from '../../../gateway/planos/findPlanos.gateway.impl';
import { FindPlanosService } from '../../../core/application/services/planos/findPlanos.service';
import { FindPlanosGateway } from '../../../core/application/gateway/planos/findPlanos.gateway';

export const planosProviders: Provider[] = [
	{
		provide: 'PLANOS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(PlanosEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindPlanosGateway',
		useFactory: (
			findPlanosRepository: Repository<PlanosEntity>,
		): FindPlanosGatewayImpl => new FindPlanosGatewayImpl(findPlanosRepository),
		inject: ['PLANOS_REPOSITORY'],
	},
	{
		provide: FindPlanosService.name,
		useFactory: (findPlanosGateway: FindPlanosGateway): FindPlanosService => {
			return new FindPlanosService(findPlanosGateway);
		},
		inject: ['FindPlanosGateway'],
	},
];
