import { FileParserValidatorGateway } from 'src/core/application/gateway/parser/fileParserValidator.gateway';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { FileParserValidatorGatewayImpl } from 'src/gateway/parser/fileParserValidator.gateway.Impl';
import { UploadFileGatewayImpl } from 'src/gateway/file/upload/uploadFile.gateway.impl';
import { UploadFileGateway } from '../../core/application/gateway/file/upload/uploadFile.gateway';
import { GoogleStorageWrapper } from '../apis/gcs.api.config';
import { DataSource, ObjectLiteral } from 'typeorm';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { InsertCensoService } from 'src/core/application/services/censo/insertCenso.service';
import { UploadCensoService } from 'src/core/application/services/file/upload/censo/uploadCenso.service';
import { Provider } from '@nestjs/common';
import { DeleteFileService } from '../../core/application/services/file/deleteFile.service';

export const uploadProviders: Provider[] = [
	{
		provide: 'CENSO_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CensoEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FileParserValidatorGateway',
		useFactory: (): FileParserValidatorGatewayImpl => {
			return new FileParserValidatorGatewayImpl();
		},
	},
	{
		provide: 'UploadFileGateway',
		useFactory: (): UploadFileGatewayImpl => {
			return new UploadFileGatewayImpl(new GoogleStorageWrapper());
		},
	},
	{
		provide: UploadCensoService.UploadCensoService,
		useFactory: (
			fileParserValidatorGateway: FileParserValidatorGateway,
			validationPolicyFactory: ValidationPolicyFactory,
			uploadFileGateway: UploadFileGateway,
			insertCensoService: InsertCensoService.InsertCensoService,
			deleteFileService: DeleteFileService.DeleteFileService,
		): UploadCensoService.UploadCensoService => {
			return new UploadCensoService.UploadCensoService(
				fileParserValidatorGateway,
				validationPolicyFactory,
				uploadFileGateway,
				insertCensoService,
				deleteFileService,
			);
		},
		inject: [
			'FileParserValidatorGateway',
			'ValidationPolicyFactory',
			'UploadFileGateway',
			InsertCensoService.InsertCensoService,
			DeleteFileService.DeleteFileService,
		],
	},
];
