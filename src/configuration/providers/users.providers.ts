import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { UserEntity } from '../../gateway/entities/user.entity';
import { FindUserGatewayImpl } from 'src/gateway/user/findUser.gateway.Impl';
import { FindUserService } from 'src/core/application/services/user/findUser.service';
import { FindUserGateway } from 'src/core/application/gateway/user/findUser.gateway';
import { FindCompanyConfigService } from 'src/core/application/services/companyConfig/findCompanyConfig.service';
import { FindPermissionsService } from 'src/core/application/services/permissions/findPermissions.service';

export const usersProviders = [
	{
		provide: 'USERS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(UserEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindUserGateway',
		useFactory: (userRepository: Repository<UserEntity>): FindUserGatewayImpl =>
			new FindUserGatewayImpl(userRepository),
		inject: ['USERS_REPOSITORY'],
	},
	{
		provide: FindUserService.FindUserService,
		useFactory: (
			findUserGateway: FindUserGateway,
			findCompanyConfigService: FindCompanyConfigService,
			findPermissionsService: FindPermissionsService,
		): FindUserService.FindUserService => {
			return new FindUserService.FindUserService(
				findUserGateway,
				findCompanyConfigService,
				findPermissionsService,
			);
		},
		inject: [
			'FindUserGateway',
			FindCompanyConfigService.name,
			FindPermissionsService.name,
		],
	},
];
