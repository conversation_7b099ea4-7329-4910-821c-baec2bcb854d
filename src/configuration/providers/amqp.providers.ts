import { Provider } from '@nestjs/common';
import { ProducerMqttConfig } from '../amqp/abstract/producer.mqtt.config';
import { connect } from 'amqplib';
import { AmqpProducerConfig } from '../amqp/amqp.producer.config';
import { ProducerMqttConfigImpl } from '../amqp/producer.mqtt.config.impl';
import { ConsumerFactory } from 'src/entrypoint/consumer/consumer.factory';
import { ConsumerMqttConfig } from '../amqp/abstract/consumer.mqtt.config';
import { ConsumerMqttConfigImpl } from '../amqp/consumer.mqtt.config.impl';
import { AmqpConsumerConfig } from '../amqp/amqp.consumer.config';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';

export const amqpProviders: Provider[] = [
	{
		provide: 'ProducerMqttConfig',
		async useFactory(): Promise<ProducerMqttConfig> {
			const url = `amqp://${process.env.CAREFY_RABBIT_MQ_CONSUMER_USER}:${process.env.CAREFY_RABBIT_MQ_CONSUMER_PASS}@${process.env.CAREFY_RABBIT_MQ_HOST}:${process.env.CAREFY_RABBIT_MQ_PORT}`;
			const connection = await connect(url);
			const channel = await connection.createChannel();
			const amqpClientConfig = new AmqpProducerConfig(channel);
			return new ProducerMqttConfigImpl(amqpClientConfig, 'censo-queue');
		},
	},
	{
		provide: 'ConsumerMqttConfig',
		async useFactory(
			consumerFactory: ConsumerFactory,
		): Promise<ConsumerMqttConfig> {
			const url = `amqp://${process.env.CAREFY_RABBIT_MQ_CONSUMER_USER}:${process.env.CAREFY_RABBIT_MQ_CONSUMER_PASS}@${process.env.CAREFY_RABBIT_MQ_HOST}:${process.env.CAREFY_RABBIT_MQ_PORT}`;
			const connection = await connect(url);
			const channel = await connection.createChannel();
			const amqpClientConfig = new AmqpConsumerConfig(channel, consumerFactory);
			return new ConsumerMqttConfigImpl(amqpClientConfig);
		},
		inject: [ConsumerFactory],
	},
	{
		provide: ConsumerFactory,
		useFactory(
			notificationCensoService: NotificationCensoService.NotificationCensoService,
			validationPolicyFactory: ValidationPolicyFactory,
			insertCensoDadosService: InsertCensoDadosService.InsertCensoDadosService,
			findPatientService: FindPatientService,
		): ConsumerFactory {
			return new ConsumerFactory(
				notificationCensoService,
				validationPolicyFactory,
				insertCensoDadosService,
				findPatientService,
			);
		},
		inject: [
			NotificationCensoService.NotificationCensoService,
			'ValidationPolicyFactory',
			InsertCensoDadosService.InsertCensoDadosService,
			FindPatientService.name,
		],
	},
];
