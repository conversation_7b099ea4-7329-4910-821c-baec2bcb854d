import { CriptografiaGateway } from 'src/core/application/gateway/criptografia/criptografia.gateway';
import { CriptografiaService } from 'src/core/application/services/criptografia/criptografia.service';
import { CriptografiaGatewayImpl } from 'src/gateway/criptografia/criptografia.gateway.impl';

export const criptografiaProviders = [
	{
		provide: 'CriptografiaGateway',
		useFactory: (): CriptografiaGatewayImpl => new CriptografiaGatewayImpl(),
	},
	{
		provide: CriptografiaService.name,
		useFactory: (
			CriptografiaGateway: CriptografiaGateway,
		): CriptografiaService => {
			return new CriptografiaService(CriptografiaGateway);
		},
		inject: ['CriptografiaGateway'],
	},
];
