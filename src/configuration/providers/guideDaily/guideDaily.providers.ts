import { Provider } from '@nestjs/common';
import { InsertGuideDailyGateway } from 'src/core/application/gateway/guideDaily/insertGuideDaily.gateway';
import { InsertGuideDailyService } from 'src/core/application/services/guideDaily/insertGuideDaily.service';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { InsertGuideDailyGatewayImpl } from 'src/gateway/guideDaily/insertGuideDaily.gateway.impl';
import { Repository } from 'typeorm';

export const guideDailyProviders: Provider[] = [
	{
		provide: 'InsertGuideDailyGateway',
		useFactory: (
			guideDailyRepository: Repository<GuideDailyEntity>,
		): InsertGuideDailyGateway =>
			new InsertGuideDailyGatewayImpl(guideDailyRepository),
		inject: ['GUIDE_DAILY_REPOSITORY'],
	},

	{
		provide: 'InsertGuideDailyService',
		useFactory: (
			insertGuideDailyGateway: InsertGuideDailyGateway,
		): InsertGuideDailyService =>
			new InsertGuideDailyService(insertGuideDailyGateway),
		inject: ['InsertGuideDailyGateway'],
	},
];
