import { Provider } from '@nestjs/common';
import { InsertGuideStatusGateway } from 'src/core/application/gateway/guideStatus/insertGuideStatus.gateway';
import { InsertGuideStatusService } from 'src/core/application/services/guideStatus/insertGuideStatus.service';
import { GuideStatusEntity } from 'src/gateway/entities/guideStatus.entity';
import { InsertGuideStatusGatewayImpl } from 'src/gateway/guideStatus/insertGuideStatus.gateway.impl';
import { Repository } from 'typeorm';

export const guideStatusProviders: Provider[] = [
	{
		provide: 'InsertGuideStatusGateway',
		useFactory: (
			guideStatusRepository: Repository<GuideStatusEntity>,
		): InsertGuideStatusGateway =>
			new InsertGuideStatusGatewayImpl(guideStatusRepository),
		inject: ['GUIDE_STATUS_REPOSITORY'],
	},
	{
		provide: 'InsertGuideStatusService',
		useFactory: (
			insertGuideStatusGateway: InsertGuideStatusGateway,
		): InsertGuideStatusService =>
			new InsertGuideStatusService(insertGuideStatusGateway),
		inject: ['InsertGuideStatusGateway'],
	},
];
