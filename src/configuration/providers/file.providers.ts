import { Provider } from '@nestjs/common';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';
import { DownloadFileGatewayImpl } from 'src/gateway/file/download/downloadFile.gateway.impl';
import { GoogleStorageWrapper } from '../apis/gcs.api.config';
import { DeleteFileService } from 'src/core/application/services/file/deleteFile.service';

export const fileProviders: Provider[] = [
	{
		provide: 'DownloadFileGateway',
		useFactory(): DownloadFileGateway {
			return new DownloadFileGatewayImpl(new GoogleStorageWrapper());
		},
	},
	{
		provide: DeleteFileService.DeleteFileService,
		useFactory(): DeleteFileService.DeleteFileService {
			return new DeleteFileService.DeleteFileService();
		},
	},
];
