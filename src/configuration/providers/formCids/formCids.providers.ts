import { Provider } from '@nestjs/common';
import { InsertFormCidsGateway } from 'src/core/application/gateway/formCids/insertFormCids.gateway';
import { InsertFormCidsService } from 'src/core/application/services/formCids/InsertFormCids.service';
import { FormCidsEntity } from 'src/gateway/entities/formCids.entity';
import { InsertFormCidsGatewayImpl } from 'src/gateway/formCids/insertFormCids.gateway.impl';
import { Repository } from 'typeorm';

export const formCidsProviders: Provider[] = [
	{
		provide: 'InsertFormCidsGateway',
		useFactory: (
			formCidsRepository: Repository<FormCidsEntity>,
		): InsertFormCidsGateway =>
			new InsertFormCidsGatewayImpl(formCidsRepository),
		inject: ['FORM_CIDS_REPOSITORY'],
	},
	{
		provide: 'InsertFormCidsService',
		useFactory: (
			insertFormCidsGateway: InsertFormCidsGateway,
		): InsertFormCidsService =>
			new InsertFormCidsService(insertFormCidsGateway),
		inject: ['InsertFormCidsGateway'],
	},
];
