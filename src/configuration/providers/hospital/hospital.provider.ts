import { Provider } from '@nestjs/common';
import { FindHospitalGateway } from 'src/core/application/gateway/hospital/findHospital.gateway';
import { FindHospitalCompanyGateway } from 'src/core/application/gateway/hospital/findHospitalCompany.gateway';
import { InsertHospitalGateway } from 'src/core/application/gateway/hospital/insertHospitalGateway';
import { InsertHospitalService } from 'src/core/application/services/hospital/insert/insertHospital.service';
import { CityEntity } from 'src/gateway/entities/city.entity';
import { CnesMunicipioOldEntity } from 'src/gateway/entities/cnesMunicipioOld.entity';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';
import { FindHospitalGatewayImpl } from 'src/gateway/hospital/findHospital.gateway.impl';
import { FindHospitalCompanyGatewayImpl } from 'src/gateway/hospitalCompany/findHospitalCompany.gateway.impl';
import { InsertHospitalGatewayImpl } from 'src/gateway/hospitalCompany/insertHospital.gateway.impl';
import { Repository } from 'typeorm';
import { FindHospitalService } from '../../../core/application/services/hospital/find/findHospital.service';

export const hospitalProvider: Provider[] = [
	{
		provide: 'InsertHospitalGateway',
		useFactory: (
			hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
			hospitalRepository: Repository<HospitalEntity>,
			cnesMunicipioOldRepository: Repository<CnesMunicipioOldEntity>,
			cityRepository: Repository<CityEntity>,
		): InsertHospitalGateway => {
			return new InsertHospitalGatewayImpl(
				hospitalCompanyRepository,
				hospitalRepository,
				cnesMunicipioOldRepository,
				cityRepository,
			);
		},
		inject: [
			'HOSPITAL_COMPANY_REPOSITORY',
			'HOSPITAL_REPOSITORY',
			'CNES_MUNICIPIO_OLD_REPOSITORY',
			'CITY_REPOSITORY',
		],
	},
	{
		provide: FindHospitalService.name,
		useFactory: (
			findHospitalCompanyGateway: FindHospitalCompanyGateway,
		): FindHospitalService => {
			return new FindHospitalService(findHospitalCompanyGateway);
		},
		inject: ['FindHospitalCompanyGateway'],
	},

	{
		provide: 'FindHospitalCompanyGateway',
		useFactory: (
			hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
		): FindHospitalCompanyGateway => {
			return new FindHospitalCompanyGatewayImpl(hospitalCompanyRepository);
		},
		inject: ['HOSPITAL_COMPANY_REPOSITORY'],
	},
	{
		provide: 'FindHospitalGateway',
		useFactory: (
			hospitalsRepository: Repository<HospitalEntity>,
		): FindHospitalGatewayImpl => {
			return new FindHospitalGatewayImpl(hospitalsRepository);
		},
		inject: ['HOSPITAL_REPOSITORY'],
	},
	{
		provide: 'InsertHospitalService',
		useFactory: (
			findHospitalCompanyGateway: FindHospitalCompanyGateway,
			insertHospitalGateway: InsertHospitalGateway,
			findHospitalGateway: FindHospitalGateway,
		): InsertHospitalService =>
			new InsertHospitalService(
				findHospitalCompanyGateway,
				insertHospitalGateway,
				findHospitalGateway,
			),
		inject: [
			'FindHospitalCompanyGateway',
			'InsertHospitalGateway',
			'FindHospitalGateway',
		],
	},
];
