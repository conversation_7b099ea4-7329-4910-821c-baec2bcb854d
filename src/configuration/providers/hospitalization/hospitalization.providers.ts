import { Provider } from '@nestjs/common';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { FindHospitalizationGatewayImpl } from 'src/gateway/hospitalization/findHospitalization.gateway.impl';
import { UpdateHospitalizationGatewayImpl } from 'src/gateway/hospitalization/updateHospitalization.gateway.impl';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';

export const hospitalizationProvider: Provider[] = [
	{
		provide: 'HOSPITALIZATION_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(HospitalizationEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindHospitalizationGateway',
		useFactory: (
			findHospitalizationRepository: Repository<HospitalizationEntity>,
		): FindHospitalizationGatewayImpl =>
			new FindHospitalizationGatewayImpl(findHospitalizationRepository),
		inject: ['HOSPITALIZATION_REPOSITORY'],
	},
	{
		provide: 'UpdateHospitalizationGateway',
		useFactory: (
			hospitalizationRepository: Repository<HospitalizationEntity>,
		): UpdateHospitalizationGatewayImpl =>
			new UpdateHospitalizationGatewayImpl(hospitalizationRepository),
		inject: ['HOSPITALIZATION_REPOSITORY'],
	},
];
