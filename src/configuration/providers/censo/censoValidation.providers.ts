import { Provider } from '@nestjs/common';
import { ConflictValidationFactoryGateway } from 'src/core/application/gateway/validation/conflictValidation.factory.gateway';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { ValidationPolicyFactoryImpl } from 'src/core/application/validations/factory/validation.policy.factory.Impl';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { ConflictValidationFactoryGatewayImpl } from 'src/gateway/validation/conflictValidation.factory.gateway.impl';
import { Repository } from 'typeorm';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';

export const censoValidationProviders: Provider[] = [
	{
		provide: 'ConflictValidationGateway',
		useFactory: (
			hospitalCompanyRepository: Repository<HospitalsCompanyEntity>,
			hospitalizationRepository: Repository<HospitalizationEntity>,
			patientRepository: Repository<PatientEntity>,
		): ConflictValidationFactoryGateway =>
			new ConflictValidationFactoryGatewayImpl(
				hospitalCompanyRepository,
				hospitalizationRepository,
				patientRepository,
			),
		inject: [
			'HOSPITAL_COMPANY_REPOSITORY',
			'HOSPITALIZATION_REPOSITORY',
			'PATIENT_REPOSITORY',
		],
	},
	{
		provide: 'ValidationPolicyFactory',
		useFactory: (
			conflictValidationGateway: ConflictValidationFactoryGateway,
		): ValidationPolicyFactory =>
			new ValidationPolicyFactoryImpl(conflictValidationGateway),
		inject: ['ConflictValidationGateway'],
	},
];
