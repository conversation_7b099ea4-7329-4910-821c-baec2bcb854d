import { Provider } from '@nestjs/common';
import { FindCensoDadosGateway } from 'src/core/application/gateway/censoDados/findCensoDados.gateway';
import { InsertCensoDadosGateway } from 'src/core/application/gateway/censoDados/insertCensoDados.gateway';
import { FindCensoDadosService } from 'src/core/application/services/censoDados/findCensoDados.service';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { FindCensoDadosGatewayImpl } from 'src/gateway/censoDados/findCensoDados.gateway.Impl';
import { InsertCensoDadosGatewayImpl } from 'src/gateway/censoDados/insertCensoDados.gateway.impl';
import { CensoDadosEntity } from 'src/gateway/entities/censoDados.entity';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { LinhasConflitadasCensoEntity } from '../../../gateway/entities/linhasConflitadasCenso.entity';
import { EditCensoDadosService } from 'src/core/application/services/censoDados/editCensoDados.service';
import { EditCensoDadosGateway } from 'src/core/application/gateway/censoDados/editCensoDados.gateway';
import { EditCensoDadosGatewayImpl } from 'src/gateway/censoDados/editCensoDados.gateway.impl';
import { ProcessCensoDadosService } from 'src/core/application/services/censoDados/processCensoDados.service';
import { CensoDadosChainFactory } from 'src/core/application/services/censoDados/chain/factory/censoDadosChain.factory';
import { CensoDadosChainBuilder } from 'src/core/application/services/censoDados/chain/abstract/censoDadosChain.builder';
import { CensoDadosChainBuilderImpl } from 'src/core/application/services/censoDados/chain/censoDadosChain.builder.impl';
import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { InsertHospitalService } from 'src/core/application/services/hospital/insert/insertHospital.service';
import { InsertBedHospitalService } from 'src/core/application/services/bedHospital/insertBedHospital.service';
import { InsertFormCidsService } from 'src/core/application/services/formCids/InsertFormCids.service';
import { InsertGuideService } from 'src/core/application/services/guide/insertGuide.service';
import { InsertGuideDailyService } from 'src/core/application/services/guideDaily/insertGuideDaily.service';
import { InsertGuideStatusService } from 'src/core/application/services/guideStatus/insertGuideStatus.service';
import { ChangeCensoStatusService } from 'src/core/application/services/censo/changeCensoStatus.service';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';
import { FindBedHospitalService } from 'src/core/application/services/bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from 'src/core/application/services/bedHospital/updateBedHospital.service';
import { DeleteCensoDadosGateway } from 'src/core/application/gateway/censoDados/deleteCensoDados.gateway';
import { DeleteCensoDadosGatewayImpl } from 'src/gateway/censoDados/deleteCensoDados.gateway.impl';
import { DeleteCensoDadosService } from 'src/core/application/services/censoDados/deleteCensoDados.service';

export const censoDadosProviders: Provider[] = [
	{
		provide: 'CENSO_DADOS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CensoDadosEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'LINHAS_CONFLITADAS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(LinhasConflitadasCensoEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindCensoDadosGateway',
		useFactory: (
			censoDadosRepository: Repository<CensoDadosEntity>,
		): FindCensoDadosGatewayImpl =>
			new FindCensoDadosGatewayImpl(censoDadosRepository),
		inject: ['CENSO_DADOS_REPOSITORY'],
	},
	{
		provide: FindCensoDadosService.FindCensoDadosService,
		useFactory: (
			findCensoDadosGateway: FindCensoDadosGateway,
		): FindCensoDadosService.FindCensoDadosService => {
			return new FindCensoDadosService.FindCensoDadosService(
				findCensoDadosGateway,
			);
		},
		inject: ['FindCensoDadosGateway'],
	},
	{
		provide: 'FindCensoDadosGateway',
		useFactory: (
			censoDadosRepository: Repository<CensoDadosEntity>,
		): FindCensoDadosGateway => {
			return new FindCensoDadosGatewayImpl(censoDadosRepository);
		},
		inject: ['CENSO_DADOS_REPOSITORY'],
	},
	{
		provide: 'InsertCensoDadosGateway',
		useFactory: (
			censoDadosRepository: Repository<CensoDadosEntity>,
			linhasConflitadasCensoEntity: Repository<LinhasConflitadasCensoEntity>,
		): InsertCensoDadosGateway => {
			return new InsertCensoDadosGatewayImpl(
				censoDadosRepository,
				linhasConflitadasCensoEntity,
			);
		},
		inject: ['CENSO_DADOS_REPOSITORY', 'LINHAS_CONFLITADAS_REPOSITORY'],
	},
	{
		provide: InsertCensoDadosService.InsertCensoDadosService,
		useFactory: (
			insertCensoDadosGateway: InsertCensoDadosGateway,
		): InsertCensoDadosService.InsertCensoDadosService => {
			return new InsertCensoDadosService.InsertCensoDadosService(
				insertCensoDadosGateway,
			);
		},
		inject: ['InsertCensoDadosGateway'],
	},
	{
		provide: EditCensoDadosService.EditCensoDadosService,
		useFactory: (
			editCensoDadosGateway: EditCensoDadosGatewayImpl,
		): EditCensoDadosService.EditCensoDadosService => {
			return new EditCensoDadosService.EditCensoDadosService(
				editCensoDadosGateway,
			);
		},
		inject: ['EditCensoDadosGateway'],
	},
	{
		provide: 'EditCensoDadosGateway',
		useFactory: (
			censoDadosRepository: Repository<CensoDadosEntity>,
			linhasConflitadasRepository: Repository<LinhasConflitadasCensoEntity>,
		): EditCensoDadosGateway => {
			return new EditCensoDadosGatewayImpl(
				censoDadosRepository,
				linhasConflitadasRepository,
			);
		},
		inject: ['CENSO_DADOS_REPOSITORY', 'LINHAS_CONFLITADAS_REPOSITORY'],
	},
	{
		provide: ProcessCensoDadosService.ProcessCensoDadosService,
		useFactory: (
			findCensoDadosService: FindCensoDadosService.FindCensoDadosService,
			censoDadosChainFactory: CensoDadosChainFactory,
			changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		): ProcessCensoDadosService.ProcessCensoDadosService => {
			return new ProcessCensoDadosService.ProcessCensoDadosService(
				findCensoDadosService,
				censoDadosChainFactory,
				changeCensoStatusService,
			);
		},
		inject: [
			FindCensoDadosService.FindCensoDadosService,
			CensoDadosChainFactory,
			ChangeCensoStatusService.ChangeCensoStatusService,
		],
	},
	{
		provide: CensoDadosChainFactory,
		useFactory: (
			censoDadosChainBuilder: CensoDadosChainBuilder,
			insertPatientGateway: InsertPatientGateway,
			insertHospitalizationGateway: InsertHospitalizationGateway,
			insertHospitalService: InsertHospitalService,
			insertBedHospitalService: InsertBedHospitalService,
			insertFormCidsService: InsertFormCidsService,
			insertGuideService: InsertGuideService,
			insertGuideDailyService: InsertGuideDailyService,
			insertGuideStatusService: InsertGuideStatusService,
			findPatientGateway: FindPatientGateway,
			findHospitalizationGateway: FindHospitalizationGateway,
			updateHospitalizationGateway: UpdateHospitalizationGateway,
			findBedHospitalService: FindBedHospitalService,
			updateBedHospitalService: UpdateBedHospitalService,
		): CensoDadosChainFactory => {
			return new CensoDadosChainFactory(
				censoDadosChainBuilder,
				insertPatientGateway,
				insertHospitalizationGateway,
				insertHospitalService,
				insertBedHospitalService,
				insertFormCidsService,
				insertGuideService,
				insertGuideDailyService,
				insertGuideStatusService,
				findPatientGateway,
				findHospitalizationGateway,
				updateHospitalizationGateway,
				findBedHospitalService,
				updateBedHospitalService,
			);
		},
		inject: [
			'CensoDadosChainBuilder',
			'InsertPatientGateway',
			'InsertHospitalizationGateway',
			'InsertHospitalService',
			'InsertBedHospitalService',
			'InsertFormCidsService',
			'InsertGuideService',
			'InsertGuideDailyService',
			'InsertGuideStatusService',
			'findPatientGateway',
			'FindHospitalizationGateway',
			'UpdateHospitalizationGateway',
			FindBedHospitalService.name,
			UpdateBedHospitalService.name,
		],
	},
	{
		provide: 'CensoDadosChainBuilder',
		useFactory: (): CensoDadosChainBuilder => {
			return new CensoDadosChainBuilderImpl();
		},
	},
	{
		provide: 'DeleteCensoDadosGateway',
		useFactory: (
			censoDadosRepository: Repository<CensoDadosEntity>,
		): DeleteCensoDadosGateway => {
			return new DeleteCensoDadosGatewayImpl(censoDadosRepository);
		},
		inject: ['CENSO_DADOS_REPOSITORY'],
	},
	{
		provide: DeleteCensoDadosService.name,
		useFactory: (
			deleteCensoDadosGateway: DeleteCensoDadosGateway,
		): DeleteCensoDadosService => {
			return new DeleteCensoDadosService(deleteCensoDadosGateway);
		},
		inject: ['DeleteCensoDadosGateway'],
	},
];
