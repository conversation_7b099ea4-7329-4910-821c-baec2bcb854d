import { Repository } from 'typeorm';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { FindCensoService } from 'src/core/application/services/censo/findCenso.service';
import { FindCensoGateway } from 'src/core/application/gateway/censo/findCenso.gateway';
import { FindCensoGatewayImpl } from 'src/gateway/censo/findCenso.gateway.Impl';
import { InsertCensoGateway } from 'src/core/application/gateway/censo/insertCenso.gateway';
import { InsertCensoGatewayImpl } from 'src/gateway/censo/insertCenso.gateway.impl';
import { InsertCensoService } from 'src/core/application/services/censo/insertCenso.service';
import { ProcessCensoService } from 'src/core/application/services/censo/processCenso.service';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';
import { ProcessCensoGatewayImpl } from 'src/gateway/censo/processCenso.gateway.impl';
import { ProcessCensoGateway } from 'src/core/application/gateway/censo/processCenso.gateway';
import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { Provider } from '@nestjs/common';
import { EventBus } from '../../socket/abstract/event-bus';
import { DeleteFileService } from 'src/core/application/services/file/deleteFile.service';
import { ChangeCensoStatusService } from 'src/core/application/services/censo/changeCensoStatus.service';
import { DeleteCensoService } from 'src/core/application/services/censo/deleteCenso.service';
import { DeleteCensoGateway } from 'src/core/application/gateway/censo/deleteCenso.gateway';
import { DeleteCensoGatewayImpl } from 'src/gateway/censo/deleteCenso.gateway.impl';
import { ProducerMqttConfig } from '../../amqp/abstract/producer.mqtt.config';

export const censoProviders: Provider[] = [
	{
		provide: 'FindCensoGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
		): FindCensoGatewayImpl => new FindCensoGatewayImpl(censoRepository),
		inject: ['CENSO_REPOSITORY'],
	},
	{
		provide: FindCensoService.FindCensoService,
		useFactory: (
			findCensoGateway: FindCensoGateway,
		): FindCensoService.FindCensoService => {
			return new FindCensoService.FindCensoService(findCensoGateway);
		},
		inject: ['FindCensoGateway'],
	},
	{
		provide: 'FindCensoGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
		): FindCensoGateway => {
			return new FindCensoGatewayImpl(censoRepository);
		},
		inject: ['CENSO_REPOSITORY'],
	},
	{
		provide: 'InsertCensoGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
			findCensoGateway: FindCensoGateway,
		): InsertCensoGateway => {
			return new InsertCensoGatewayImpl(censoRepository, findCensoGateway);
		},
		inject: ['CENSO_REPOSITORY', 'FindCensoGateway'],
	},
	{
		provide: InsertCensoService.InsertCensoService,
		useFactory: (
			insertCensoGateway: InsertCensoGateway,
		): InsertCensoService.InsertCensoService => {
			return new InsertCensoService.InsertCensoService(insertCensoGateway);
		},
		inject: ['InsertCensoGateway'],
	},
	{
		provide: 'ProcessCensoGateway',
		useFactory(producerMqttConfig: ProducerMqttConfig): ProcessCensoGateway {
			return new ProcessCensoGatewayImpl(producerMqttConfig);
		},
		inject: ['ProducerMqttConfig'],
	},
	{
		provide: ProcessCensoService.ProcessCensoService,
		useFactory: (
			downloadFileGateway: DownloadFileGateway,
			processCensoGateway: ProcessCensoGateway,
			deleteFileService: DeleteFileService.DeleteFileService,
			changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		): ProcessCensoService.ProcessCensoService => {
			return new ProcessCensoService.ProcessCensoService(
				downloadFileGateway,
				processCensoGateway,
				deleteFileService,
				changeCensoStatusService,
			);
		},
		inject: [
			'DownloadFileGateway',
			'ProcessCensoGateway',
			DeleteFileService.DeleteFileService,
			ChangeCensoStatusService.ChangeCensoStatusService,
		],
	},
	{
		provide: NotificationCensoService.NotificationCensoService,
		useFactory: (
			eventBus: EventBus,
			changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		): NotificationCensoService.NotificationCensoService => {
			return new NotificationCensoService.NotificationCensoService(
				new Map<string, string>(),
				eventBus,
				changeCensoStatusService,
			);
		},
		inject: ['EventBus', ChangeCensoStatusService.ChangeCensoStatusService],
	},
	{
		provide: DeleteCensoService.DeleteCensoService,
		useFactory: (
			deleteCensoGateway: DeleteCensoGateway,
		): DeleteCensoService.DeleteCensoService => {
			return new DeleteCensoService.DeleteCensoService(deleteCensoGateway);
		},
		inject: ['DeleteCensoGateway'],
	},
	{
		provide: 'DeleteCensoGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
		): DeleteCensoGateway => {
			return new DeleteCensoGatewayImpl(censoRepository);
		},
		inject: ['CENSO_REPOSITORY'],
	},
];
