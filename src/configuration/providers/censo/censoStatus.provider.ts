import { Provider } from '@nestjs/common';
import { ChangeCensoStatusGateway } from 'src/core/application/gateway/censo/changeCensoStatus.gateway';
import { ChangeCensoStatusService } from 'src/core/application/services/censo/changeCensoStatus.service';
import { ChangeCensoStatusGatewayImpl } from 'src/gateway/censo/changeCensoStatus.gateway.impl';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { Repository } from 'typeorm';

export const censoStatusProvider: Provider[] = [
	{
		provide: ChangeCensoStatusService.ChangeCensoStatusService,
		useFactory: (
			changeCensoStatusGateway: ChangeCensoStatusGateway,
		): ChangeCensoStatusService.ChangeCensoStatusService => {
			return new ChangeCensoStatusService.ChangeCensoStatusService(
				changeCensoStatusGateway,
			);
		},
		inject: ['ChangeCensoStatusGateway'],
	},
	{
		provide: 'ChangeCensoStatusGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
		): ChangeCensoStatusGateway => {
			return new ChangeCensoStatusGatewayImpl(censoRepository);
		},
		inject: ['CENSO_REPOSITORY'],
	},
];
