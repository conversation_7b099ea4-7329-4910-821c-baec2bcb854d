import { Provider } from '@nestjs/common';
import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { EventBus } from '../socket/abstract/event-bus';
import { ChangeCensoStatusService } from 'src/core/application/services/censo/changeCensoStatus.service';
import { ChangeCensoStatusGateway } from 'src/core/application/gateway/censo/changeCensoStatus.gateway';
import { Repository } from 'typeorm';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { ChangeCensoStatusGatewayImpl } from 'src/gateway/censo/changeCensoStatus.gateway.impl';

export const socketProviders: Provider[] = [
	{
		provide: NotificationCensoService.NotificationCensoService,
		useFactory: (
			eventBus: EventBus,
			changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		): NotificationCensoService.NotificationCensoService => {
			return new NotificationCensoService.NotificationCensoService(
				new Map<string, string>(),
				eventBus,
				changeCensoStatusService,
			);
		},
		inject: ['EventBus', ChangeCensoStatusService.ChangeCensoStatusService],
	},
	{
		provide: ChangeCensoStatusService.ChangeCensoStatusService,
		useFactory: (
			changeCensoStatusGateway: ChangeCensoStatusGateway,
		): ChangeCensoStatusService.ChangeCensoStatusService => {
			return new ChangeCensoStatusService.ChangeCensoStatusService(
				changeCensoStatusGateway,
			);
		},
		inject: ['ChangeCensoStatusGateway'],
	},
	{
		provide: 'ChangeCensoStatusGateway',
		useFactory: (
			censoRepository: Repository<CensoEntity>,
		): ChangeCensoStatusGateway => {
			return new ChangeCensoStatusGatewayImpl(censoRepository);
		},
		inject: ['CENSO_REPOSITORY'],
	},
];
