import { Provider } from '@nestjs/common';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { FindPatientGatewayImpl } from 'src/gateway/patient/findPatient.gateway.impl';
import { InsertPatientGatewayImpl } from 'src/gateway/patient/insertPatient.gateway.impl';
import { InsertHospitalizationGatewayImpl } from 'src/gateway/hospitalization/insertHospitalization.gateway.impl';
import { Repository } from 'typeorm';

export const patientProvider: Provider[] = [
	{
		provide: 'findPatientGateway',
		useFactory: (
			patientRepository: Repository<PatientEntity>,
		): FindPatientGateway => new FindPatientGatewayImpl(patientRepository),
		inject: ['PATIENT_REPOSITORY'],
	},
	{
		provide: 'InsertPatientGateway',
		useFactory: (
			patientRepository: Repository<PatientEntity>,
		): InsertPatientGateway => {
			return new InsertPatientGatewayImpl(patientRepository);
		},
		inject: ['PATIENT_REPOSITORY'],
	},
	{
		provide: 'InsertHospitalizationGateway',
		useFactory: (
			hospitalizationRepository: Repository<HospitalizationEntity>,
		): InsertHospitalizationGateway => {
			return new InsertHospitalizationGatewayImpl(hospitalizationRepository);
		},
		inject: ['HOSPITALIZATION_REPOSITORY'],
	},
	{
		provide: FindPatientService.name,
		useFactory: (
			findPatientGateway: FindPatientGateway,
		): FindPatientService => {
			return new FindPatientService(findPatientGateway);
		},
		inject: ['findPatientGateway'],
	},
];
