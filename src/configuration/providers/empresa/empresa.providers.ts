import { Provider } from '@nestjs/common';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { EmpresaEntity } from '../../../gateway/entities/empresa.entity';
import { FindEmpresaGateway } from '../../../core/application/gateway/empresa/findEmpresa.gateway';
import { FindEmpresaGatewayImpl } from '../../../gateway/empresa/findEmpresa.gateway.impl';
import { FindEmpresaService } from '../../../core/application/services/empresa/findEmpresa.service';

export const empresaProviders: Provider[] = [
	{
		provide: 'EMPRESA_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(EmpresaEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindEmpresaGateway',
		useFactory: (
			findEmpresaRepository: Repository<EmpresaEntity>,
		): FindEmpresaGatewayImpl =>
			new FindEmpresaGatewayImpl(findEmpresaRepository),
		inject: ['EMPRESA_REPOSITORY'],
	},
	{
		provide: FindEmpresaService.name,
		useFactory: (
			findEmpresaGateway: FindEmpresaGateway,
		): FindEmpresaService => {
			return new FindEmpresaService(findEmpresaGateway);
		},
		inject: ['FindEmpresaGateway'],
	},
];
