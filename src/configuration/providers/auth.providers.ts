import { JwtService } from '@nestjs/jwt';
import { LoginService } from 'src/core/application/services/auth/login.service';
import { ValidateUserService } from 'src/core/application/services/auth/validateUser.service';
import { LoginGateway } from 'src/core/application/gateway/auth/login.gateway';
import { ValidateUserGateway } from 'src/core/application/gateway/auth/validateUser.gateway';
import { FindUserGateway } from 'src/core/application/gateway/user/findUser.gateway';
import { LoginGatewayImpl } from 'src/gateway/auth/login.gateway.Impl';
import { ValidateUserGatewayImpl } from 'src/gateway/auth/validateUser.gateway.Impl';

export const authProviders = [
	{
		provide: 'LoginGateway',
		useFactory: (jwtService: JwtService): LoginGatewayImpl =>
			new LoginGatewayImpl(jwtService),
		inject: [JwtService],
	},
	{
		provide: LoginService.LoginService,
		useFactory: (loginGateway: LoginGateway): LoginService.LoginService => {
			return new LoginService.LoginService(loginGateway);
		},
		inject: ['LoginGateway'],
	},
	{
		provide: 'ValidateUserGateway',
		useFactory: (findUserGateway: FindUserGateway): ValidateUserGatewayImpl =>
			new ValidateUserGatewayImpl(findUserGateway),
		inject: ['FindUserGateway'],
	},
	{
		provide: ValidateUserService.ValidateUserService,
		useFactory: (
			validateGateway: ValidateUserGateway,
		): ValidateUserService.ValidateUserService => {
			return new ValidateUserService.ValidateUserService(validateGateway);
		},
		inject: ['ValidateUserGateway'],
	},
];
