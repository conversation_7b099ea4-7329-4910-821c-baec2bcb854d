import { Provider } from '@nestjs/common';
import { FindCompanyConfigGateway } from 'src/core/application/gateway/companyConfig/findCompanyConfig.gateway';
import { FindCompanyConfigService } from 'src/core/application/services/companyConfig/findCompanyConfig.service';
import { FindCompanyConfigGatewayImpl } from 'src/gateway/companyConfig/findCompanyConfig.gateway.impl';
import { CompanyConfigEntity } from 'src/gateway/entities/companyConfig.entity';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';

export const companyConfigProvider: Provider[] = [
	{
		provide: 'COMPANY_CONFIG_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CompanyConfigEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindCompanyConfigGateway',
		useFactory: (
			companyConfigRepository: Repository<CompanyConfigEntity>,
		): FindCompanyConfigGateway =>
			new FindCompanyConfigGatewayImpl(companyConfigRepository),
		inject: ['COMPANY_CONFIG_REPOSITORY'],
	},
	{
		provide: FindCompanyConfigService.name,
		useFactory: (
			findCompanyConfigGateway: FindCompanyConfigGateway,
		): FindCompanyConfigService =>
			new FindCompanyConfigService(findCompanyConfigGateway),
		inject: ['FindCompanyConfigGateway'],
	},
];
