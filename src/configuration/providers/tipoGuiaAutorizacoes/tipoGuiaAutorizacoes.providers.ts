import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { FindTipoGuiaAutorizacoesService } from 'src/core/application/services/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes.service';
import { FindTipoGuiaAutorizacoesGateway } from 'src/core/application/gateway/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoesEntity } from 'src/gateway/entities/tipoGuiaAutorizacoes.entity';
import { FindTipoGuiaAutorizacoesGatewayImpl } from 'src/gateway/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes.gateway.impl';
import { CriptografiaService } from 'src/core/application/services/criptografia/criptografia.service';

export const tipoGuiaAutorizacoesProviders = [
	{
		provide: 'TIPO_GUIA_AUTORIZACOES_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(TipoGuiaAutorizacoesEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindTipoGuiaAutorizacoesGateway',
		useFactory: (
			findTipoGuiaAutorizacoesRepository: Repository<TipoGuiaAutorizacoesEntity>,
		): FindTipoGuiaAutorizacoesGatewayImpl =>
			new FindTipoGuiaAutorizacoesGatewayImpl(
				findTipoGuiaAutorizacoesRepository,
			),
		inject: ['TIPO_GUIA_AUTORIZACOES_REPOSITORY'],
	},
	{
		provide: FindTipoGuiaAutorizacoesService,
		useFactory: (
			findTipoGuiaAutorizacoesGateway: FindTipoGuiaAutorizacoesGateway,
			criptografiaService: CriptografiaService,
		): FindTipoGuiaAutorizacoesService => {
			return new FindTipoGuiaAutorizacoesService(
				findTipoGuiaAutorizacoesGateway,
				criptografiaService,
			);
		},
		inject: ['FindTipoGuiaAutorizacoesGateway', CriptografiaService.name],
	},
];
