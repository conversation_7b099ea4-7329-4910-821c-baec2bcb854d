import { Provider } from '@nestjs/common';
import { InsertGuideGateway } from 'src/core/application/gateway/guide/insertGuide.gateway';
import { InsertGuideService } from 'src/core/application/services/guide/insertGuide.service';
import { GuideEntity } from 'src/gateway/entities/guide.entity';
import { InsertGuideGatewayImpl } from 'src/gateway/guide/insertGuide.gateway.impl';
import { Repository } from 'typeorm';

export const guideProviders: Provider[] = [
	{
		provide: 'InsertGuideGateway',
		useFactory: (
			guideRepository: Repository<GuideEntity>,
		): InsertGuideGateway => new InsertGuideGatewayImpl(guideRepository),
		inject: ['GUIDE_REPOSITORY'],
	},
	{
		provide: 'InsertGuideService',
		useFactory: (insertGuideGateway: InsertGuideGateway): InsertGuideService =>
			new InsertGuideService(insertGuideGateway),
		inject: ['InsertGuideGateway'],
	},
];
