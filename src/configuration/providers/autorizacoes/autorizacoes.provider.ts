import { Provider } from '@nestjs/common';
import { FindAutorizacoesGateway } from 'src/core/application/gateway/autorizacoes/findAutorizacoes.gateway';
import { FindAutorizacoesService } from 'src/core/application/services/autorizacoes/findAutorizacoes.service';
import { FindAutorizacoesGatewayImpl } from 'src/gateway/autorizacoes/findAutorizacoes.gateway.impl';
import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';
import { Repository } from 'typeorm';

export const autorizacoesProvider: Provider[] = [
	{
		provide: 'AutorizacoesGateway',
		useFactory: (
			autorizacoesRepository: Repository<AutorizacoesEntity>,
		): FindAutorizacoesGatewayImpl =>
			new FindAutorizacoesGatewayImpl(autorizacoesRepository),
		inject: ['AUTORIZACOES_REPOSITORY'],
	},
	{
		provide: 'FindAutorizacoesService',
		useFactory: (
			findAutorizacoesGateway: FindAutorizacoesGateway,
		): FindAutorizacoesService =>
			new FindAutorizacoesService(findAutorizacoesGateway),
		inject: ['AutorizacoesGateway'],
	},
];
