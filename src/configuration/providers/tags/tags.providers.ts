import { DataSource, ObjectLiteral, Repository } from 'typeorm';
import { TagsEntity } from 'src/gateway/entities/tags.entity';
import { FindTagsGatewayImpl } from 'src/gateway/tags/findTags.gateway.impl';
import { FindTagsService } from 'src/core/application/services/tags/findTags.service';
import { FindTagsGateway } from 'src/core/application/gateway/tags/findTags.gateway';

export const tagsProviders = [
	{
		provide: 'TAGS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(TagsEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FindTagsGateway',
		useFactory: (tagsRepository: Repository<TagsEntity>): FindTagsGatewayImpl =>
			new FindTagsGatewayImpl(tagsRepository),
		inject: ['TAGS_REPOSITORY'],
	},
	{
		provide: FindTagsService,
		useFactory: (findTagsGateway: FindTagsGateway): FindTagsService => {
			return new FindTagsService(findTagsGateway);
		},
		inject: ['FindTagsGateway'],
	},
];
