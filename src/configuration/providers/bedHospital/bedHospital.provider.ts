import { Provider } from '@nestjs/common';
import { FindBedHospitalGateway } from 'src/core/application/gateway/bedHospital/findBedHospital.gateway';
import { InsertBedHospitalGateway } from 'src/core/application/gateway/bedHospital/insertBedHospital.gateway';
import { UpdateBedHospitalGateway } from 'src/core/application/gateway/bedHospital/updateBedHospital.gateway';
import { FindBedHospitalService } from 'src/core/application/services/bedHospital/findBedHospital.service';
import { InsertBedHospitalService } from 'src/core/application/services/bedHospital/insertBedHospital.service';
import { UpdateBedHospitalService } from 'src/core/application/services/bedHospital/updateBedHospital.service';
import { FindBedHospitalGatewayImpl } from 'src/gateway/bedHospital/findBedHospital.gateway.impl';
import { InsertBedHospitalGatewayImpl } from 'src/gateway/bedHospital/insertBedHospital.gateway.impl';
import { UpdateBedHospitalGatewayImpl } from 'src/gateway/bedHospital/updateBedHospital.gateway.impl';
import { BedHospitalEntity } from 'src/gateway/entities/bedHospital.entity';
import { Repository } from 'typeorm';

export const bedHospitalProvider: Provider[] = [
	{
		provide: 'InsertBedHospitalGateway',
		useFactory: (
			bedHospitalRepository: Repository<BedHospitalEntity>,
		): InsertBedHospitalGateway =>
			new InsertBedHospitalGatewayImpl(bedHospitalRepository),
		inject: ['BED_HOSPITAL_RESPOSITORY'],
	},
	{
		provide: 'InsertBedHospitalService',
		useFactory: (
			insertBedHospitalGateway: InsertBedHospitalGateway,
		): InsertBedHospitalService =>
			new InsertBedHospitalService(insertBedHospitalGateway),
		inject: ['InsertBedHospitalGateway'],
	},
	{
		provide: 'FindBedHospitalGateway',
		useFactory: (
			bedHospitalRepository: Repository<BedHospitalEntity>,
		): FindBedHospitalGateway =>
			new FindBedHospitalGatewayImpl(bedHospitalRepository),
		inject: ['BED_HOSPITAL_RESPOSITORY'],
	},
	{
		provide: FindBedHospitalService.name,
		useFactory: (
			findBedHospitalGateway: FindBedHospitalGateway,
		): FindBedHospitalService =>
			new FindBedHospitalService(findBedHospitalGateway),
		inject: ['FindBedHospitalGateway'],
	},

	{
		provide: 'UpdateBedHospitalGateway',
		useFactory: (
			bedHospitalRepository: Repository<BedHospitalEntity>,
		): UpdateBedHospitalGateway =>
			new UpdateBedHospitalGatewayImpl(bedHospitalRepository),
		inject: ['BED_HOSPITAL_RESPOSITORY'],
	},
	{
		provide: UpdateBedHospitalService.name,
		useFactory: (
			updateBedHospitalGateway: UpdateBedHospitalGateway,
		): UpdateBedHospitalService =>
			new UpdateBedHospitalService(updateBedHospitalGateway),
		inject: ['UpdateBedHospitalGateway'],
	},
];
