import { Provider } from '@nestjs/common';
import { EventBus } from '../socket/abstract/event-bus';
import { EventBusImpl } from '../socket/event-bus.impl';
import { DataSource, ObjectLiteral } from 'typeorm';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';
import { BedHospitalEntity } from 'src/gateway/entities/bedHospital.entity';
import { FormCidsEntity } from 'src/gateway/entities/formCids.entity';
import { GuideEntity } from 'src/gateway/entities/guide.entity';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { GuideStatusEntity } from 'src/gateway/entities/guideStatus.entity';
import { CnesMunicipioOldEntity } from 'src/gateway/entities/cnesMunicipioOld.entity';
import { CityEntity } from 'src/gateway/entities/city.entity';
import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';

export const infraProviders: Provider[] = [
	{
		provide: 'CENSO_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CensoEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'HOSPITALIZATION_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(HospitalizationEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'PATIENT_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(PatientEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'HOSPITAL_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(HospitalEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'HOSPITAL_COMPANY_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(HospitalsCompanyEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'BED_HOSPITAL_RESPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(BedHospitalEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'FORM_CIDS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(FormCidsEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'GUIDE_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(GuideEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'GUIDE_DAILY_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(GuideDailyEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'CNES_MUNICIPIO_OLD_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CnesMunicipioOldEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'CITY_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(CityEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'GUIDE_STATUS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(GuideStatusEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'AUTORIZACOES_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(AutorizacoesEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: 'EventBus',
		useFactory: (): EventBus => new EventBusImpl(),
	},
];
