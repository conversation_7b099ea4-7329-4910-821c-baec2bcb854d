import { Provider } from '@nestjs/common';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';
import { DownloadModeloCensoService } from 'src/core/application/services/file/download/censo/downloadModeloCenso.service';
import { DownloadFileGatewayImpl } from 'src/gateway/file/download/downloadFile.gateway.impl';
import { GoogleStorageWrapper } from '../apis/gcs.api.config';
import { GenerateModelCensoImpl } from 'src/core/application/gateway/file/download/factory/generateModel.factory.impl';
import { ModelTemplateFactory } from 'src/core/application/gateway/file/download/factory/abstract/modelTemplate.factory';

export const downloadProviders: Provider[] = [
	{
		provide: 'DownloadFileGateway',
		useFactory: (): DownloadFileGatewayImpl => {
			return new DownloadFileGatewayImpl(new GoogleStorageWrapper());
		},
	},
	{
		provide: 'ModelTemplateFactory',
		useFactory: (): GenerateModelCensoImpl => {
			return new GenerateModelCensoImpl();
		},
	},
	{
		provide: DownloadModeloCensoService,
		useFactory: (
			downloadFileGateway: DownloadFileGateway,
			modelTemplateFactory: ModelTemplateFactory,
		): DownloadModeloCensoService => {
			return new DownloadModeloCensoService(
				downloadFileGateway,
				modelTemplateFactory,
			);
		},
		inject: ['DownloadFileGateway', 'ModelTemplateFactory'],
	},
];
