import { Provider } from '@nestjs/common';
import { FindPermissionsGateway } from 'src/core/application/gateway/permissions/findPermissions.gateway';
import { FindPermissionsService } from 'src/core/application/services/permissions/findPermissions.service';
import { PermissionsEntity } from 'src/gateway/entities/permissions.entity';
import { FindPermissionsGatewayImpl } from 'src/gateway/permissions/findPermissions.gateway.impl';
import { DataSource, ObjectLiteral, Repository } from 'typeorm';

export const permissionsProvider: Provider[] = [
	{
		provide: 'PERMISSIONS_REPOSITORY',
		useFactory: (dataSource: DataSource): ObjectLiteral =>
			dataSource.getRepository(PermissionsEntity),
		inject: ['DATA_SOURCE'],
	},
	{
		provide: FindPermissionsGatewayImpl.name,
		useFactory: (
			permissionsRepository: Repository<PermissionsEntity>,
		): FindPermissionsGateway =>
			new FindPermissionsGatewayImpl(permissionsRepository),
		inject: ['PERMISSIONS_REPOSITORY'],
	},
	{
		provide: FindPermissionsService.name,
		useFactory: (
			findPermissionsGateway: FindPermissionsGateway,
		): FindPermissionsService =>
			new FindPermissionsService(findPermissionsGateway),
		inject: [FindPermissionsGatewayImpl.name],
	},
];
