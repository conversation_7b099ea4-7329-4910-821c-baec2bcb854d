import { File } from 'src/core/domain/File';
import { StorageApiWrapper } from './abstract/storageApiWrapper';
import { Storage } from '@google-cloud/storage';
import { FailStoreObject } from 'src/shared/exceptions/upload/FailStoreObject.exception';
import { RetriveObjectException } from 'src/shared/exceptions/upload/RetrieveObject.exception';
import { HttpStatus } from '@nestjs/common';
import * as fs from 'fs';
import path from 'path';

export class GoogleStorageWrapper implements StorageApiWrapper {
	constructor(
		private readonly storage: Storage = (this.storage = new Storage({
			projectId: process.env.CAREFY_GCP_PROJECT,
			keyFilename: process.env.CAREFY_GCP_KEYFILE,
		})),
		private readonly bucketName = process.env.CAREFY_GCP_BUCKET_NAME,
	) {}

	async insertFile(file: File): Promise<string> {
		let result = null;
		try {
			[result] = await this.storage
				.bucket(this.bucketName)
				.upload(file.caminhoInterno, {
					destination: `company_${file.companyId}/${new Date().getFullYear()}/${new Date().getMonth()}/${Date.now() + '-' + Math.round(Math.random() * 1e9) + file.nome}`,
				});
			return result.name;
		} catch (error) {
			throw new FailStoreObject(
				400,
				'falha ao enviar o inserir o arquivo na nossa base: ' + error,
			);
		}
	}

	async getFile(string: string): Promise<string> {
		try {
			const [result] = await this.storage
				.bucket(this.bucketName)
				.file(string)
				.download();
			const internalPath = 'uploads/' + string;
			const dirPath = path.dirname(internalPath);
			fs.mkdirSync(dirPath, { recursive: true });

			fs.writeFileSync(internalPath, result);
			return internalPath;
		} catch (error) {
			throw new RetriveObjectException(
				HttpStatus.BAD_REQUEST,
				'falha ao recuperar arquivo: ' + error,
			);
		}
	}
}
