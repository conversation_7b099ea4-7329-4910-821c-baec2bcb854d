import {
	ArgumentsHost,
	HttpStatus,
	UnauthorizedException,
} from '@nestjs/common';
import { Request, Response } from 'express';
import { InvalidContentException } from 'src/shared/exceptions/upload/InvalidContent.exception';
import { HttpExceptionFilter } from './httpException.config.filter';

describe('HttpExceptionFilter', () => {
	let httpExceptionFilter: HttpExceptionFilter;
	let mockResponse: Partial<Response>;
	let mockArgumentsHost: Partial<ArgumentsHost>;

	beforeEach(() => {
		httpExceptionFilter = new HttpExceptionFilter();

		mockResponse = {
			status: jest.fn().mockReturnThis(),
			json: jest.fn(),
		};

		const mockRequest = { url: '/test-url' } as unknown as Request;

		mockArgumentsHost = {
			switchToHttp: () => ({
				getResponse: (): Response => mockResponse as Response,
				getRequest: (): Request => mockRequest,
			}),
		} as Partial<ArgumentsHost>;
	});

	it('deve retornar INTERNAL_SERVER_ERROR para erros genéricos', () => {
		const exception = new Error('Erro Genérico');

		httpExceptionFilter.catch(exception, mockArgumentsHost as ArgumentsHost);

		expect(mockResponse.status).toHaveBeenCalledWith(
			HttpStatus.INTERNAL_SERVER_ERROR,
		);
		expect(mockResponse.json).toHaveBeenCalledWith({
			statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
			timestamp: expect.any(String),
			path: '/test-url',
			error: exception.message,
			tagException: exception.name,
		});
	});

	it('deve retornar BAD_REQUEST para InvalidContentException', () => {
		const exception = new InvalidContentException(
			HttpStatus.BAD_REQUEST,
			'Conteúdo do arquivo inválido',
			['data'],
		);

		httpExceptionFilter.catch(exception, mockArgumentsHost as ArgumentsHost);

		expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.BAD_REQUEST);
		expect(mockResponse.json).toHaveBeenCalledWith({
			statusCode: HttpStatus.BAD_REQUEST,
			timestamp: expect.any(String),
			path: '/test-url',
			error: 'Conteúdo do arquivo inválido',
			tagException: exception.tag,
			detail: exception.fields,
		});
	});

	it('deve retornar UNAUTHORIZED para UnauthorizedException', () => {
		const exception = new UnauthorizedException('Não autorizado');

		httpExceptionFilter.catch(exception, mockArgumentsHost as ArgumentsHost);

		expect(mockResponse.status).toHaveBeenCalledWith(HttpStatus.UNAUTHORIZED);
		expect(mockResponse.json).toHaveBeenCalledWith({
			statusCode: HttpStatus.UNAUTHORIZED,
			timestamp: expect.any(String),
			path: '/test-url',
			error: exception.message,
			tagException: exception.name,
		});
	});
});
