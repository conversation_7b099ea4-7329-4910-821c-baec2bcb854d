import { AutorizacoesEntity } from 'src/gateway/entities/autorizacoes.entity';
import { AutorizacoesAuditStatusEntity } from 'src/gateway/entities/autorizacoesAuditStatus.entity';
import { BedHospitalEntity } from 'src/gateway/entities/bedHospital.entity';
import { CensoEntity } from 'src/gateway/entities/censo.entity';
import { CensoDadosEntity } from 'src/gateway/entities/censoDados.entity';
import { CensoStatusEntity } from 'src/gateway/entities/censoStatus.entity';
import { CityEntity } from 'src/gateway/entities/city.entity';
import { CnesMunicipioOldEntity } from 'src/gateway/entities/cnesMunicipioOld.entity';
import { EmpresaEntity } from 'src/gateway/entities/empresa.entity';
import { FormCidsEntity } from 'src/gateway/entities/formCids.entity';
import { GuideEntity } from 'src/gateway/entities/guide.entity';
import { GuideDailyEntity } from 'src/gateway/entities/guideDaily.entity';
import { GuideStatusEntity } from 'src/gateway/entities/guideStatus.entity';
import { HospitalEntity } from 'src/gateway/entities/hospital.entity';
import { HospitalsCompanyEntity } from 'src/gateway/entities/hospitalsCompany.entity';
import { LinhasConflitadasCensoEntity } from 'src/gateway/entities/linhasConflitadasCenso.entity';
import { MotivoNegativaEntity } from 'src/gateway/entities/motivoNegativa.entity';
import { OperadoraEntity } from 'src/gateway/entities/operadora.entity';
import { PatientEntity } from 'src/gateway/entities/patient.entity';
import { HospitalizationEntity } from 'src/gateway/entities/hospitalization.entity';
import { PendenciasEntity } from 'src/gateway/entities/pendencias.entity';
import { PlanosEntity } from 'src/gateway/entities/planos.entity';
import { TagsEntity } from 'src/gateway/entities/tags.entity';
import { TiposConflitosEntity } from 'src/gateway/entities/tiposConflitos.entity';
import { UserEntity } from 'src/gateway/entities/user.entity';
import { DataSource } from 'typeorm';
import { AutorizacoesAuditEntity } from '../../gateway/entities/autorizacoesAudit.entity';
import { AutorizacoesTagsEntity } from '../../gateway/entities/autorizacoesTags.entity';
import { AutorizacoesItensEntity } from '../../gateway/entities/autorizacoesItens.entity';
import { TipoGuiaAutorizacoesEntity } from 'src/gateway/entities/tipoGuiaAutorizacoes.entity';
import { TipoContratoEntity } from '../../gateway/entities/tipoContrato.entity';
import { CompanyConfigEntity } from 'src/gateway/entities/companyConfig.entity';
import { CompanyConfigFunctionsEntity } from 'src/gateway/entities/companyConfigFunctions.entity';
import { PermissionFunctionEntity } from 'src/gateway/entities/permissionFunction.entity';
import { PermissionsEntity } from 'src/gateway/entities/permissions.entity';

export const databaseProviders = [
	{
		provide: 'DATA_SOURCE',
		useFactory: async (): Promise<DataSource> => {
			const dataSource = new DataSource({
				type: 'mysql',
				host: process.env.DATABASE_HOST,
				port: Number(process.env.DATABASE_PORT),
				username: process.env.DATABASE_USERNAME,
				password: process.env.DATABASE_PASSWORD,
				database: process.env.DATABASE_NAME,
				// logging: true,
				entities: [
					UserEntity,
					CensoEntity,
					CensoStatusEntity,
					OperadoraEntity,
					CensoDadosEntity,
					TiposConflitosEntity,
					HospitalizationEntity,
					PatientEntity,
					LinhasConflitadasCensoEntity,
					HospitalEntity,
					HospitalsCompanyEntity,
					BedHospitalEntity,
					FormCidsEntity,
					GuideEntity,
					GuideDailyEntity,
					GuideStatusEntity,
					CityEntity,
					CnesMunicipioOldEntity,
					AutorizacoesEntity,
					MotivoNegativaEntity,
					PlanosEntity,
					PendenciasEntity,
					EmpresaEntity,
					AutorizacoesAuditStatusEntity,
					TagsEntity,
					AutorizacoesAuditEntity,
					AutorizacoesTagsEntity,
					AutorizacoesItensEntity,
					TipoGuiaAutorizacoesEntity,
					TipoContratoEntity,
					CompanyConfigEntity,
					CompanyConfigFunctionsEntity,
					PermissionFunctionEntity,
					PermissionsEntity,
				],
				//NÃO COLOCAR NUNCA COMO TRUE
				synchronize: false, //NÃO ENCOSTA NISSO PELO AMOR DE DEUS
			});

			return dataSource.initialize();
		},
	},
];
