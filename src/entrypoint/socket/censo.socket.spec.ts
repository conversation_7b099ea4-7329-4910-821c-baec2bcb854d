import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { EventBus } from 'src/configuration/socket/abstract/event-bus';
import { Socket } from 'socket.io';
import { CENSO_LINHA_PROCESSADA_EVENT } from 'src/helpers/events.helper';
import { CensoSocket } from './censo.socket';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';

describe('CensoSocket', () => {
	let censoSocket: CensoSocket;
	let mockEventBus: jest.Mocked<EventBus>;
	let mockNotificationCensoService: jest.Mocked<NotificationCensoService.NotificationCensoService>;
	let mockSocket: jest.Mocked<Socket>;
	let processaCenso: ProcessaCenso;
	const receivedUuid = 'mock-uuid';

	beforeEach(() => {
		mockEventBus = {
			on: jest.fn(),
			emit: jest.fn(),
		};

		mockNotificationCensoService = {
			connect: jest.fn(),
			disconect: jest.fn(),
			sendNotification: jest.fn(),
			clients: new Map(),
		} as unknown as jest.Mocked<NotificationCensoService.NotificationCensoService>;

		mockSocket = {
			handshake: { query: { id: receivedUuid } },
			id: 'client-1',
			emit: jest.fn(),
			to: jest.fn().mockReturnThis(),
		} as unknown as jest.Mocked<Socket>;

		processaCenso = new ProcessaCenso(
			0,
			'/mock/caminho',
			0,
			0,
			'/mock/caminho-interno',
			{ key: 'value' },
			'mock-id-socket',
		);

		censoSocket = new CensoSocket(mockEventBus, mockNotificationCensoService);
		censoSocket['server'] = mockSocket;
	});

	it('deve chamar connect quando handleConnection for chamado', () => {
		const receivedUuid = 'mock-uuid';
		const clientId = 'client-1';

		censoSocket.handleConnection(mockSocket);

		expect(mockNotificationCensoService.connect).toHaveBeenCalledWith(
			receivedUuid,
			clientId,
		);
	});

	it('deve chamar disconect quando handleDisconnect for chamado', () => {
		const clientId = 'client-1';
		censoSocket.handleDisconnect(mockSocket);

		expect(mockNotificationCensoService.disconect).toHaveBeenCalledWith(
			clientId,
		);
	});

	it('deve emitir um evento quando sendNotification for chamado', () => {
		const message = processaCenso;

		mockNotificationCensoService.connect(processaCenso.idSocket, mockSocket.id);

		censoSocket.sendNotification(processaCenso);

		expect(mockSocket.to).toHaveBeenCalledWith(
			mockNotificationCensoService.clients.get(processaCenso.idSocket),
		);

		expect(mockSocket.emit).toHaveBeenCalledWith(
			CENSO_LINHA_PROCESSADA_EVENT,
			message.porcentagem,
		);
	});

	it('deve ouvir o evento CENSO_LINHA_PROCESSADA_EVENT', () => {
		expect(mockEventBus.on).toHaveBeenCalledWith(
			CENSO_LINHA_PROCESSADA_EVENT,
			expect.any(Function),
		);
	});
});
