import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { Socket } from 'socket.io';
import { CENSO_LINHA_PROCESSADA_EVENT } from 'src/helpers/events.helper';
import { EventBus } from 'src/configuration/socket/abstract/event-bus';
import { Inject } from '@nestjs/common';
import {
	OnGatewayConnection,
	OnGatewayDisconnect,
	WebSocketGateway,
	WebSocketServer,
} from '@nestjs/websockets';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';

@WebSocketGateway({ cors: true, namespace: '/censo' })
export class CensoSocket implements OnGatewayConnection, OnGatewayDisconnect {
	@WebSocketServer()
	private server: Socket;

	constructor(
		@Inject('EventBus')
		private readonly eventBus: EventBus,
		@Inject(NotificationCensoService.NotificationCensoService)
		private readonly notificationCensoService: NotificationCensoService.NotificationCensoService,
	) {
		this.eventBus.on(
			CENSO_LINHA_PROCESSADA_EVENT,
			this.sendNotification.bind(this),
		);
	}

	public handleConnection(client: Socket): void {
		const receivedUuid = client.handshake.query.id as string;
		this.notificationCensoService.connect(receivedUuid, client.id);
	}

	public handleDisconnect(client: Socket): void {
		this.notificationCensoService.disconect(client.id);
	}

	public sendNotification(message: ProcessaCenso): void {
		const client = this.server.to(
			this.notificationCensoService.clients.get(message.idSocket),
		);
		client.emit(CENSO_LINHA_PROCESSADA_EVENT, message.porcentagem);
	}
}
