import { File } from 'src/core/domain/File';

export class UploadFileDto {
	constructor(
		private readonly nome: string,
		private readonly extencao: string,
		private readonly tamanho: number,
		private readonly operadoraId: number,
		private readonly caminhoExterno: string,
		private readonly id?: number,
	) {}

	public static fromFileDomain(file: File, id?: number): UploadFileDto {
		return new UploadFileDto(
			file.nome,
			file.extencao,
			file.tamanho,
			file.companyId,
			file.caminhoExterno,
			id,
		);
	}
}
