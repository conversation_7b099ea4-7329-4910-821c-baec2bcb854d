import { ApiProperty } from '@nestjs/swagger';
import {
	IsOptional,
	IsNumber,
	IsIn,
	ValidateIf,
	IsDateString,
	IsString,
	IsArray,
} from 'class-validator';
import { PeriodoFiltroEnum } from '../../../core/application/enums/autorizacoes/periodoFiltro.enum';

export class FindListagemDto {
	@ApiProperty({
		description: 'Número da página para a paginação',
		required: false,
		example: 1,
	})
	@IsOptional()
	@IsNumber()
	page: number;

	@ApiProperty({
		description: 'Número de itens por página',
		required: false,
		example: 10,
	})
	@IsOptional()
	@IsNumber()
	limit: number;

	@ApiProperty({
		description: 'Período do filtro',
		required: true,
		enum: [
			'pedidosEm',
			'pedidosEsteMes',
			'pedidosHoje',
			'autorizadosEsteMes',
			'autorizadosEm',
			'negadosEm',
		],
		example: 'pedidosHoje',
	})
	@IsOptional()
	@IsIn([
		'pedidosEm',
		'pedidosEsteMes',
		'pedidosHoje',
		'autorizadosEsteMes',
		'autorizadosEm',
		'negadosEm',
	])
	periodo: PeriodoFiltroEnum;

	@ApiProperty({
		description: 'Data de início (obrigatório quando periodo = "em")',
		required: false,
		example: '2024-01-01',
	})
	@ValidateIf(
		(o) =>
			o.periodo === 'autorizadosEm' ||
			o.periodo === 'negadosEm' ||
			o.periodo === 'pedidosEm',
	)
	@IsDateString(
		{},
		{ message: 'dataInicio deve ser uma data válida no formato AAAA-MM-DD' },
	)
	dataInicio: Date;

	@ApiProperty({
		description: 'Data de fim (obrigatório quando periodo = "em")',
		required: false,
		example: '2024-01-31',
	})
	@ValidateIf(
		(o) =>
			o.periodo === 'autorizadosEm' ||
			o.periodo === 'negadosEm' ||
			o.periodo === 'pedidosEm',
	)
	@IsDateString(
		{},
		{ message: 'dataFim deve ser uma data válida no formato AAAA-MM-DD' },
	)
	dataFim: Date;

	@ApiProperty({
		description: 'Prazo limite',
		required: false,
		enum: ['todos', 'prazoLimiteEm'],
		example: 'todos',
	})
	@IsOptional()
	@IsIn(['todos', 'prazoLimiteEm'])
	prazoLimite: 'todos' | 'prazoLimiteEm';

	@ApiProperty({
		description: 'Prazo Inicial (obrigatório quando periodo = "em")',
		required: false,
		example: '2024-01-01',
	})
	@ValidateIf((o) => o.periodo === 'prazoLimiteEm')
	@IsDateString(
		{},
		{ message: 'prazoInicial deve ser uma data válida no formato AAAA-MM-DD' },
	)
	prazoInicial: Date;

	@ApiProperty({
		description: 'Prazo Final (obrigatório quando periodo = "em")',
		required: false,
		example: '2024-01-31',
	})
	@ValidateIf((o) => o.periodo === 'prazoLimiteEm')
	@IsDateString(
		{},
		{ message: 'prazoFinal deve ser uma data válida no formato AAAA-MM-DD' },
	)
	prazoFinal: Date;

	@ApiProperty({
		description: 'Tags',
		required: false,
		example: [],
	})
	@IsOptional()
	@IsArray({ message: 'Tag deve ser uma lista de string (id)' })
	tags: string[];

	@ApiProperty({
		description: 'Status',
		required: false,
		example: ['autorizado'],
	})
	@IsOptional()
	@IsArray({ message: 'Status deve ser uma lista de string' })
	status: string[];

	@ApiProperty({
		description: 'tipoGuia',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Tipo de guia deve ser uma lista de string' })
	tipoGuia: string[];

	@ApiProperty({
		description: 'sugestaoAcao',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Sugestao de acao deve ser uma lista de string' })
	sugestaoAcao: string[];

	@ApiProperty({
		description: 'Prestador',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Prestador deve ser uma lista de string' })
	prestador: string[];

	@ApiProperty({
		description: 'Unimed',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Unimed deve ser uma lista de string' })
	unimed: string[];

	@ApiProperty({
		description: 'Plano',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Plano deve ser uma lista de string' })
	plano: string[];

	@ApiProperty({
		description: 'Empresa',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Empresa deve ser uma lista de string' })
	empresa: string[];

	@ApiProperty({
		description: 'Caráter',
		required: false,
		example: ['todos'],
	})
	@IsOptional()
	@IsArray({ message: 'Caráter deve ser uma lista de string' })
	carater: string[];

	@ApiProperty({
		description: 'Pendência',
		required: false,
		example: 'todos',
	})
	@IsOptional()
	@IsString({
		message: 'Pendência deve ser um boolean todos, 1 ou 0 ',
	})
	@IsOptional()
	pendencia: string;

	@ApiProperty({
		description: 'Regime',
		required: false,
		example: 'todos',
	})
	@IsOptional()
	@IsString({ message: 'Regime deve ser uma lista de string' })
	regime: string;

	@ApiProperty({
		description: 'Código de item',
		required: false,
		example: '',
	})
	@IsOptional()
	@IsString({ message: 'Código de item deve ser uma string' })
	codigoItem: string;
}
