import { ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';

export class PaginationQueryDto {
	@ApiProperty({
		description: 'Número da página para a paginação',
		required: true,
		example: 1,
	})
	page: number;

	@ApiProperty({
		description: 'Número de itens por página',
		required: true,
		example: 10,
	})
	limit: number;
	@ApiProperty({
		description:
			'Termo de busca para filtrar censos (por nome de operadora, status, etc.)',
		required: false,
		example: 'Minha operadora',
	})
	@IsString()
	@IsOptional()
	search?: string;
}
