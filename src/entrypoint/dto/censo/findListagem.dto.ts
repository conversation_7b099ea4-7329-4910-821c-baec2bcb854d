import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { OrdenacaoEnum } from 'src/core/application/dto/OrdenacaoEnum';
import { Transform } from 'class-transformer';
export class FindListagemDto {
	@ApiProperty({
		description: 'Número da página para a paginação',
		required: false,
		example: 1,
	})
	@IsOptional()
	@Transform(({ value }) => {
		if (value === null || value === undefined) return value;
		const num = Number(value);
		return isNaN(num) ? value : num;
	})
	@IsNumber()
	page: number;

	@ApiProperty({
		description: 'Número de itens por página',
		required: false,
		example: 10,
	})
	@IsOptional()
	@Transform(({ value }) => {
		if (value === null || value === undefined) return value;
		const num = Number(value);
		return isNaN(num) ? value : num;
	})
	@IsNumber()
	limit: number;

	@ApiProperty({
		description:
			'Termo de busca para filtrar censos (por nome de operadora, status, etc.)',
		required: false,
		example: 'Minha operadora',
	})
	@IsOptional()
	@IsString()
	search?: string;

	@ApiProperty({
		description: 'Ordenação data de envio',
		required: false,
		example: 'desc',
		enum: OrdenacaoEnum,
	})
	@IsOptional()
	dataEnvio: OrdenacaoEnum;

	@ApiProperty({
		description: 'Ordenação total de linhas',
		required: false,
		example: 'desc',
		enum: OrdenacaoEnum,
	})
	@IsOptional()
	totalLinhas: OrdenacaoEnum;

	@ApiProperty({
		description: 'Ordenação adicionado por',
		required: false,
		example: 'desc',
		enum: OrdenacaoEnum,
	})
	@IsOptional()
	adicionadoPor: OrdenacaoEnum;

	@ApiProperty({
		description: 'Ordenação status',
		required: false,
		example: 'desc',
		enum: OrdenacaoEnum,
	})
	@IsOptional()
	status: OrdenacaoEnum;
}
