import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class searchTipoGuiaAutorizacoes {
	@ApiProperty({
		description: 'Nome do tipo da guia',
		required: false,
		example: 'internacao',
	})
	@IsOptional()
	@IsString()
	name: string;

	@ApiProperty({
		description: 'Limite de retorno',
		required: false,
		example: 10,
	})
	@Transform(({ value }) => {
		if (value === null || value === undefined) return value;
		const num = Number(value);
		return isNaN(num) ? value : num;
	})
	@IsNumber()
	limit: number;
}
