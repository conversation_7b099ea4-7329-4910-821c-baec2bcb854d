import { ApiProperty } from '@nestjs/swagger';
import { IsNumber, IsOptional, IsString } from 'class-validator';
import { Transform } from 'class-transformer';

export class search {
	@ApiProperty({
		description: 'Nome',
		required: false,
	})
	@IsOptional()
	@IsString()
	nome: string;

	@ApiProperty({
		description: 'Limite de retorno',
		required: true,
		example: 10,
	})
	@Transform(({ value }) => {
		if (value === null || value === undefined) return value;
		const num = Number(value);
		return isNaN(num) ? value : num;
	})
	@IsNumber()
	limit: number;
}
