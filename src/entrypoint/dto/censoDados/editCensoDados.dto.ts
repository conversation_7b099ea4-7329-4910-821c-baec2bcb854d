import { ApiProperty } from '@nestjs/swagger';
import { CensoDados } from 'src/core/domain/CensoDados';

export class CensoDadosDto {
	constructor(censoDados: CensoDados[]) {
		this.censoDados = censoDados;
	}

	@ApiProperty({
		name: 'censoDados',
		example: [
			{
				id: 0,
				censoId: 0,
				companyId: 0,
				userId: 0,
				dataCriacao: '0000-01-01T00:00:00.000Z',
				conflito: 0,
				data: '0000-01-01T00:00:00.000Z',
				municipio: 'string',
				hospitalCredenciado: 'string',
				controle: 'string',
				dtNascimento: '0000-01-01T00:00:00.000Z',
				dataInternacao: '0000-01-01T00:00:00.000Z',
				dataAlta: '0000-01-01T00:00:00.000Z',
				motivoAlta: 'string',
				diagnostico: 'string',
				diagnosticoSecundario: 'string',
				previsaoAlta: '0000-01-01T00:00:00.000Z',
				caraterInternacao: 'string',
				tipoInternacao: 'string',
				codigoGuia: 'string',
				altoCustoStatus: 'string',
				nomeBeneficiario: 'string',
				codBeneficiario: 'string',
				cidadeBeneficiario: 'string',
				estadoBeneficiario: 'string',
				recemNascido: false,
				tipoCliente: 'string',
				valorDiaria: '0.00',
				regionalBeneficiario: 'string',
				tipoControle: 'string',
				diariasAutorizadas: 0,
				codigoHospital: 'string',
				codigoPlano: 'string',
				nomePlano: 'string',
				codigoEmpresa: 'string',
				nomeEmpresa: 'string',
				statusPlano: 'string',
				dataPlanoDesde: '0000-01-01T00:00:00.000Z',
			},
		],
	})
	public readonly censoDados: CensoDados[];
}
