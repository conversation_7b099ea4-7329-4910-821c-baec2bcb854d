import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty } from 'class-validator';

export class FindCensoDataGridDto {
	@ApiProperty({
		description: 'ID do censo para filtrar os dados do censo',
		required: true,
		example: 1,
	})
	@IsNotEmpty()
	censoId: number;

	@ApiProperty({
		description: 'Filtrar linhas com conflito do tipo erro',
		required: true,
		example: true,
	})
	@IsBoolean()
	onlyError: boolean;

	@ApiProperty({
		description: 'Filtrar linhas com conflito do tipo aviso',
		required: true,
		example: true,
	})
	@IsBoolean()
	onlyWarning: boolean;
}
