import amqp from 'amqplib';

export class MessageConsumerDto implements amqp.Message {
	public readonly content: Buffer;
	public readonly fields: amqp.MessageFields;
	public readonly properties: amqp.MessageProperties;

	constructor(
		content: Buffer,
		fields: amqp.MessageFields,
		properties: amqp.MessageProperties,
	) {
		this.content = content;
		this.fields = fields;
		this.properties = properties;
	}
}
