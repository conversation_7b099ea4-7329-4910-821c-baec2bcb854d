import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';
import { NotificationCensoService } from '../../core/application/services/censo/notificationCenso.service';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { CensoDadosMapper } from 'src/shared/mapper/censoDados/censoDados.mapper';
import { InsereCensoDados } from 'src/core/application/dto/InsereCensoDados';
import { Consumer } from './abstract/consumer';
import { MessageConsumerDto } from './dto/message.consumer.dto';
import { Channel } from 'amqplib';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';

export class CensoConsumer extends Consumer {
	constructor(
		private readonly findPatientService: FindPatientService,
		private readonly notificationCensoService: NotificationCensoService.NotificationCensoService,
		private readonly validationPolicyFactory: ValidationPolicyFactory,
		private readonly insertCensoDadosService: InsertCensoDadosService.InsertCensoDadosService,
		channel: Channel,
	) {
		super(channel);
	}

	public async handleMessage(message: MessageConsumerDto): Promise<void> {
		try {
			const processaCenso = JSON.parse(
				message.content.toString(),
			) as ProcessaCenso;
			const censoDados = CensoDadosMapper.toCensoDadosFromLinhaCenso(
				processaCenso.companyId,
				processaCenso.linhaCenso,
			);
			censoDados.companyId = processaCenso.companyId;
			censoDados.censoId = processaCenso.censoId;
			censoDados.userId = processaCenso.userId;

			const patient = await this.findPatientService.findPatientByCodBeneficiary(
				censoDados.codBeneficiario,
				censoDados.companyId,
			);
			if (patient) {
				censoDados.patientId = patient.id;
			}

			const validationPolicy =
				this.validationPolicyFactory.createConflictValidationFactory(
					censoDados,
				);

			const tiposConflitos = await validationPolicy.validate();
			const insereDados = new InsereCensoDados(censoDados, tiposConflitos);

			await this.insertCensoDadosService.insert(insereDados);
			this.notificationCensoService.sendNotification(processaCenso);
			super.handleMessage(message);
		} catch (error) {
			console.error(error);
			this.channel.nack(message);
		}
	}
}
