import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { CensoConsumer } from './censo.consumer';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { ConflictValidationPolicy } from 'src/core/application/validations/policy/abstract/conflict.validation.policy';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { CensoDadosMapper } from 'src/shared/mapper/censoDados/censoDados.mapper';
import { CensoDados } from 'src/core/domain/CensoDados';
import amqp from 'amqplib';
import { MessageConsumerDto } from './dto/message.consumer.dto';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';
import { Patient } from 'src/core/domain/Patient';
import Operadoras from 'src/shared/operadoras.enum';

describe('CensoConsumer', () => {
	let notificationCensoServiceMock: jest.Mocked<NotificationCensoService.NotificationCensoService>;
	let validationPolicyFactoryMock: jest.Mocked<ValidationPolicyFactory>;
	let conflictValidationPolicyMock: jest.Mocked<ConflictValidationPolicy>;
	let insertCensoDadosServiceMock: jest.Mocked<InsertCensoDadosService.InsertCensoDadosService>;
	let channel: jest.Mocked<amqp.Channel>;
	let censoConsumer: CensoConsumer;
	let findPatientServiceMock: jest.Mocked<FindPatientService>;

	beforeEach(() => {
		notificationCensoServiceMock = {
			sendNotification: jest.fn(),
			connect: jest.fn(),
			disconect: jest.fn(),
		} as unknown as jest.Mocked<NotificationCensoService.NotificationCensoService>;

		conflictValidationPolicyMock = {
			validate: jest.fn(),
		} as jest.Mocked<ConflictValidationPolicy>;

		validationPolicyFactoryMock = {
			createConflictValidationFactory: jest
				.fn()
				.mockReturnValue(conflictValidationPolicyMock),
			createUploadValidationFactory: jest.fn(),
		};

		insertCensoDadosServiceMock = {
			insert: jest.fn(),
		} as unknown as jest.Mocked<InsertCensoDadosService.InsertCensoDadosService>;

		channel = {
			ack: jest.fn(),
			nack: jest.fn(),
		} as unknown as jest.Mocked<amqp.Channel>;

		findPatientServiceMock = {
			findPatientByCodBeneficiary: jest.fn(),
			findPatientByCodBeneficiaryAndNotName: jest.fn(),
		} as unknown as jest.Mocked<FindPatientService>;

		censoConsumer = new CensoConsumer(
			findPatientServiceMock,
			notificationCensoServiceMock,
			validationPolicyFactoryMock,
			insertCensoDadosServiceMock,
			channel,
		);

		const censoDadosMapper = jest.spyOn(
			CensoDadosMapper,
			'toCensoDadosFromLinhaCenso',
		);
		censoDadosMapper.mockReturnValue({
			companyId: 1,
			censoId: 1,
			userId: 1,
		} as CensoDados);
	});

	it.each([Operadoras.MINHA_OPERADORA, Operadoras.SAO_FRANCISCO])(
		'deve adicionar o patientId no censoDados quando o paciente for encontrado',
		async (companyId) => {
			const mockProperties = {} as amqp.MessageProperties;
			const mockFields = {} as amqp.MessageFields;
			const message = new MessageConsumerDto(
				Buffer.from(
					'{"id":1,"nome":"teste","companyId":1,"censoId":1,"userId":1}',
				),
				mockFields,
				mockProperties,
			);
			const censoDados = CensoDadosMapper.toCensoDadosFromLinhaCenso(
				companyId,
				{
					id: '1',
					nome: 'teste',
					companyId: '1',
					censoId: '1',
					userId: '1',
				},
			);
			findPatientServiceMock.findPatientByCodBeneficiary.mockReturnValue({
				id: 1,
			} as unknown as Promise<Patient>);

			await censoConsumer.handleMessage(message);

			expect(
				findPatientServiceMock.findPatientByCodBeneficiary,
			).toHaveBeenCalled();

			expect(censoDados.patientId).toBe(1);
		},
	);
	it('deve rodar as validacoes e emitir mensagem para os listeners quando a mensagem consumida for processada', async () => {
		const mockProperties = {} as amqp.MessageProperties;
		const mockFields = {} as amqp.MessageFields;
		const message = new MessageConsumerDto(
			Buffer.from(
				'{"id":1,"nome":"teste","companyId":1,"censoId":1,"userId":1}',
			),
			mockFields,
			mockProperties,
		);
		await censoConsumer.handleMessage(message);

		expect(
			validationPolicyFactoryMock.createConflictValidationFactory,
		).toHaveBeenCalledTimes(1);
		expect(
			validationPolicyFactoryMock.createConflictValidationFactory,
		).toHaveReturnedWith(conflictValidationPolicyMock);
		expect(conflictValidationPolicyMock.validate).toHaveBeenCalledTimes(1);
		expect(notificationCensoServiceMock.sendNotification).toHaveBeenCalledTimes(
			1,
		);
		expect(channel.ack).toHaveBeenCalledWith(message);
	});

	it('deve chamar channel.nack quando algum erro ocorrer', async () => {
		conflictValidationPolicyMock.validate.mockRejectedValue(
			new Error('Erro de validação'),
		);

		const mockProperties = {} as amqp.MessageProperties;
		const mockFields = {} as amqp.MessageFields;
		const message = new MessageConsumerDto(
			Buffer.from(
				'{"id":1,"nome":"teste","companyId":1,"censoId":1,"userId":1}',
			),
			mockFields,
			mockProperties,
		);

		await censoConsumer.handleMessage(message);
		expect(channel.nack).toHaveBeenCalledWith(message);
	});
});
