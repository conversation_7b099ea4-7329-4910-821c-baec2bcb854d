import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { InsertCensoDadosService } from 'src/core/application/services/censoDados/insertCensoDados.service';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { ConflictValidationPolicy } from 'src/core/application/validations/policy/abstract/conflict.validation.policy';
import { ConsumerFactory } from './consumer.factory';
import { CENSO_QUEUE } from 'src/shared/queues';
import amqp from 'amqplib';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';

describe('consumer factory', () => {
	let notificationCensoServiceMock: jest.Mocked<NotificationCensoService.NotificationCensoService>;
	let validationPolicyFactoryMock: jest.Mocked<ValidationPolicyFactory>;
	let conflictValidationPolicyMock: jest.Mocked<ConflictValidationPolicy>;
	let insertCensoDadosServiceMock: jest.Mocked<InsertCensoDadosService.InsertCensoDadosService>;
	let channel: jest.Mocked<amqp.Channel>;
	let consumerFactory: ConsumerFactory;
	let findPatientServiceMock: jest.Mocked<FindPatientService>;

	beforeEach(() => {
		notificationCensoServiceMock = {
			sendNotification: jest.fn(),
			connect: jest.fn(),
			disconect: jest.fn(),
		} as unknown as jest.Mocked<NotificationCensoService.NotificationCensoService>;

		conflictValidationPolicyMock = {
			validate: jest.fn(),
		} as jest.Mocked<ConflictValidationPolicy>;

		validationPolicyFactoryMock = {
			createConflictValidationFactory: jest
				.fn()
				.mockReturnValue(conflictValidationPolicyMock),
			createUploadValidationFactory: jest.fn(),
		};

		insertCensoDadosServiceMock = {
			insert: jest.fn(),
		} as unknown as jest.Mocked<InsertCensoDadosService.InsertCensoDadosService>;

		findPatientServiceMock = {
			findPatientByCodBeneficiary: jest.fn(),
			findPatientByCodBeneficiaryAndNotName: jest.fn(),
		} as unknown as jest.Mocked<FindPatientService>;

		consumerFactory = new ConsumerFactory(
			notificationCensoServiceMock,
			validationPolicyFactoryMock,
			insertCensoDadosServiceMock,
			findPatientServiceMock,
		);

		channel = {
			ack: jest.fn(),
		} as unknown as jest.Mocked<amqp.Channel>;
	});

	const consumersNames = [CENSO_QUEUE];

	it.each(consumersNames)(
		'deve criar um consumer valido',
		async (consumerName) => {
			const consumer = consumerFactory.execute(consumerName, channel);

			expect(consumer).toHaveProperty('handleMessage');
		},
	);

	it('deve criar um consumer invalido', () => {
		expect(consumerFactory.execute).toThrow('consumidor nao existente.');
	});
});
