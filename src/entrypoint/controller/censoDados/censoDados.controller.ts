import {
	Body,
	Controller,
	Delete,
	Inject,
	Param,
	Post,
	Put,
	Query,
	Req,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
	ApiBearerAuth,
	ApiBody,
	ApiQuery,
	ApiResponse,
	ApiTags,
} from '@nestjs/swagger';
import { CensoDadosListagem } from 'src/core/application/dto/CensoDadosListagem';
import { EditCensoDadosService } from 'src/core/application/services/censoDados/editCensoDados.service';
import { FindCensoDadosService } from 'src/core/application/services/censoDados/findCensoDados.service';
import { CensoDadosDto } from '../../dto/censoDados/editCensoDados.dto';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { User } from 'src/core/domain/User';
import { ProcessCensoDadosService } from 'src/core/application/services/censoDados/processCensoDados.service';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';
import { DeleteCensoDadosService } from 'src/core/application/services/censoDados/deleteCensoDados.service';
import { DeleteCensoDadosDto } from 'src/entrypoint/dto/censoDados/deleteCensoDados.dto';
import { FindCensoDataGridDto } from 'src/entrypoint/dto/censoDados/findCensoDadosLIstagem.dto';
import { PaginationQueryDto } from 'src/entrypoint/dto/paginationQuery.dto';

@Controller('v1/censoDados')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('censoDados')
export class CensoDadosController {
	constructor(
		@Inject(FindCensoDadosService.FindCensoDadosService)
		private readonly findCensoDadosService: FindCensoDadosService.FindCensoDadosService,
		private readonly editCensoDadosService: EditCensoDadosService.EditCensoDadosService,
		@Inject('ValidationPolicyFactory')
		private readonly validationPolicyFactory: ValidationPolicyFactory,
		private readonly processCensoDadosService: ProcessCensoDadosService.ProcessCensoDadosService,
		@Inject(FindPatientService.name)
		private readonly findPatientService: FindPatientService,
		@Inject(DeleteCensoDadosService.name)
		private readonly deleteCensoDadosService: DeleteCensoDadosService,
	) {}

	@Post('/data-grid')
	@ApiBody({ type: FindCensoDataGridDto })
	@ApiQuery({ type: PaginationQueryDto })
	@ApiResponse({
		status: 200,
		description: 'Lista de dados censo encontrados',
	})
	async findListagem(
		@Body() body: FindCensoDataGridDto,
		@Query() pagination: PaginationQueryDto,
	): Promise<CensoDadosListagem> {
		return await this.findCensoDadosService.findListagem(
			body.censoId,
			{
				onlyError: body.onlyError,
				onlyWarning: body.onlyWarning,
			},
			pagination,
		);
	}

	@Put()
	@ApiBody({ type: CensoDadosDto })
	public async editCensoDados(
		@Body() editCensoDados: CensoDadosDto,
		@Req() req: { user: Partial<User> },
	): Promise<void> {
		for (const dados of editCensoDados.censoDados) {
			dados.companyId = Number(req.user.companyId);
			const patient = await this.findPatientService.findPatientByCodBeneficiary(
				dados.codBeneficiario,
				dados.companyId,
			);
			if (patient) {
				dados.patientId = patient.id;
			}

			const validation =
				this.validationPolicyFactory.createConflictValidationFactory(dados);
			const conflitos = await validation.validate();
			dados.tiposConflitos = conflitos;
		}
		await this.editCensoDadosService.editCensoDados(editCensoDados.censoDados);
	}

	@Post('/:censoId')
	public async processCensoDados(
		@Param('censoId') censoId: number,
	): Promise<void> {
		await this.processCensoDadosService.processCensoDados(censoId);
	}

	@Delete('/batch')
	public async deleteBatch(@Body() body: DeleteCensoDadosDto): Promise<void> {
		await this.deleteCensoDadosService.deleteBatch(body.ids);
	}
}
