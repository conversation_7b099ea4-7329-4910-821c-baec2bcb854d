import { Test, TestingModule } from '@nestjs/testing';
import { CensoDadosController } from './censoDados.controller';
import { FindCensoDadosService } from 'src/core/application/services/censoDados/findCensoDados.service';
import { CensoDadosListagem } from 'src/core/application/dto/CensoDadosListagem';
import { AuthGuard } from '@nestjs/passport';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { EditCensoDadosService } from 'src/core/application/services/censoDados/editCensoDados.service';
import { CensoDadosDto } from '../../dto/censoDados/editCensoDados.dto';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { ConflictValidationPolicy } from 'src/core/application/validations/policy/abstract/conflict.validation.policy';
import { User } from 'src/core/domain/User';
import { ProcessCensoDadosService } from 'src/core/application/services/censoDados/processCensoDados.service';
import { FindPatientService } from 'src/core/application/services/patient/findPatient.service';
import { Patient } from 'src/core/domain/Patient';
import { DeleteCensoDadosService } from 'src/core/application/services/censoDados/deleteCensoDados.service';

const mockCensoDados = new CensoDados(
	1,
	100,
	200,
	1,
	new Date('2023-01-01'),

	0,
	new Date('2023-02-01'),
	'São Paulo',
	'Hospital X',
	'Controle 123',
	new Date('1990-01-01'),
	new Date('2023-01-10'),
	new Date('2023-01-15'),
	'Alta Médica',
	'Diagnóstico Principal',
	'Diagnóstico Secundário',
	new Date('2023-01-20'),
	'Urgente',
	'Clínica',
	'GUIA123',
	'Ativo',
	'José da Silva',
	'**********',
	'São Paulo',
	'SP',
	false,
	'Tipo A',
	0,
	'SP-Regional',
	'Controle Tipo 1',
	10,
	'HOSP123',
	'PLANO123',
	'Plano de Saúde XYZ',
	'EMPRESA123',
	'Empresa Teste',
	'Ativo',
	new Date('2023-01-01'),
	null,
	null,
	[new TipoConflito(1, 'conflito')],
);

const mockCensoDadosListagem = new CensoDadosListagem(1, 10, 10, 10, [
	mockCensoDados,
]);

jest.mock('@nestjs/passport', () => ({
	AuthGuard: jest.fn(() => ({
		canActivate: jest.fn(() => true),
	})),
}));

const pagination = { page: 1, limit: 10, search: '' };

describe('CensoDadosController', () => {
	let censoDadosController: CensoDadosController;
	let findCensoDadosServiceMock: jest.Mocked<FindCensoDadosService.FindCensoDadosService>;
	let editCensoDadosServiceMock: jest.Mocked<EditCensoDadosService.EditCensoDadosService>;
	let validationPolicyFactory: ValidationPolicyFactory;
	let conflictValidationPoilicy: ConflictValidationPolicy;
	let processCensoDadosService: jest.Mocked<ProcessCensoDadosService.ProcessCensoDadosService>;
	let findPatientService: jest.Mocked<FindPatientService>;
	let deleteCensoDadosService: DeleteCensoDadosService;

	beforeEach(async () => {
		findCensoDadosServiceMock = {
			findByCensoId: jest.fn(),
			findListagem: jest.fn(),
		} as unknown as jest.Mocked<FindCensoDadosService.FindCensoDadosService>;

		editCensoDadosServiceMock = {
			editCensoDados: jest.fn(),
		} as unknown as jest.Mocked<EditCensoDadosService.EditCensoDadosService>;

		conflictValidationPoilicy = {
			validate: jest.fn(),
		};

		deleteCensoDadosService = {
			deleteBatch: jest.fn(),
		} as unknown as jest.Mocked<DeleteCensoDadosService>;

		processCensoDadosService = {
			processCensoDados: jest.fn(),
			findCensoDadosService: findCensoDadosServiceMock,
			findPaginatedCensoDados: jest.fn(),
		} as unknown as jest.Mocked<ProcessCensoDadosService.ProcessCensoDadosService>;

		validationPolicyFactory = {
			createConflictValidationFactory: jest
				.fn()
				.mockReturnValueOnce(conflictValidationPoilicy),
			createUploadValidationFactory: jest.fn(),
		};

		findPatientService = {
			findPatientByCodBeneficiary: jest.fn(),
			findPatientByCodBeneficiaryAndNotName: jest.fn(),
		} as unknown as jest.Mocked<FindPatientService>;

		const module: TestingModule = await Test.createTestingModule({
			controllers: [CensoDadosController],
			providers: [
				{
					provide: FindCensoDadosService.FindCensoDadosService,
					useValue: findCensoDadosServiceMock,
				},
				{
					provide: EditCensoDadosService.EditCensoDadosService,
					useValue: editCensoDadosServiceMock,
				},
				{
					provide: 'ValidationPolicyFactory',
					useValue: validationPolicyFactory,
				},
				{
					provide: ProcessCensoDadosService.ProcessCensoDadosService,
					useValue: processCensoDadosService,
				},
				{
					provide: FindPatientService.name,
					useValue: findPatientService,
				},
				{
					provide: DeleteCensoDadosService.name,
					useValue: deleteCensoDadosService,
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({
				canActivate: jest.fn(() => true),
			})
			.compile();

		censoDadosController =
			module.get<CensoDadosController>(CensoDadosController);
	});

	describe('findListagem', () => {
		it('deve chamar findListagem do serviço e retornar CensoDadosListagem sem onlyConflito', async () => {
			const censoId = 100;

			findCensoDadosServiceMock.findListagem.mockResolvedValue(
				mockCensoDadosListagem,
			);

			const result = await censoDadosController.findListagem(
				{
					censoId,
					onlyError: false,
					onlyWarning: false,
				},
				{
					page: pagination.page,
					limit: pagination.limit,
					search: pagination.search,
				},
			);

			expect(findCensoDadosServiceMock.findListagem).toHaveBeenCalledWith(
				censoId,
				{ onlyError: false, onlyWarning: false },
				pagination,
			);

			expect(result).toEqual(mockCensoDadosListagem);
		});

		it('deve chamar findListagem do serviço e retornar CensoDadosListagem com onlyConflito igual a true', async () => {
			const censoId = 100;

			findCensoDadosServiceMock.findListagem.mockResolvedValue(
				mockCensoDadosListagem,
			);

			const result = await censoDadosController.findListagem(
				{
					censoId,
					onlyError: true,
					onlyWarning: false,
				},
				{
					page: pagination.page,
					limit: pagination.limit,
					search: pagination.search,
				},
			);

			expect(findCensoDadosServiceMock.findListagem).toHaveBeenCalledWith(
				censoId,
				{ onlyError: true, onlyWarning: false },
				pagination,
			);

			expect(result).toEqual(mockCensoDadosListagem);
		});

		it('deve chamar findListagem do serviço e retornar CensoDadosListagem com onlyConflito igual a false', async () => {
			const censoId = 100;

			findCensoDadosServiceMock.findListagem.mockResolvedValue(
				mockCensoDadosListagem,
			);

			const result = await censoDadosController.findListagem(
				{
					censoId,
					onlyError: false,
					onlyWarning: false,
				},
				{
					page: pagination.page,
					limit: pagination.limit,
					search: pagination.search,
				},
			);

			expect(findCensoDadosServiceMock.findListagem).toHaveBeenCalledWith(
				censoId,
				{ onlyError: false, onlyWarning: false },
				pagination,
			);

			expect(result).toEqual(mockCensoDadosListagem);
		});

		it('deve retornar CensoDadosListagem com paginação padrão', async () => {
			const censoId = 100;

			findCensoDadosServiceMock.findListagem.mockResolvedValue(
				mockCensoDadosListagem,
			);

			const result = await censoDadosController.findListagem(
				{
					censoId,
					onlyError: false,
					onlyWarning: false,
				},
				{
					page: pagination.page,
					limit: pagination.limit,
					search: pagination.search,
				},
			);

			expect(findCensoDadosServiceMock.findListagem).toHaveBeenCalledWith(
				censoId,
				{ onlyError: false, onlyWarning: false },
				pagination,
			);

			expect(result).toEqual(mockCensoDadosListagem);
		});

		it('deve retornar CensoDadosListagem com paginação padrão', async () => {
			const censoId = 100;

			findCensoDadosServiceMock.findListagem.mockResolvedValue(
				mockCensoDadosListagem,
			);

			const result = await censoDadosController.findListagem(
				{
					censoId,
					onlyError: false,
					onlyWarning: false,
				},
				{
					page: pagination.page,
					limit: pagination.limit,
					search: pagination.search,
				},
			);

			expect(findCensoDadosServiceMock.findListagem).toHaveBeenCalledWith(
				censoId,
				{ onlyError: false, onlyWarning: false },
				pagination,
			);

			expect(result).toEqual(mockCensoDadosListagem);
		});
	});

	describe('editCenso', () => {
		let censoDados: CensoDados[];
		let editCensoDados: CensoDadosDto;
		beforeEach(() => {
			censoDados = [
				new CensoDados(
					1,
					100,
					200,
					1,
					new Date('2023-01-01'),
					1,
					new Date('2023-02-01'),
					'São Paulo',
					'Hospital X',
					'Controle 123',
					new Date('1990-01-01'),
					new Date('2023-01-10'),
					new Date('2023-01-15'),
					'Alta Médica',
					'Diagnóstico Principal',
					'Diagnóstico Secundário',
					new Date('2023-01-20'),
					'Urgente',
					'Clínica',
					'GUIA123',
					'Ativo',
					'José da Silva',
					'**********',
					'São Paulo',
					'SP',
					false,
					'Tipo A',
					0,
					'SP-Regional',
					'Controle Tipo 1',
					10,
					'HOSP123',
					'PLANO123',
					'Plano de Saúde XYZ',
					'EMPRESA123',
					'Empresa Teste',
					'Ativo',
					new Date('2023-01-01'),
					null,
					null,
					[],
				),
			];

			editCensoDados = new CensoDadosDto(censoDados);
		});

		it('deve obter o patientId do paciente', async () => {
			const req = {
				user: {
					email: '',
					enabled: 1,
					id: 1,
					name: 'name',
					password: '',
					companyId: 10,
				} as unknown as Partial<User>,
			};
			findPatientService.findPatientByCodBeneficiary.mockResolvedValue({
				id: 1,
			} as unknown as Patient);
			await censoDadosController.editCensoDados(editCensoDados, req);
			expect(
				findPatientService.findPatientByCodBeneficiary,
			).toHaveBeenCalledWith('**********', 10);
			expect(
				findPatientService.findPatientByCodBeneficiary,
			).toHaveBeenCalledTimes(1);
		});
		it('deve editar os dados de um censo', async () => {
			const req = {
				user: {
					email: '',
					enabled: 1,
					id: 1,
					name: 'name',
					password: '',
					companyId: 10,
				} as unknown as Partial<User>,
			};
			await censoDadosController.editCensoDados(editCensoDados, req);
			expect(editCensoDadosServiceMock.editCensoDados).toHaveBeenCalledWith(
				editCensoDados.censoDados,
			);
			expect(editCensoDadosServiceMock.editCensoDados).toHaveBeenCalledTimes(1);
		});
	});

	describe('processCensoDados', () => {
		it('deve chamar processCensoDados service', async () => {
			await censoDadosController.processCensoDados(1);
			expect(processCensoDadosService.processCensoDados).toHaveBeenCalledWith(
				1,
			);
		});
	});

	describe('processCensoDados', () => {
		it('deve chamar processCensoDados service', async () => {
			await censoDadosController.processCensoDados(1);
			expect(processCensoDadosService.processCensoDados).toHaveBeenCalledWith(
				1,
			);
		});
	});

	describe('processCensoDados', () => {
		it('deve chamar processCensoDados service', async () => {
			await censoDadosController.processCensoDados(1);
			expect(processCensoDadosService.processCensoDados).toHaveBeenCalledWith(
				1,
			);
		});
	});

	describe('deleteBatch', () => {
		it('should call deleteBatch', async () => {
			await censoDadosController.deleteBatch({ ids: [1, 2, 3] });
			expect(deleteCensoDadosService.deleteBatch).toHaveBeenCalledWith([
				1, 2, 3,
			]);
		});
	});
});
