import { Test, TestingModule } from '@nestjs/testing';
import { UsersController } from './users.controller';
import { FindUserService } from '../../../core/application/services/user/findUser.service';
import { UserEntity } from '../../../gateway/entities/user.entity';
import { UserRoleEnum } from 'src/core/application/enums/user/userRole.enum';
import { User } from 'src/core/domain/User';

describe('UsersController', () => {
	let usersController: UsersController;
	let findUserService: jest.Mocked<FindUserService.FindUserService>;

	beforeEach(async () => {
		const mockUserService = {
			findUserById: jest.fn(),
			findUserPermissions: jest.fn(),
		};

		const module: TestingModule = await Test.createTestingModule({
			controllers: [UsersController],
			providers: [
				{
					provide: FindUserService.FindUserService,
					useValue: mockUserService,
				},
			],
		}).compile();

		usersController = module.get<UsersController>(UsersController);
		findUserService = module.get(FindUserService.FindUserService);
	});

	it('should be defined', () => {
		expect(usersController).toBeDefined();
	});

	it('should return an user', async () => {
		const userEntities: UserEntity = {
			id: 1,
			name: 'John Doe',
			email: '<EMAIL>',
			role: UserRoleEnum.ADMIN,
		} as UserEntity;

		findUserService.findUserById.mockResolvedValue(userEntities);

		const result = await usersController.findById(0);

		expect(findUserService.findUserById).toHaveBeenCalledTimes(1);
		expect(result).toEqual(userEntities);
	});

	it('should return an empty result when no users are found', async () => {
		findUserService.findUserById.mockResolvedValue(null);

		const result = await usersController.findById(0);

		expect(findUserService.findUserById).toHaveBeenCalledTimes(1);
		expect(result).toEqual(null);
	});

	it('should return an user permission', async () => {
		const userPermissions = {
			companyConfigs: {
				['teste']: [{ id: 1, value: '1', enabled: true, name: '1' }],
			},
			permissions: {
				['authorizations']: {
					view: true,
					insert: true,
					edit: true,
					delete: true,
				},
			},
		};
		findUserService.findUserPermissions.mockResolvedValue(userPermissions);

		const result = await usersController.findUserPermissions({
			user: new User(1, 'John Doe', '<EMAIL>', '3'),
		});

		expect(findUserService.findUserPermissions).toHaveBeenCalledTimes(1);
		expect(result).toEqual(userPermissions);
	});
});
