import {
	Controller,
	Get,
	Inject,
	Param,
	Request,
	UseGuards,
} from '@nestjs/common';
import { FindUserService } from '../../../core/application/services/user/findUser.service';
import { GetUserDto } from '../../dto/user/get-user.dto';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { UserPermissions } from 'src/core/domain/UserPermissions';
import { User } from 'src/core/domain/User';

@Controller('v1/users')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('users')
export class UsersController {
	constructor(
		@Inject(FindUserService.FindUserService)
		private readonly findUserService: FindUserService.FindUserService,
	) {}

	@Get('permissions')
	findUserPermissions(
		@Request() req: { user: Partial<User> },
	): Promise<UserPermissions> {
		const userId = req.user.id;
		const companyId = req.user.companyId;

		return this.findUserService.findUserPermissions(userId, Number(companyId));
	}

	@Get(':id')
	findById(@Param('id') id: number): Promise<GetUserDto> {
		return this.findUserService.findUserById(id);
	}
}
