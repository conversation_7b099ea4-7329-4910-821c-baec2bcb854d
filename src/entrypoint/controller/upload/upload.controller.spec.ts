import { Test, TestingModule } from '@nestjs/testing';
import { UploadController } from './upload.controller';
import { File } from 'src/core/domain/File';
import { FileRequiredException } from 'src/shared/exceptions/upload/FileRequired.exception';
import { User } from 'src/core/domain/User';
import { UploadCensoService } from 'src/core/application/services/file/upload/censo/uploadCenso.service';

jest.mock('src/helpers/encryptFile.helper', () => ({
	encryptFile: jest.fn().mockResolvedValue('mocked-md5-hash'),
}));
describe('UploadController', () => {
	let controller: UploadController;
	let uploadCensoService: UploadCensoService.UploadCensoService;
	let mockedUser: { user: Partial<User> };

	const companyId = 1;
	const userId = 1;
	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [UploadController],
			providers: [
				{
					provide: UploadCensoService.UploadCensoService,
					useValue: {
						execute: jest.fn(),
					},
				},
			],
		}).compile();

		controller = module.get<UploadController>(UploadController);
		uploadCensoService = module.get<UploadCensoService.UploadCensoService>(
			UploadCensoService.UploadCensoService,
		);
		mockedUser = { user: { id: userId } };
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('uploadFile', () => {
		it('should call uploadCensoService.execute with correct arguments when file is provided', async () => {
			const file = {
				originalname: 'test-file.csv',
				mimetype: 'text/csv',
				size: 1024,
				path: './uploads/test-file.csv',
			} as Express.Multer.File;

			const fileDomain = new File(
				file.originalname,
				file.mimetype,
				file.size,
				file.path,
				companyId,
				userId,
				'mocked-md5-hash',
			);

			await controller.uploadFile(file, companyId, mockedUser);

			expect(uploadCensoService.execute).toHaveBeenCalledWith(fileDomain);
		});

		it('should throw FileRequiredException if file is not provided', async () => {
			await expect(
				controller.uploadFile(null, companyId, mockedUser),
			).rejects.toThrow(FileRequiredException);
			await expect(
				controller.uploadFile(null, companyId, mockedUser),
			).rejects.toThrow('File is required.');
		});
	});
});
