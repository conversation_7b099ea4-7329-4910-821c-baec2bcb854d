import {
	<PERSON>,
	HttpStatus,
	Inject,
	Param,
	Post,
	Req,
	UploadedFile,
	UseGuards,
	UseInterceptors,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags, ApiConsumes, ApiBody } from '@nestjs/swagger';
import { FileInterceptor } from '@nestjs/platform-express';
import { File } from 'src/core/domain/File';
import { diskStorage } from 'multer';
import { UploadFileDto } from '../../dto/upload/UploadFile.dto';
import { generateFilename } from 'src/helpers/generateFilename.helper';
import { encryptFile } from 'src/helpers/encryptFile.helper';
import { FileRequiredException } from 'src/shared/exceptions/upload/FileRequired.exception';
import { User } from 'src/core/domain/User';
import { UploadCensoService } from 'src/core/application/services/file/upload/censo/uploadCenso.service';

@Controller('v1/upload')
@ApiTags('upload')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
export class UploadController {
	constructor(
		@Inject(UploadCensoService.UploadCensoService)
		private uploadCensoService: UploadCensoService.UploadCensoService,
	) {}

	@Post(':companyId')
	@UseInterceptors(
		FileInterceptor('file', {
			storage: diskStorage({
				destination: './uploads',
				filename: (_, file, callback) => {
					/* istanbul ignore next */
					callback(null, generateFilename(file.originalname));
				},
			}),
		}),
	)
	@ApiConsumes('multipart/form-data')
	@ApiBody({
		description: 'Arquivo a ser enviado',
		required: true,
		schema: {
			type: 'object',
			properties: {
				file: {
					type: 'string',
					format: 'binary',
				},
			},
		},
	})
	async uploadFile(
		@UploadedFile() file: Express.Multer.File,
		@Param('companyId') companyId: number,
		@Req() req: { user: Partial<User> },
	): Promise<UploadFileDto> {
		if (!file) {
			throw new FileRequiredException(
				HttpStatus.BAD_REQUEST,
				'File is required.',
			);
		}
		const fileDomain = new File(
			file.originalname,
			file.mimetype,
			file.size,
			file.path,
			companyId,
			req.user.id,
			await encryptFile(file.path),
		);
		const uploadId = await this.uploadCensoService.execute(fileDomain);
		return UploadFileDto.fromFileDomain(fileDomain, uploadId);
	}
}
