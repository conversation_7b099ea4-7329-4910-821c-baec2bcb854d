import {
	Controller,
	Get,
	Inject,
	Query,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from 'src/core/domain/User';
import { searchHospital } from '../../dto/hospital/searchHospital.dto';
import { FindHospitalService } from '../../../core/application/services/hospital/find/findHospital.service';
import { HospitalsCompany } from '../../../core/domain/HospitalsCompany';

@Controller('v1/hospital')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('hospital')
export class HospitalController {
	constructor(
		@Inject(FindHospitalService.name)
		private readonly findHospitalService: FindHospitalService,
	) {}

	@Get('search')
	searchHospital(
		@Request() req: { user: Partial<User> },
		@Query() dto: searchHospital,
	): Promise<HospitalsCompany[]> {
		const companyId = req.user.companyId;
		return this.findHospitalService.searchHospitalCompany(
			dto.nome,
			Number(companyId),
			dto.limit,
		);
	}

	@Get('searchUnimed')
	searchUnimed(
		@Request() req: { user: Partial<User> },
		@Query() dto: searchHospital,
	): Promise<HospitalsCompany[]> {
		const companyId = req.user.companyId;
		return this.findHospitalService.searchUnimed(
			dto.nome,
			Number(companyId),
			dto.limit,
		);
	}
}
