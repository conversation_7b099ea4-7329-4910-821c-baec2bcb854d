import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { HospitalController } from './hospital.controller';
import { FindHospitalService } from '../../../core/application/services/hospital/find/findHospital.service';
import { HospitalsCompany } from '../../../core/domain/HospitalsCompany';
import { searchHospital } from '../../dto/hospital/searchHospital.dto';
import { User } from '../../../core/domain/User';

describe('HospitalController', () => {
	let app: INestApplication;
	let hospitalController: HospitalController;
	let findHospitalService: FindHospitalService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [HospitalController],
			providers: [
				{
					provide: FindHospitalService.name,
					useValue: {
						searchHospitalCompany: jest.fn(),
						searchUnimed: jest.fn(),
					},
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({ canActivate: jest.fn().mockReturnValue(true) })
			.compile();

		app = module.createNestApplication();
		await app.init();

		hospitalController = module.get<HospitalController>(HospitalController);
		findHospitalService = module.get<FindHospitalService>(
			FindHospitalService.name,
		);
	});

	afterEach(async () => {
		await app.close();
	});

	describe('searchHospital', () => {
		it('should call searchHospitalCompany on FindHospitalService with correct parameters and return the result', async () => {
			// Arrange
			const mockHospitals: HospitalsCompany[] = [
				{ id: 1, nome: 'Hospital 1' } as unknown as HospitalsCompany,
				{ id: 2, nome: 'Hospital 2' } as unknown as HospitalsCompany,
			];
			const mockRequest = {
				user: {
					companyId: 1,
				} as unknown as Partial<User>,
			};
			const mockDto: searchHospital = {
				nome: 'Hospital',
				limit: 10,
			};

			jest
				.spyOn(findHospitalService, 'searchHospitalCompany')
				.mockResolvedValue(mockHospitals);

			const result = await hospitalController.searchHospital(
				mockRequest,
				mockDto,
			);

			expect(findHospitalService.searchHospitalCompany).toHaveBeenCalledWith(
				mockDto.nome,
				Number(mockRequest.user.companyId),
				mockDto.limit,
			);
			expect(result).toEqual(mockHospitals);
		});
	});

	describe('searchUnimed', () => {
		it('should call searchUnimed on FindHospitalService with correct parameters and return the result', async () => {
			const mockHospitals: HospitalsCompany[] = [
				{ id: 1, nome: 'Unimed 1' } as unknown as HospitalsCompany,
				{ id: 2, nome: 'Unimed 2' } as unknown as HospitalsCompany,
			];
			const mockRequest = {
				user: {
					companyId: 1,
				} as unknown as Partial<User>,
			};
			const mockDto: searchHospital = {
				nome: 'Unimed',
				limit: 10,
			};

			jest
				.spyOn(findHospitalService, 'searchUnimed')
				.mockResolvedValue(mockHospitals);

			// Act
			const result = await hospitalController.searchUnimed(
				mockRequest,
				mockDto,
			);

			// Assert
			expect(findHospitalService.searchUnimed).toHaveBeenCalledWith(
				mockDto.nome,
				Number(mockRequest.user.companyId),
				mockDto.limit,
			);
			expect(result).toEqual(mockHospitals);
		});
	});
});
