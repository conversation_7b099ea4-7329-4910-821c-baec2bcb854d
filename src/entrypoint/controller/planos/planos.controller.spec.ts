import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { PlanosController } from './planos.controller';
import { FindPlanosService } from '../../../core/application/services/planos/findPlanos.service';
import { Planos } from '../../../core/domain/Planos';
import { searchPlanos } from '../../dto/planos/searchPlanos.dto';
import { User } from '../../../core/domain/User';

describe('PlanosController', () => {
	let app: INestApplication;
	let planosController: PlanosController;
	let findPlanosService: FindPlanosService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [PlanosController],
			providers: [
				{
					provide: FindPlanosService.name,
					useValue: {
						searchPlanos: jest.fn(),
					},
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({ canActivate: jest.fn().mockReturnValue(true) })
			.compile();

		app = module.createNestApplication();
		await app.init();

		planosController = module.get<PlanosController>(PlanosController);
		findPlanosService = module.get<FindPlanosService>(FindPlanosService.name);
	});

	afterEach(async () => {
		await app.close();
	});

	describe('searchPlanos', () => {
		it('should call searchPlanos on FindPlanosService with correct parameters and return the result', async () => {
			// Arrange
			const mockPlanos: Planos[] = [
				{ id: 1, nome: 'Plano 1' } as Planos,
				{ id: 2, nome: 'Plano 2' } as Planos,
			];
			const mockRequest = {
				user: {
					companyId: 1,
				} as unknown as Partial<User>,
			};
			const mockDto: searchPlanos = {
				nome: 'Plano',
				limit: 10,
			};

			jest
				.spyOn(findPlanosService, 'searchPlanos')
				.mockResolvedValue(mockPlanos);

			// Act
			const result = await planosController.searchPlanos(mockRequest, mockDto);

			// Assert
			expect(findPlanosService.searchPlanos).toHaveBeenCalledWith(
				mockDto.nome,
				Number(mockRequest.user.companyId),
				mockDto.limit,
			);
			expect(result).toEqual(mockPlanos);
		});
	});
});
