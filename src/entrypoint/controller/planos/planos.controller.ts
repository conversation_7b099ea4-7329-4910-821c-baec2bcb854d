import {
	Controller,
	Get,
	Inject,
	Query,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from 'src/core/domain/User';
import { searchPlanos } from '../../dto/planos/searchPlanos.dto';
import { FindPlanosService } from '../../../core/application/services/planos/findPlanos.service';
import { Planos } from '../../../core/domain/Planos';

@Controller('v1/planos')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('planos')
export class PlanosController {
	constructor(
		@Inject(FindPlanosService.name)
		private readonly findPlanosService: FindPlanosService,
	) {}

	@Get('search')
	searchPlanos(
		@Request() req: { user: Partial<User> },
		@Query() dto: searchPlanos,
	): Promise<Planos[]> {
		const companyId = req.user.companyId;
		return this.findPlanosService.searchPlanos(
			dto.nome,
			Number(companyId),
			dto.limit,
		);
	}
}
