import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { EmpresaController } from './empresa.controller';
import { FindEmpresaService } from '../../../core/application/services/empresa/findEmpresa.service';
import { Empresa } from '../../../core/domain/Empresa';
import { searchEmpresa } from '../../dto/empresa/searchEmpresa.dto';
import { User } from '../../../core/domain/User';

describe('EmpresaController', () => {
	let app: INestApplication;
	let empresaController: EmpresaController;
	let findEmpresaService: FindEmpresaService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [EmpresaController],
			providers: [
				{
					provide: FindEmpresaService.name,
					useValue: {
						searchEmpresa: jest.fn(),
					},
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({ canActivate: jest.fn().mockReturnValue(true) })
			.compile();

		app = module.createNestApplication();
		await app.init();

		empresaController = module.get<EmpresaController>(EmpresaController);
		findEmpresaService = module.get<FindEmpresaService>(
			FindEmpresaService.name,
		);
	});

	afterEach(async () => {
		await app.close();
	});

	describe('searchPlanos', () => {
		it('should call searchEmpresa on FindEmpresaService with correct parameters and return the result', async () => {
			// Arrange
			const mockEmpresas: Empresa[] = [
				{ id: 1, nome: 'Empresa 1' } as Empresa,
				{ id: 2, nome: 'Empresa 2' } as Empresa,
			];
			const mockRequest = {
				user: {
					companyId: 1,
				} as unknown as Partial<User>,
			};
			const mockDto: searchEmpresa = {
				nome: 'Empresa',
				limit: 10,
			};

			jest
				.spyOn(findEmpresaService, 'searchEmpresa')
				.mockResolvedValue(mockEmpresas);

			const result = await empresaController.searchPlanos(mockRequest, mockDto);

			expect(findEmpresaService.searchEmpresa).toHaveBeenCalledWith(
				mockDto.nome,
				Number(mockRequest.user.companyId),
				mockDto.limit,
			);
			expect(result).toEqual(mockEmpresas);
		});
	});
});
