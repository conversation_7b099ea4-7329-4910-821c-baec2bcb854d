import {
	Controller,
	Get,
	Inject,
	Query,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { User } from 'src/core/domain/User';
import { FindEmpresaService } from '../../../core/application/services/empresa/findEmpresa.service';
import { Empresa } from '../../../core/domain/Empresa';
import { searchEmpresa } from '../../dto/empresa/searchEmpresa.dto';

@Controller('v1/empresa')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('empresa')
export class EmpresaController {
	constructor(
		@Inject(FindEmpresaService.name)
		private readonly findEmpresaService: FindEmpresaService,
	) {}

	@Get('search')
	searchPlanos(
		@Request() req: { user: Partial<User> },
		@Query() dto: searchEmpresa,
	): Promise<Empresa[]> {
		const companyId = req.user.companyId;
		return this.findEmpresaService.searchEmpresa(
			dto.nome,
			Number(companyId),
			dto.limit,
		);
	}
}
