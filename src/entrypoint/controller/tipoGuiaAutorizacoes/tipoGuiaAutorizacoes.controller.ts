import {
	Controller,
	Get,
	Inject,
	Query,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { FindTipoGuiaAutorizacoesService } from 'src/core/application/services/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes.service';
import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';
import { User } from 'src/core/domain/User';
import { searchTipoGuiaAutorizacoes } from 'src/entrypoint/dto/tipoGuiaAutorizacoes/searchTipoGuiaAutorizacoes.dto';

@Controller('v1/tipoGuiaAutorizacoes')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('tipoGuiaAutorizacoes')
export class TipoGuiaAutorizacoesController {
	constructor(
		@Inject(FindTipoGuiaAutorizacoesService)
		private readonly findTipoGuiaAutorizacoesService: FindTipoGuiaAutorizacoesService,
	) {}

	@Get('search')
	searchTipoGuiaAutorizacoes(
		@Request() req: { user: Partial<User> },
		@Query() dto: searchTipoGuiaAutorizacoes,
	): Promise<TipoGuiaAutorizacoes[]> {
		const companyId = req.user.companyId;
		return this.findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes(
			dto.name,
			Number(companyId),
			dto.limit,
		);
	}
}
