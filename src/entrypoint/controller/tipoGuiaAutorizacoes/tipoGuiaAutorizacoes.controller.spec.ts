import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { TipoGuiaAutorizacoesController } from './tipoGuiaAutorizacoes.controller';
import { FindTipoGuiaAutorizacoesService } from '../../../core/application/services/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes.service';
import { TipoGuiaAutorizacoes } from '../../../core/domain/TipoGuiaAutorizacoes';
import { searchTipoGuiaAutorizacoes } from '../../dto/tipoGuiaAutorizacoes/searchTipoGuiaAutorizacoes.dto';
import { User } from '../../../core/domain/User';

describe('TipoGuiaAutorizacoesController', () => {
	let app: INestApplication;
	let tipoGuiaAutorizacoesController: TipoGuiaAutorizacoesController;
	let findTipoGuiaAutorizacoesService: FindTipoGuiaAutorizacoesService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [TipoGuiaAutorizacoesController],
			providers: [
				{
					provide: FindTipoGuiaAutorizacoesService,
					useValue: {
						searchTipoGuiaAutorizacoes: jest.fn(),
					},
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({ canActivate: jest.fn().mockReturnValue(true) })
			.compile();

		app = module.createNestApplication();
		await app.init();

		tipoGuiaAutorizacoesController = module.get<TipoGuiaAutorizacoesController>(
			TipoGuiaAutorizacoesController,
		);
		findTipoGuiaAutorizacoesService =
			module.get<FindTipoGuiaAutorizacoesService>(
				FindTipoGuiaAutorizacoesService,
			);
	});

	afterEach(async () => {
		await app.close();
	});

	describe('searchTipoGuiaAutorizacoes', () => {
		it('should call searchTipoGuiaAutorizacoes on FindTipoGuiaAutorizacoesService with correct parameters and return the result', async () => {
			// Arrange
			const mockTipoGuiaAutorizacoes: TipoGuiaAutorizacoes[] = [
				{ id: 1, name: 'Tipo 1' } as unknown as TipoGuiaAutorizacoes,
				{ id: 2, name: 'Tipo 2' } as unknown as TipoGuiaAutorizacoes,
			];
			const mockRequest = {
				user: {
					companyId: 1,
				} as unknown as Partial<User>,
			};
			const mockDto: searchTipoGuiaAutorizacoes = {
				name: 'Tipo',
				limit: 10,
			};

			jest
				.spyOn(findTipoGuiaAutorizacoesService, 'searchTipoGuiaAutorizacoes')
				.mockResolvedValue(mockTipoGuiaAutorizacoes);

			const result =
				await tipoGuiaAutorizacoesController.searchTipoGuiaAutorizacoes(
					mockRequest,
					mockDto,
				);

			expect(
				findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes,
			).toHaveBeenCalledWith(
				mockDto.name,
				Number(mockRequest.user.companyId),
				mockDto.limit,
			);
			expect(result).toEqual(mockTipoGuiaAutorizacoes);
		});
	});
});
