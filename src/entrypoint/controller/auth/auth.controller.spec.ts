import { Test, TestingModule } from '@nestjs/testing';
import { Auth } from 'src/core/domain/Auth';
import { AuthGuard } from '@nestjs/passport';
import { LoginDto } from '../../dto/auth/login.dto';
import { INestApplication } from '@nestjs/common';
import { AuthController } from './auth.controller';
import { LoginService } from 'src/core/application/services/auth/login.service';

describe('AuthController', () => {
	let app: INestApplication;
	let authController: AuthController;
	let loginService: LoginService.LoginService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [AuthController],
			providers: [
				{
					provide: LoginService.LoginService,
					useValue: {
						login: jest.fn(),
					},
				},
			],
		})
			.overrideGuard(AuthGuard('local'))
			.useValue({ canActivate: jest.fn().mockReturnValue(true) })
			.compile();

		app = module.createNestApplication();
		await app.init();

		authController = module.get<AuthController>(AuthController);
		loginService = module.get<LoginService.LoginService>(
			LoginService.LoginService,
		);
	});

	afterEach(async () => {
		await app.close();
	});

	it('should call login on LoginService with correct parameters and return the result', async () => {
		const mockAuth: Auth = { token: 'mock_token' };
		const mockRequest: LoginDto = {
			id: 1,
			email: '<EMAIL>',
			password: 'password123',
		};

		jest.spyOn(loginService, 'login').mockResolvedValue(mockAuth);

		const result = await authController.login({ user: mockRequest });

		expect(loginService.login).toHaveBeenCalledWith(mockRequest);
		expect(result).toEqual(mockAuth);
	});
});
