import { Controller, Inject, Post, Req, UseGuards } from '@nestjs/common';
import { LoginService } from 'src/core/application/services/auth/login.service';

import { AuthGuard } from '@nestjs/passport';
import { Auth } from 'src/core/domain/Auth';
import { LoginDto } from '../../dto/auth/login.dto';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { User } from 'src/core/domain/User';
@Controller('v1/auth')
@ApiTags('auth')
export class AuthController {
	constructor(
		@Inject(LoginService.LoginService)
		private readonly loginService: LoginService.LoginService,
	) {}

	@UseGuards(AuthGuard('local'))
	@Post('login')
	@ApiBody({ type: LoginDto })
	async login(@Req() req: { user: LoginDto }): Promise<Auth> {
		const user = req.user as User;
		return await this.loginService.login(user);
	}
}
