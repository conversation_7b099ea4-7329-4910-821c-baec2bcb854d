import { Test, TestingModule } from '@nestjs/testing';
import { DownloadModeloCensoService } from 'src/core/application/services/file/download/censo/downloadModeloCenso.service';
import { DownloadController } from '../download/download.controller';

describe('DownloadController', () => {
	let controller: DownloadController;
	let downloadModeloCensoService: DownloadModeloCensoService;

	beforeEach(async () => {
		const module: TestingModule = await Test.createTestingModule({
			controllers: [DownloadController],
			providers: [
				{
					provide: DownloadModeloCensoService,
					useValue: {
						execute: jest.fn().mockResolvedValue('csv,data,content'),
					},
				},
			],
		}).compile();

		controller = module.get<DownloadController>(DownloadController);
		downloadModeloCensoService = module.get<DownloadModeloCensoService>(
			DownloadModeloCensoService,
		);
	});

	it('should be defined', () => {
		expect(controller).toBeDefined();
	});

	describe('downloadModeloCensoByCompanyId', () => {
		it('should call downloadModeloCensoService.execute with correct companyId', async () => {
			const companyId = 1;
			const result = await controller.downloadModeloCensoByCompanyId(companyId);

			expect(downloadModeloCensoService.execute).toHaveBeenCalledWith(
				companyId,
			);
			expect(result).toBe('csv,data,content');
		});
	});
});
