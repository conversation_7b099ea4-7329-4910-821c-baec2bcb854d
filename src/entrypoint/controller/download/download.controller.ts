import {
	<PERSON>,
	Get,
	Header,
	Inject,
	Param,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { DownloadModeloCensoService } from 'src/core/application/services/file/download/censo/downloadModeloCenso.service';

@Controller('v1/download')
@ApiTags('download')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
export class DownloadController {
	constructor(
		@Inject(DownloadModeloCensoService)
		private downloadModeloCensoService: DownloadModeloCensoService,
	) {}

	@Get('censo/modelo/:companyId')
	@Header('Content-Type', 'text/csv')
	@Header('Content-Disposition', 'attachment; filename=data.csv')
	async downloadModeloCensoByCompanyId(
		@Param('companyId') companyId: number,
	): Promise<string> {
		return this.downloadModeloCensoService.execute(companyId);
	}
}
