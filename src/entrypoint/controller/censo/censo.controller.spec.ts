/* eslint-disable @typescript-eslint/no-explicit-any */
import { Test, TestingModule } from '@nestjs/testing';
import { FindCensoService } from 'src/core/application/services/censo/findCenso.service';
import { Censo } from 'src/core/domain/Censo';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { AuthGuard } from '@nestjs/passport';
import { CensoController } from './censo.controller';
import { ProcessCensoService } from 'src/core/application/services/censo/processCenso.service';
import { ProcessaCensoDto } from '../../dto/censo/processaCenso.dto';
import { RetornoProcessaCenso } from 'src/core/application/dto/RetornoProcessaCenso';
import { CensoMapper } from 'src/shared/mapper/censo/censo.mapper';
import { ProcessaCenso } from 'src/core/application/dto/ProcessaCenso';
import { DeleteCensoService } from 'src/core/application/services/censo/deleteCenso.service';
import { RetornoDeleteCenso } from 'src/core/application/dto/RetornoDeleteCenso';
import { OrdenacaoCensoListagem } from 'src/core/application/dto/OrdenacaoCensoListagem';
import { OrdenacaoEnum } from 'src/core/application/dto/OrdenacaoEnum';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { FindListagemDto } from '../../dto/censo/findListagem.dto';

jest.mock('@nestjs/passport', () => ({
	AuthGuard: jest.fn(() => ({
		canActivate: jest.fn(() => true),
	})),
}));

describe('CensoController', () => {
	let censoController: CensoController;
	let findCensoServiceMock: jest.Mocked<FindCensoService.FindCensoService>;
	let processCensoServiceMock: jest.Mocked<ProcessCensoService.ProcessCensoService>;
	let deleteCensoServiceMock: jest.Mocked<DeleteCensoService.DeleteCensoService>;
	let censosMock: Censo[];
	let processaCenso: ProcessaCenso;
	beforeEach(async () => {
		findCensoServiceMock = {
			findByCompanyId: jest.fn(),
			findListagem: jest.fn(),
			findById: jest.fn(),
			findModalStateByUser: jest.fn(),
		} as unknown as jest.Mocked<FindCensoService.FindCensoService>;

		processCensoServiceMock = {
			process: jest.fn(),
		} as unknown as jest.Mocked<ProcessCensoService.ProcessCensoService>;

		deleteCensoServiceMock = {
			delete: jest.fn(),
		} as unknown as jest.Mocked<DeleteCensoService.DeleteCensoService>;

		const module: TestingModule = await Test.createTestingModule({
			controllers: [CensoController],
			providers: [
				{
					provide: FindCensoService.FindCensoService,
					useValue: findCensoServiceMock,
				},
				{
					provide: ProcessCensoService.ProcessCensoService,
					useValue: processCensoServiceMock,
				},
				{
					provide: DeleteCensoService.DeleteCensoService,
					useValue: deleteCensoServiceMock,
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({
				canActivate: jest.fn(() => true),
			})
			.compile();

		censoController = module.get<CensoController>(CensoController);
		censosMock = [
			new Censo(
				'1',
				new Date(),
				'1',
				100,
				'user1',
				{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
				{ name: 'Operadora A' },
				{ name: 'User A' },
				'/mock/directory',
				'censo',
			),
		];

		processaCenso = new ProcessaCenso(0, '/diretorio/salvo', 1, 1);
	});

	describe('findByCompanyId', () => {
		it('deve chamar findByCompanyId do serviço e retornar uma lista de censos', async () => {
			const companyId = 1;

			findCensoServiceMock.findByCompanyId.mockResolvedValue(censosMock);

			const result = await censoController.findByCompanyId(companyId);

			expect(findCensoServiceMock.findByCompanyId).toHaveBeenCalledWith(
				companyId,
			);
			expect(result).toEqual(censosMock);
		});
	});

	describe('findModalStateByUser', () => {
		it('deve chamar findModalStateByUser do serviço e retornar um censos', async () => {
			const userId = 1;
			const censoMock = new Censo(
				'1',
				new Date(),
				'1',
				100,
				'user1',
				{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
				{ name: 'Operadora A' },
				{ name: 'User A' },
				'/diretorio/salvo',
				'censo',
			);
			findCensoServiceMock.findModalStateByUser.mockResolvedValue(censoMock);

			const result = await censoController.findModalStateByUser(userId);

			expect(findCensoServiceMock.findModalStateByUser).toHaveBeenCalledWith(
				userId,
			);

			expect(result).toStrictEqual(censoMock);
		});
	});

	describe('deleteCenso', () => {
		it('deve chamar deleteCenso do serviço e deletar censo', async () => {
			const censoId = 1;
			const returnMock: RetornoDeleteCenso = {
				message: 'Censo Deletado com Sucesso',
			};

			deleteCensoServiceMock.delete.mockResolvedValue(returnMock);

			const result = await censoController.deleteCenso(censoId);

			expect(deleteCensoServiceMock.delete).toHaveBeenCalledWith(censoId);

			expect(result).toStrictEqual(returnMock);
		});
	});

	describe('findListagem', () => {
		it('deve chamar findListagem do serviço e retornar CensoListagem', async () => {
			const companyId = 1;
			const censoListagemMock = new CensoListagem(1, 10, censosMock);
			const ordenacao = new OrdenacaoCensoListagem(
				OrdenacaoEnum.ASC,
				OrdenacaoEnum.ASC,
				OrdenacaoEnum.ASC,
				OrdenacaoEnum.ASC,
			);

			const params = new Paginacao(1, 10, undefined);
			const query: Paginacao & OrdenacaoCensoListagem = {
				...params,
				...ordenacao,
			};
			findCensoServiceMock.findListagem.mockResolvedValue(censoListagemMock);

			const result = await censoController.findListagem(
				companyId,
				query as FindListagemDto,
			);

			expect(findCensoServiceMock.findListagem).toHaveBeenCalledWith(
				companyId,
				query,
			);
			expect(result).toEqual(censoListagemMock);
		});
	});

	describe('processarCenso', () => {
		it('deve iniciar o processamento do censo', async () => {
			const processaCensoDto = new ProcessaCensoDto(0);
			const retornoProcessaCenso = new RetornoProcessaCenso('1');

			findCensoServiceMock.findById.mockResolvedValue(
				new Censo(
					'1',
					new Date(),
					'1',
					100,
					'user1',
					{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
					{ name: 'Operadora A' },
					{ name: 'User A' },
					'/diretorio/salvo',
					'censo',
				),
			);

			jest.spyOn(CensoMapper, 'toProcessaCenso').mockReturnValue(processaCenso);

			processCensoServiceMock.process.mockResolvedValue(retornoProcessaCenso);

			const result = await censoController.processarCenso(processaCensoDto);

			expect(processCensoServiceMock.process).toHaveBeenCalledWith(
				processaCenso,
			);
			expect(result).toEqual(retornoProcessaCenso);
		});

		it('deve soltar erro ao iniciar processo do censo sem ter achado um censo', async () => {
			const processaCenso = new ProcessaCensoDto(0);

			findCensoServiceMock.findById.mockReturnValue(null);

			await expect(
				censoController.processarCenso(processaCenso),
			).rejects.toThrow('Censo não encontrado');

			expect(processCensoServiceMock.process).not.toHaveBeenCalled();
		});
	});
});
