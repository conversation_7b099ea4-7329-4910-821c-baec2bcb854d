import {
	Body,
	Controller,
	Delete,
	Get,
	HttpStatus,
	Inject,
	Param,
	Post,
	Query,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import {
	ApiBearerAuth,
	ApiBody,
	ApiExcludeEndpoint,
	ApiParam,
	ApiResponse,
	ApiTags,
} from '@nestjs/swagger';
import { FindCensoService } from 'src/core/application/services/censo/findCenso.service';
import { Censo } from 'src/core/domain/Censo';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { ProcessaCensoDto } from '../../dto/censo/processaCenso.dto';
import { ProcessCensoService } from 'src/core/application/services/censo/processCenso.service';
import { CensoMapper } from 'src/shared/mapper/censo/censo.mapper';
import { RetornoProcessaCenso } from 'src/core/application/dto/RetornoProcessaCenso';
import { CensoNotFoundException } from 'src/shared/exceptions/rule/censoNotFound.exception';
import { DeleteCensoService } from 'src/core/application/services/censo/deleteCenso.service';
import { RetornoDeleteCenso } from 'src/core/application/dto/RetornoDeleteCenso';
import { FindListagemDto } from '../../dto/censo/findListagem.dto';

@Controller('v1/censo')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('censo')
export class CensoController {
	constructor(
		@Inject(FindCensoService.FindCensoService)
		private readonly findCensoService: FindCensoService.FindCensoService,
		private readonly processCensoService: ProcessCensoService.ProcessCensoService,
		private readonly deleteCensoService: DeleteCensoService.DeleteCensoService,
	) {}

	@Get('operadora/:companyId')
	@ApiExcludeEndpoint()
	async findByCompanyId(
		@Param('companyId') companyId: number,
	): Promise<Censo[]> {
		return await this.findCensoService.findByCompanyId(companyId);
	}

	@Get('user/modal-estado/:userId')
	async findModalStateByUser(@Param('userId') userId: number): Promise<Censo> {
		return await this.findCensoService.findModalStateByUser(userId);
	}

	@Get(':companyId')
	@ApiParam({
		name: 'companyId',
		type: Number,
		description: 'ID da operadora para filtrar os censos',
	})
	@ApiResponse({
		status: 200,
		description: 'Lista de censos encontrados',
	})
	async findListagem(
		@Param('companyId') companyId: number,
		@Query() query: FindListagemDto,
	): Promise<CensoListagem> {
		return await this.findCensoService.findListagem(companyId, query);
	}

	@Post('/processar')
	@ApiBody({ type: ProcessaCensoDto })
	async processarCenso(
		@Body() processCensoDto: ProcessaCensoDto,
	): Promise<RetornoProcessaCenso> {
		const censo = await this.findCensoService.findById(processCensoDto.censoId);
		if (!censo)
			throw new CensoNotFoundException(
				HttpStatus.BAD_REQUEST,
				'Censo não encontrado',
			);

		const processaCenso = CensoMapper.toProcessaCenso(censo);
		return await this.processCensoService.process(processaCenso);
	}

	@Delete(':censoId')
	async deleteCenso(
		@Param('censoId') censoId: number,
	): Promise<RetornoDeleteCenso> {
		return await this.deleteCensoService.delete(censoId);
	}
}
