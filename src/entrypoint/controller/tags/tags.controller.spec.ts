import { Test, TestingModule } from '@nestjs/testing';
import { TagsController } from './tags.controller';
import { FindTagsService } from 'src/core/application/services/tags/findTags.service';
import { Tags } from 'src/core/domain/Tags';
import { SearchTagsDto } from 'src/entrypoint/dto/tags/searchTags.dto';
import { User } from 'src/core/domain/User';

jest.mock('@nestjs/passport', () => ({
	AuthGuard: jest.fn(() => ({
		canActivate: jest.fn(() => true),
	})),
}));

describe('TagsController', () => {
	let tagsController: TagsController;
	let findTagsServiceMock: jest.Mocked<FindTagsService>;
	let tagsMock: Tags[];

	beforeEach(async () => {
		findTagsServiceMock = {
			searchTags: jest.fn(),
		} as unknown as jest.Mocked<FindTagsService>;

		const module: TestingModule = await Test.createTestingModule({
			controllers: [TagsController],
			providers: [
				{
					provide: FindTagsService,
					useValue: findTagsServiceMock,
				},
			],
		}).compile();

		tagsController = module.get<TagsController>(TagsController);

		tagsMock = [
			{
				id: 1,
				tag: 'Test Tag',
				modulo: 'test',
				habilitado: true,
			},
		];
	});

	describe('searchTags', () => {
		it('deve chamar searchTags do serviço e retornar uma lista de tags', async () => {
			const dto: SearchTagsDto = {
				nome: 'Test',
				modulo: 'test',
				limit: 10,
			};

			const req: { user: Partial<User> } = {
				user: {
					companyId: '1',
				},
			};

			findTagsServiceMock.searchTags.mockResolvedValue(tagsMock);

			const result = await tagsController.searchTags(req, dto);

			expect(findTagsServiceMock.searchTags).toHaveBeenCalledWith(
				dto.nome,
				Number(req.user.companyId),
				dto.modulo,
				dto.limit,
			);
			expect(result).toEqual(tagsMock);
		});

		it('deve chamar searchTags com os parâmetros corretos quando o usuário pertence a uma empresa', async () => {
			const dto: SearchTagsDto = {
				nome: 'Test',
				modulo: 'test',
				limit: 10,
			};

			const companyId = 123;
			const req: { user: Partial<User> } = {
				user: {
					companyId: companyId.toString(),
				},
			};

			findTagsServiceMock.searchTags.mockResolvedValue([]);

			await tagsController.searchTags(req, dto);

			expect(findTagsServiceMock.searchTags).toHaveBeenCalledWith(
				dto.nome,
				companyId,
				dto.modulo,
				dto.limit,
			);
		});

		it('deve retornar um array vazio quando nenhuma tag é encontrada', async () => {
			const dto: SearchTagsDto = {
				nome: 'NonExistent',
				modulo: 'test',
				limit: 10,
			};

			const req: { user: Partial<User> } = {
				user: {
					companyId: '1',
				},
			};

			findTagsServiceMock.searchTags.mockResolvedValue([]);

			const result = await tagsController.searchTags(req, dto);

			expect(result).toEqual([]);
			expect(findTagsServiceMock.searchTags).toHaveBeenCalled();
		});
	});
});
