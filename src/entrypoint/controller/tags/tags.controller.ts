import {
	Controller,
	Get,
	Inject,
	Query,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { FindTagsService } from 'src/core/application/services/tags/findTags.service';
import { Tags } from 'src/core/domain/Tags';
import { User } from 'src/core/domain/User';
import { SearchTagsDto } from 'src/entrypoint/dto/tags/searchTags.dto';

@Controller('v1/tags')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('tags')
export class TagsController {
	constructor(
		@Inject(FindTagsService)
		private readonly findTagsService: FindTagsService,
	) {}

	@Get('search/:modulo')
	searchTags(
		@Request() req: { user: Partial<User> },
		@Query() dto: SearchTagsDto,
	): Promise<Tags[]> {
		const companyId = req.user.companyId;
		return this.findTagsService.searchTags(
			dto.nome,
			Number(companyId),
			dto.modulo,
			dto.limit,
		);
	}
}
