import {
	Body,
	Controller,
	Inject,
	Post,
	Request,
	UseGuards,
} from '@nestjs/common';
import { AuthGuard } from '@nestjs/passport';
import { ApiBearerAuth, ApiResponse, ApiTags } from '@nestjs/swagger';
import { AutorizacoesListagemDto } from 'src/core/application/dto/AutorizacoesListagem';
import { FindAutorizacoesService } from 'src/core/application/services/autorizacoes/findAutorizacoes.service';
import { User } from 'src/core/domain/User';
import { FindListagemDto } from 'src/entrypoint/dto/autorizacoes/findListagem.dto';

@Controller('v1/autorizacoes')
@UseGuards(AuthGuard('jwt'))
@ApiBearerAuth('access-token')
@ApiTags('autorizacoes')
export class AutorizacoesController {
	constructor(
		@Inject(FindAutorizacoesService.name)
		private readonly findAutorizacoesService: FindAutorizacoesService,
	) {}

	@Post('/listagem')
	@ApiResponse({
		status: 200,
		description: 'Lista de autorizações encontradas',
	})
	async findListagem(
		@Request() req: { user: Partial<User> },
		@Body() filtros: FindListagemDto,
	): Promise<AutorizacoesListagemDto> {
		const companyId = req.user.companyId;
		return await this.findAutorizacoesService.findListagem(
			Number(companyId),
			filtros,
		);
	}
}
