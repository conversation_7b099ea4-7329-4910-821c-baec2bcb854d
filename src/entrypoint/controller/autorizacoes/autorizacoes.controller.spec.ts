import { Test, TestingModule } from '@nestjs/testing';
import { AutorizacoesController } from './autorizacoes.controller';
import { FindAutorizacoesService } from 'src/core/application/services/autorizacoes/findAutorizacoes.service';
import { AuthGuard } from '@nestjs/passport';
import { FindListagemDto } from '../../dto/autorizacoes/findListagem.dto';
import { AutorizacoesListagemDto } from 'src/core/application/dto/AutorizacoesListagem';
import { User } from 'src/core/domain/User';
import { AutorizacoesViewModelDto } from 'src/core/application/dto/AutorizacoesViewModel';
import { PeriodoFiltroEnum } from 'src/core/application/enums/autorizacoes/periodoFiltro.enum';

jest.mock('@nestjs/passport', () => ({
	AuthGuard: jest.fn(() => ({
		canActivate: jest.fn(() => true),
	})),
}));

describe('AutorizacoesController', () => {
	let autorizacoesController: AutorizacoesController;
	let findAutorizacoesServiceMock: jest.Mocked<FindAutorizacoesService>;

	const mockAutorizacao = new AutorizacoesViewModelDto(
		1, // id
		'João Silva', // nomePaciente
		'123456', // codBeneficiario
		'Eletivo', // carater
		'01/01/2024', // dataPedido
		new Date(), // dataLimite
		'PAC123', // pac
		'Hospital ABC', // prestador
		'Ambulatorial', // regimeInternacao
		'Plano Plus', // plano
		'Ativo', // statusPlano
		true, // planoRegulamentado
		'APROVADO', // statusIa
		'Dentro dos critérios', // motivoIa
		'Aprovação automática', // descricaoIa
		1, // idAutorizacao
		1, // idPaciente
		1, // idEmpresa
		'12345', // numeroGuia
		'J45', // cidPrincipal
		'SADT', // tipoGuia
		'Em Análise', // statusAuditoria
		0, // numeroReanalises
		'TRX123', // numeroTransacao
		'SADT', // tipoGuia2
		'urgente,prioritário', // tags
		new Date(), // dataVencimento
		'Hospital ABC LTDA', // empresa
		'Ativo', // status
		'Em Análise', // statusDescricao
		'amarelo', // statusCor
		new Date(), // dataCriacao
		'Unimed Test', // nomeUnimed
		0, // pendencia
		'Observação teste', // observacoes
		'Sem negativa', // motivoNegativa
		false, // isFavorito
		0, // isFavorito2
		'Sugestão teste', // sugestao
		'Dr. Smith', // ultimoAuditou
		new Date(), // dataUltimoAuditou
	);

	const mockListagem: AutorizacoesListagemDto = {
		pagina: 1,
		quantidadeTotal: 1,
		autorizacoes: [mockAutorizacao],
	};

	const mockRequest = {
		user: {
			id: 1,
			companyId: '1',
			name: 'Test User',
			email: '<EMAIL>',
		} as Partial<User>,
	};

	beforeEach(async () => {
		findAutorizacoesServiceMock = {
			findListagem: jest.fn(),
		} as unknown as jest.Mocked<FindAutorizacoesService>;

		const module: TestingModule = await Test.createTestingModule({
			controllers: [AutorizacoesController],
			providers: [
				{
					provide: FindAutorizacoesService.name,
					useValue: findAutorizacoesServiceMock,
				},
			],
		})
			.overrideGuard(AuthGuard('jwt'))
			.useValue({ canActivate: jest.fn(() => true) })
			.compile();

		autorizacoesController = module.get<AutorizacoesController>(
			AutorizacoesController,
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('findListagem', () => {
		it('deve retornar a listagem de autorizações com sucesso', async () => {
			// Arrange
			const filtros: FindListagemDto = {
				page: 1,
				limit: 10,
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
				periodo: PeriodoFiltroEnum.PEDIDOS_EM,
			} as FindListagemDto;

			findAutorizacoesServiceMock.findListagem.mockResolvedValueOnce(
				mockListagem,
			);

			// Act
			const result = await autorizacoesController.findListagem(
				mockRequest,
				filtros,
			);

			// Assert
			expect(findAutorizacoesServiceMock.findListagem).toHaveBeenCalledWith(
				Number(mockRequest.user.companyId),
				filtros,
			);
			expect(findAutorizacoesServiceMock.findListagem).toHaveBeenCalledTimes(1);

			expect(result).toBeDefined();
			expect(result).toEqual(mockListagem);
			expect(result.autorizacoes).toHaveLength(1);
			expect(result.autorizacoes[0]).toEqual(mockAutorizacao);
		});

		it('deve retornar lista vazia quando não houver autorizações', async () => {
			// Arrange
			const filtros: FindListagemDto = {
				page: 1,
				limit: 10,
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
				periodo: PeriodoFiltroEnum.PEDIDOS_EM,
			} as FindListagemDto;

			const emptyListagem = new AutorizacoesListagemDto(1, 0, []);

			findAutorizacoesServiceMock.findListagem.mockResolvedValueOnce(
				emptyListagem,
			);

			// Act
			const result = await autorizacoesController.findListagem(
				mockRequest,
				filtros,
			);

			// Assert
			expect(findAutorizacoesServiceMock.findListagem).toHaveBeenCalledWith(
				Number(mockRequest.user.companyId),
				filtros,
			);
			expect(result.autorizacoes).toHaveLength(0);
			expect(result.quantidadeTotal).toBe(0);
		});

		it('deve propagar erro quando serviço falhar', async () => {
			// Arrange
			const filtros: FindListagemDto = {
				page: 1,
				limit: 10,
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
				periodo: PeriodoFiltroEnum.PEDIDOS_EM,
			} as FindListagemDto;

			const errorMessage = 'Erro ao buscar autorizações';
			findAutorizacoesServiceMock.findListagem.mockRejectedValueOnce(
				new Error(errorMessage),
			);

			// Act & Assert
			await expect(
				autorizacoesController.findListagem(mockRequest, filtros),
			).rejects.toThrow(errorMessage);

			expect(findAutorizacoesServiceMock.findListagem).toHaveBeenCalledWith(
				Number(mockRequest.user.companyId),
				filtros,
			);
		});

		it('deve converter companyId para número ao chamar o serviço', async () => {
			// Arrange
			const filtros: FindListagemDto = {
				page: 1,
				limit: 10,
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
				periodo: PeriodoFiltroEnum.PEDIDOS_EM,
			} as FindListagemDto;

			const requestWithStringId = {
				user: { ...mockRequest.user, companyId: '123' },
			};

			findAutorizacoesServiceMock.findListagem.mockResolvedValueOnce(
				mockListagem,
			);

			// Act
			await autorizacoesController.findListagem(requestWithStringId, filtros);

			// Assert
			expect(findAutorizacoesServiceMock.findListagem).toHaveBeenCalledWith(
				123,
				filtros,
			);
		});
	});
});
