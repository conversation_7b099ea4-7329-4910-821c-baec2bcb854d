import { NestFactory } from '@nestjs/core';
import { AppModule } from './configuration/module/app.module';
import { OpenApiConfig } from './configuration/openapi.config';
import { HttpExceptionFilter } from './configuration/httpException.config.filter';
import { ValidationPipe } from '@nestjs/common';

async function bootstrap(): Promise<void> {
	const app = await NestFactory.create(AppModule);
	OpenApiConfig.executeConfig(app);
	app.useGlobalFilters(new HttpExceptionFilter());
	app.useGlobalPipes(new ValidationPipe({ transform: true }));
	await app.listen(5000);
	return;
}
bootstrap();
