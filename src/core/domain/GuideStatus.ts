import { Guide } from './Guide';

export class GuideStatus {
	constructor(
		public id: number | null,
		public guide: Guide | Partial<Guide>,
		public approvedBy?: number,
		public status: number = 0, // 0 - a<PERSON>, 1 - analise, 2 - aprovado, 3 - recusado, 4 - administrativo, 5 - reanalise
		public justificativa?: number,
		public obs?: string,
		public created?: Date,
		public updated?: Date,
		public enabled: number = 1,
		public userId?: number,
	) {}
}
