import { Guide } from './Guide';

export class GuideDaily {
	constructor(
		public id: number | null,
		public guide: Guide | Partial<Guide>,
		public accommodation?: string,
		public qtdeDaysRequested?: number,
		public qtdeDaysAuthorized?: number,
		public qtdeDaysDenied?: number,
		public isCenso: number = 0,
		public userId?: number,
		public price: number = 0.0,
		public guideRequestId?: number,
		public enabled: number = 1,
		public dateStart?: Date,
		public dateFinish?: Date,
		public qtdeDays?: number,
		public aux: number = 0,
		public auxId?: number,
		public codIntegration?: string,
		public updated?: Date,
	) {}
}
