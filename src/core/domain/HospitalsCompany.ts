import { Hospital } from './Hospital';

export class HospitalsCompany {
	constructor(
		readonly id: number,
		readonly valorMedioDia: number,
		readonly email: string,
		readonly enabled: boolean,
		readonly tipo: string,
		readonly valorAlertaMes: number,
		readonly enfClinica: number,
		readonly enfCirurgica: number,
		readonly enfObstetrica: number,
		readonly enfPediatrica: number,
		readonly enfPsiquiatrica: number,
		readonly utiaClinica: number,
		readonly utiaCirurgica: number,
		readonly utiaObstetrica: number,
		readonly utiaPediatrica: number,
		readonly utiaPsiquiatrica: number,
		readonly utipClinica: number,
		readonly utipCirurgica: number,
		readonly utipObstetrica: number,
		readonly utipPediatrica: number,
		readonly utipPsiquiatrica: number,
		readonly utinClinica: number,
		readonly utinCirurgica: number,
		readonly utinObstetrica: number,
		readonly utinPediatrica: number,
		readonly utinPsiquiatrica: number,
		readonly tsiClinica: number,
		readonly tsiCirurgica: number,
		readonly tsiObstetrica: number,
		readonly tsiPediatrica: number,
		readonly tsiPsiquiatrica: number,
		readonly ucoClinica: number,
		readonly ucoCirurgica: number,
		readonly ucoObstetrica: number,
		readonly ucoPediatrica: number,
		readonly ucoPsiquiatrica: number,
		readonly uceClinica: number,
		readonly uceCirurgica: number,
		readonly uceObstetrica: number,
		readonly ucePediatrica: number,
		readonly ucePsiquiatrica: number,
		readonly bernClinica: number,
		readonly bernCirurgica: number,
		readonly bernObstetrica: number,
		readonly bernPediatrica: number,
		readonly bernPsiquiatrica: number,
		readonly berppClinica: number,
		readonly berppCirurgica: number,
		readonly berppObstetrica: number,
		readonly berppPediatrica: number,
		readonly berppPsiquiatrica: number,
		readonly apartClinica: number,
		readonly apartCirurgica: number,
		readonly apartObstetrica: number,
		readonly apartPediatrica: number,
		readonly domiTransicao: number,
		readonly domiMulti: number,
		readonly domiMedicamento: number,
		readonly domiCurativo: number,
		readonly domi6h: number,
		readonly domi12h: number,
		readonly domi24Av: number,
		readonly domi24Vm: number,
		readonly onedayPsiquiatrica: number,
		readonly onedayPediatrica: number,
		readonly onedayObstetrica: number,
		readonly onedayCirurgica: number,
		readonly onedayClinica: number,
		readonly apartPsiquiatrica: number,
		readonly permanenciaEnf: number,
		readonly permanenciaUtia: number,
		readonly utiPsiquiatrica: number,
		readonly utiPediatrica: number,
		readonly utiObstetrica: number,
		readonly utiCirurgica: number,
		readonly utiClinica: number,
		readonly utiiPsiquiatrica: number,
		readonly utiiPediatrica: number,
		readonly utiiObstetrica: number,
		readonly utiiCirurgica: number,
		readonly utiiClinica: number,
		readonly utiniPsiquiatrica: number,
		readonly utiniPediatrica: number,
		readonly utiniObstetrica: number,
		readonly utiniCirurgica: number,
		readonly utiniClinica: number,
		readonly ucoiPsiquiatrica: number,
		readonly ucoiPediatrica: number,
		readonly ucoiObstetrica: number,
		readonly ucoiCirurgica: number,
		readonly ucoiClinica: number,
		readonly semiutiiPsiquiatrica: number,
		readonly semiutiiPediatrica: number,
		readonly semiutiiObstetrica: number,
		readonly ssemiutiiCirurgica: number,
		readonly semiutiiClinica: number,
		readonly semiutiPsiquiatrica: number,
		readonly semiutiPediatrica: number,
		readonly semiutiObstetrica: number,
		readonly semiutiCirurgica: number,
		readonly semiutiClinica: number,
		readonly utmoPsiquiatrica: number,
		readonly utmoPediatrica: number,
		readonly utmoObstetrica: number,
		readonly utmoCirurgica: number,
		readonly utmoClinica: number,
		readonly ucgPsiquiatrica: number,
		readonly ucgPediatrica: number,
		readonly ucgObstetrica: number,
		readonly ucgCirurgica: number,
		readonly ucgClinica: number,
		readonly ucgiPsiquiatrica: number,
		readonly ucgiPediatrica: number,
		readonly ucgiObstetrica: number,
		readonly ucgiCirurgica: number,
		readonly ucgiClinica: number,
		readonly apartiPsiquiatrica: number,
		readonly apartiPediatrica: number,
		readonly apartiObstetrica: number,
		readonly apartiCirurgica: number,
		readonly apartiClinica: number,
		readonly complexity: string,
		readonly homecareEquipamento: number,
		readonly homecareMaterial: number,
		readonly assistenciaDomiciliar: number,
		readonly internacaoDomiciliar: number,
		readonly homecareFisioterapia: number,
		readonly homecareInternado24: number,
		readonly homecareAntibiotic: number,
		readonly companyId?: number,
		public hospital?: Hospital,
		readonly created?: Date,
		readonly updated?: Date,
		readonly name?: string,
		readonly name2?: string,
		readonly name3?: string,
		readonly tel?: string,
		readonly tel2?: string,
		readonly dscTpLogra?: string,
		readonly endereco?: string,
		readonly numero?: string,
		readonly bairro?: string,
		readonly estado?: string,
		readonly cidade?: string,
		readonly regiao?: string,
		readonly regiaoNova?: string,
		readonly controle?: string,
		readonly controle2?: string,
		readonly cityId?: number,
		readonly stateId?: number,
		readonly codigoInterno?: string,
		readonly handlePrestador?: string,
		readonly codDrg?: number,
	) {}
}
