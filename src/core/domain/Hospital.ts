export class Hospital {
	constructor(
		public id: number | null,
		readonly coUnidade: string,
		readonly coCnes: string,
		readonly created?: Date,
		readonly updated?: Date,
		readonly nuCnpjMantenedora?: string,
		readonly noRazaoSocial?: string,
		readonly noFantasia?: string,
		readonly noLogradouro?: string,
		readonly nuEndereco?: string,
		readonly noComplemento?: string,
		readonly noBairro?: string,
		readonly coCep?: string,
		readonly nuTelefone?: string,
		readonly nuFax?: string,
		readonly noEmail?: string,
		readonly nuCpf?: string,
		readonly nuCnpj?: string,
		readonly coAtividade?: string,
		readonly coClientela?: string,
		readonly tpUnidade?: number,
		readonly coTurnoAtendimento?: string,
		readonly coEstadoGestor?: number,
		readonly coMunicipioGestor?: number,
		readonly dtAtualizacao?: string,
		readonly noUrl?: string,
		readonly nuLatitude?: string,
		readonly nuLongitude?: string,
		readonly tpEstabSempreAberto?: string,
		readonly stConexaoInternet?: string,
		readonly tpGestao?: string,
		readonly tpLogradouroCarefy?: string,
		readonly isUnimed?: boolean,
	) {}
}
