export class Patient {
	constructor(
		public readonly id: number,
		public enabled: number,
		public created: Date,
		public updated: Date,
		public userId: number,
		public name: string,
		public email?: string,
		public motherName?: string,
		public companyId?: number,
		public gender?: string,
		public codBeneficiario?: string,
		public birthday?: Date,
		public planId?: number,
		public planStartDate?: Date,
		public planEndDate?: Date,
		public group?: string,
		public occupation?: string,
		public pregnantAddInfo?: string,
		public lactatingAddInfo?: string,
		public permissionAppMaxDate?: Date,
		public startDate?: Date,
		public image?: string,
		public tel?: string,
		public patientCity?: string,
		public codCidadeIbge?: string,
		public regHosp?: string,
		public color?: string,
		public cpf?: string,
		public internadoRepasse?: number,
		public isCenso?: number,
		public replicateFlag?: number,
		public cityId?: number,
		public street?: string,
		public homeNumber?: string,
		public neighborhood?: string,
		public zipcode?: string,
		public complement?: string,
		public corporationId?: number,
		public status?: string,
		public error?: number,
		public codIntegration?: string,
		public planRegulamentado?: number,
		public planoTipo?: string,
		public planoAbrangencia?: string,
		public planoTipoRede?: string,
		public planoCarencia?: string,
		public gruposCidadesIbge?: string,
		public isRn?: number,
		public carteiraNacionalSaude?: string,
		public idade?: number,
		public sexo?: string,
		public massa?: number,
		public altura?: number,
		public superficieCorporal?: number,
	) {}
}
