import { BedHospital } from './BedHospital';
import { Patient } from './Patient';

export class Hospitalization {
	constructor(
		public readonly id: number,
		public created: Date,
		public updated: Date,
		public admissionIn: Date,
		public isAdmissionOutCenso: number,
		public enabled: number,
		public isNewborn: number,
		public isBill: number,
		public regime: string,
		public admissionOut?: Date,
		public admissionOutCreated?: Date,
		public admissionOutUserId?: number,
		public admissionOutCreatedFirstTime?: Date,
		public patient?: Patient,
		public outPat?: string,
		public altaPrevista?: Date,
		public altaPrevistaIA?: string,
		public altaPrevistaIAMin?: number,
		public altaPrevistaIAMax?: number,
		public altaPrevistaMI4U?: number,
		public obs?: string,
		public nomeMedicoResponsavel?: string,
		public contatoMedicoResponsavel?: string,
		public crmMedicoResponsavel?: string,
		public ufcrmMedicoResponsavel?: string,
		public especialidadeMedicoResponsavel?: string,
		public numeroGuia?: string,
		public userId?: number,
		public isEventoAdverso?: number,
		public eventoAdversoObs?: string,
		public isInternacaoDiaAnterior?: number,
		public carater?: string,
		public caraterTipo?: string,
		public hasPrenatal?: number,
		public isPregnant?: number,
		public isPuerperium?: number,
		public numeroSenha?: string,
		public clinicalHistory?: string,
		public dataEmissaoGuia?: Date,
		public supervisorId?: number,
		public palliativeCare?: number,
		public codigoMedicoResponsavel?: number,
		public conselhoMedicoResponsavel?: string,
		public dataHoraSolicitacao?: Date,
		public codIntegration?: string,
		public dataAuthorizationGuide?: Date,
		public realCost?: number,
		public codigoSituacaoTiss?: string,
		public descricaoSituacaoTiss?: string,
		public longaDecisao?: number,
		public prob?: number,
		public isEditableRealCost?: number,
		public curtaPermanencia?: number,
		public outDoctorName?: string,
		public outDoctorUfCrm?: string,
		public outDoctorCrm?: string,
		public outDoctorSpeciality?: string,
		public outDoctorIsResponsible?: number,
		public numeroRegistro?: string,
		public procedencia?: string,
		public curtaPermanenciaDate?: Date,
		public curtaPermanenciaUser?: number,
		public bedHospitals?: BedHospital[],
	) {}
}
