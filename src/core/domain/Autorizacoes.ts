import Operadoras from 'src/shared/operadoras.enum';
import { Patient } from './Patient';
import { AutorizacoesAuditStatus } from './AutorizacoesAuditStatus';
import { HospitalsCompany } from './HospitalsCompany';
import { MotivoNegativa } from './MotivoNegativa';

export class Autorizacoes {
	public constructor(
		public readonly id: number,
		public readonly dataCriacao?: Date,
		public readonly caraterInternacao?: string,
		public readonly dataSolicitacao?: Date,
		public readonly limitDate?: Date,
		public readonly pac?: string,
		public readonly nomePrestador?: string,
		public readonly regimeInternacao?: string,
		public readonly authorizationStatusIa?: string,
		public readonly authorizationMotivoIa?: string,
		public readonly authorizationDescriptionIa?: string,
		public readonly patient?: Patient,
		public readonly companyId?: Operadoras,
		public readonly numeroGuia?: string,
		public readonly cidPrincipal?: string,
		public readonly tipoGuia?: string,
		public readonly numeroReanalises?: number,
		public readonly transactionNumber?: string,
		public readonly dataVencimento?: Date,
		public readonly isFavorito?: boolean,
		public readonly sugestao?: string,
		public readonly hospitalCompany?: HospitalsCompany,
		public readonly motivoNegativa?: MotivoNegativa,
		public readonly enabled?: boolean,
		public readonly autorizacaoStatus?: AutorizacoesAuditStatus,
	) {}
}
