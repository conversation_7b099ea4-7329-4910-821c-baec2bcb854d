import { Hospitalization } from './Hospitalization';

export class FormCids {
	constructor(
		readonly id: number | null,
		readonly created: Date,
		readonly type: number,
		readonly status: number,
		readonly userId: number,
		readonly enabled: boolean,
		readonly isCenso: boolean,
		readonly patientWay: Hospitalization | Partial<Hospitalization>,
		readonly updated?: Date,
		readonly cidSubCategoriaId?: string,
		readonly cidCategoriaId?: string,
		readonly codIntegration?: string,
	) {}
}
