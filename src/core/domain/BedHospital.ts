import { Hospital } from './Hospital';

export class BedHospital {
	constructor(
		public id: number | null,
		readonly created: Date,
		readonly admissionIn: Date,
		readonly accommodation: string,
		readonly isCenso: boolean,
		readonly isTransfer: boolean,
		readonly price: number,
		readonly enabled: boolean,
		readonly hospital: Hospital | Partial<Hospital>,
		readonly updated?: Date,
		readonly changed?: Date,
		readonly admissionOut?: Date,
		readonly patientWayId?: number,
		readonly bedNumber?: string,
		readonly speciality?: string,
		readonly accommodationCustom?: string,
		readonly userId?: number,
		readonly accommodationIsolation?: string,
		readonly codIntegration?: number,
	) {}
}
