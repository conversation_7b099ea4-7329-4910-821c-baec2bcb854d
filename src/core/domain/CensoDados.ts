import { Censo } from './Censo';
import { TipoConflito } from './TipoConflito';

export class CensoDados {
	public constructor(
		public readonly id: number,
		public censoId: number,
		public companyId: number,
		public userId: number,
		public readonly dataCriacao: Date,
		public conflito: number,
		public readonly data: Date,
		public readonly municipio: string,
		public readonly hospitalCredenciado: string,
		public readonly controle: string,
		public readonly dtNascimento: Date,
		public readonly dataInternacao: Date,
		public readonly dataAlta: Date,
		public readonly motivoAlta: string,
		public readonly diagnostico: string,
		public readonly diagnosticoSecundario: string,
		public readonly previsaoAlta: Date,
		public readonly caraterInternacao: string,
		public readonly tipoInternacao: string,
		public readonly codigoGuia: string,
		public readonly altoCustoStatus: string,
		public readonly nomeBeneficiario: string,
		public readonly codBeneficiario: string,
		public readonly cidadeBeneficiario: string,
		public readonly estadoBeneficiario: string,
		public readonly recemNascido: boolean,
		public readonly tipoCliente: string,
		public readonly valorDiaria: number,
		public readonly regionalBeneficiario: string,
		public readonly tipoControle: string,
		public readonly diariasAutorizadas: number,
		public readonly codigoHospital: string,
		public readonly codigoPlano: string,
		public readonly nomePlano: string,
		public readonly codigoEmpresa: string,
		public readonly nomeEmpresa: string,
		public readonly statusPlano: string,
		public readonly dataPlanoDesde: Date,
		public patientId?: number,
		public readonly censo?: Partial<Censo>,
		public tiposConflitos?: TipoConflito[],
		public readonly acomodacao?: string,
	) {}
}
