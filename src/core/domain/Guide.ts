import { Hospital } from './Hospital';
import { Patient } from './Patient';
import { Hospitalization } from './Hospitalization';

export class Guide {
	constructor(
		public id: number | null,
		public patient: Patient | Partial<Patient>,
		public patientWay: Hospitalization | Partial<Hospitalization>,
		public hospital: Hospital | Partial<Hospital>,
		public created?: Date,
		public updated?: Date,
		public numeroSenha?: string,
		public numeroGuia?: string,
		public dataEmissaoGuia?: Date,
		public dataAutorizacaoGuia?: Date,
		public speciality?: string,
		public type?: number,
		public obs?: string,
		public obsDaily?: string,
		public obsProcedure?: string,
		public obsDrugs?: string,
		public obsMaterials?: string,
		public inicioPeriodo?: Date,
		public finalPeriodo?: Date,
		public valorTotal?: string,
		public enabled: number = 1,
		public userId?: number,
		public auditorId?: number,
		public tissLink?: string,
		public isViewed: number = 1,
		public valueRequest?: number,
		public valueSaved?: number,
		public valueAuthorized?: number,
		public isCenso: number = 0,
		public reanalysis: number = 0,
		public codIntegration?: string,
		public obsDailyProvider?: string,
		public obsProcedureProvider?: string,
		public obsDrugsProvider?: string,
		public obsMaterialsProvider?: string,
		public motivoNegativa?: string,
		public dataExpiracaoEdicao?: Date,
	) {}
}
