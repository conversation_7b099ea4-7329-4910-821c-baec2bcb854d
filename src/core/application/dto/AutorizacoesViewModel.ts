export class AutorizacoesViewModelDto {
	public constructor(
		public readonly id: number,
		public readonly nomePaciente: string,
		public readonly codBeneficiario: string,
		public readonly carater: string,
		public readonly dataPedido: string,
		public readonly dataLimite: Date,
		public readonly pac: string,
		public readonly prestador: string,
		public readonly regimeInternacao: string,
		public readonly plano: string,
		public readonly statusPlano: string,
		public readonly planoRegulamentado: boolean,
		public readonly statusIa: string,
		public readonly motivoIa: string,
		public readonly descricaoIa: string,
		public readonly idAutorizacao: number,
		public readonly idPaciente: number,
		public readonly idEmpresa: number,
		public readonly numeroGuia: string,
		public readonly cidPrincipal: string,
		public readonly tipoGuia: string,
		public readonly statusAuditoria: string,
		public readonly numeroReanalises: number,
		public readonly numeroTransacao: string,
		public readonly tipoGuia2: string,
		public readonly tags: string,
		public readonly dataVencimento: Date,
		public readonly empresa: string,
		public readonly status: string,
		public readonly statusDescricao: string,
		public readonly statusCor: string,
		public readonly dataCriacao: Date,
		public readonly nomeUnimed: string,
		public readonly pendencia: number,
		public readonly observacoes: string,
		public readonly motivoNegativa: string,
		public readonly isFavorito: boolean,
		public readonly isFavorito2: number,
		public readonly sugestao: string,
		public readonly ultimoAuditou: string,
		public readonly dataUltimoAuditou: Date,
	) {}
}
