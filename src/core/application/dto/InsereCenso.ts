import { File } from 'src/core/domain/File';

export class InsereCenso {
	constructor(
		public readonly dataCriacao: Date,
		public readonly companyId: number,
		public readonly userId: number,
		public readonly diretorioSalvo: string,
		public readonly nomeArquivo: string,
		public readonly hashArquivo: string,
		public totalLinhas?: number,
	) {}

	static fromFile(file: File): InsereCenso {
		return new InsereCenso(
			new Date(),
			file.companyId,
			file.userId,
			file.caminhoExterno,
			file.nome,
			file.hash,
			file.totalLinhas,
		);
	}
}
