import { ConflictValidationPolicy } from '../../abstract/conflict.validation.policy';
import { StrategyManager } from '../../../strategy/manager.strategy';
import { ConflictValidationFactoryGateway } from 'src/core/application/gateway/validation/conflictValidation.factory.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { ConflictValidationStrategy } from '../../../strategy/abstract/conflict.validation.strategy';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { CadastroInternacaoFuturaSemAltaConflictValidationStrategy } from '../../../strategy/impl/cadastroInternacaoFuturaSemAltaConflict.validation.strategy';
import { DataNascimentoFuturoConflictValidationStrategy } from '../../../strategy/impl/dataNascimentoFuturoConflict.validation.strategy';
import { DataEntradaSuperiorAltaConflictValidationStrategy } from '../../../strategy/impl/dataEntradaSuperiorAltaConflict.validation.strategy';
import { PeriodoEntreInternacaoConflictValidationStrategy } from '../../../strategy/impl/periodoEntreInternacaoConflict.validation.strategy';
import { DataEntradaInferiorDataNascimentoConflictValidationStrategy } from '../../../strategy/impl/dataEntradaInferiorDataNascimentoConflict.validation.strategy';
import { MesmoCodigoGuiaInternacaoConflictValidationStrategy } from '../../../strategy/impl/mesmoCodigoGuiaInternacaoConflict.validation.strategy';

export class SSUConflictValidationPolicy implements ConflictValidationPolicy {
	constructor(
		private readonly censoDados: CensoDados,
		private readonly conflictValidationFactoryGateway: ConflictValidationFactoryGateway,
	) {}

	public async validate(): Promise<TipoConflito[]> {
		return await this.validateConflict();
	}

	private async validateConflict(): Promise<TipoConflito[]> {
		const strategies = new Map<string, ConflictValidationStrategy>();
		const conflictList: TipoConflito[] = [];
		this.setStrategies(strategies, conflictList);
		const strategyManager = new StrategyManager(strategies);
		await strategyManager.traverseStrategies();
		return conflictList;
	}

	private setStrategies(
		strategies: Map<string, ConflictValidationStrategy>,
		conflictList: TipoConflito[],
	): void {
		// strategies.set(
		// 	CodigoBeneficiarioDuplicadoConflictValidationStrategy.name,
		// 	new CodigoBeneficiarioDuplicadoConflictValidationStrategy(
		// 		this.censoDados,
		// 		this.conflictValidationFactoryGateway,
		// 		conflictList,
		// 	),
		// );
		if (!this.censoDados.patientId) return;
		strategies.set(
			CadastroInternacaoFuturaSemAltaConflictValidationStrategy.name,
			new CadastroInternacaoFuturaSemAltaConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
		strategies.set(
			DataEntradaInferiorDataNascimentoConflictValidationStrategy.name,
			new DataEntradaInferiorDataNascimentoConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
		strategies.set(
			DataNascimentoFuturoConflictValidationStrategy.name,
			new DataNascimentoFuturoConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
		strategies.set(
			DataEntradaSuperiorAltaConflictValidationStrategy.name,
			new DataEntradaSuperiorAltaConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
		strategies.set(
			MesmoCodigoGuiaInternacaoConflictValidationStrategy.name,
			new MesmoCodigoGuiaInternacaoConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
		// strategies.set(
		// 	NomeNascimentoConflictValidationStrategy.name,
		// 	new NomeNascimentoConflictValidationStrategy(
		// 		this.censoDados,
		// 		this.conflictValidationFactoryGateway,
		// 		conflictList,
		// 	),
		// );
		strategies.set(
			PeriodoEntreInternacaoConflictValidationStrategy.name,
			new PeriodoEntreInternacaoConflictValidationStrategy(
				this.censoDados,
				this.conflictValidationFactoryGateway,
				conflictList,
			),
		);
	}
}
