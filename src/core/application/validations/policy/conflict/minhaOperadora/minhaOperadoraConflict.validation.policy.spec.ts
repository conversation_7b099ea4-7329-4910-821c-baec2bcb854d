import { StrategyManager } from '../../../strategy/manager.strategy';
import { ConflictValidationFactoryGateway } from 'src/core/application/gateway/validation/conflictValidation.factory.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { MinhaOperadoraConflictValidationPolicy } from './minhaOperadoraConflict.validation.policy';

jest.mock('../../../strategy/manager.strategy');

describe('MinhaOperadoraConflictValidationPolicy', () => {
	let minhaOperdoraConflictValidationPolicy: MinhaOperadoraConflictValidationPolicy;
	let censoDados: CensoDados;
	let conflictValidationGateway: jest.Mocked<ConflictValidationFactoryGateway>;
	let strategyManagerMock: jest.Mocked<StrategyManager>;

	beforeEach(() => {
		censoDados = new CensoDados(
			1, // id
			2, // censoId
			3, // companyId
			1, //userId
			new Date(), // dataCriacao
			1, // conflito
			new Date(), // data
			'São Paulo', // municipio
			'Hospital São Luiz', // hospitalCredenciado
			'Controle A', // controle
			new Date('2000-01-01'), // dtNascimento
			new Date('2024-10-10'), // dataInternacao
			null, // dataAlta
			'', // motivoAlta
			'Diagnóstico A', // diagnostico
			'', // diagnosticoSecundario
			null, // previsaoAlta
			'Eletiva', // caraterInternacao
			'Clínica', // tipoInternacao
			'12345', // codigoGuia
			'', // altoCustoStatus
			'João Silva', // nomeBeneficiario
			'*********', // codBeneficiario
			'Campinas', // cidadeBeneficiario
			'SP', // estadoBeneficiario
			false, // recemNascido
			'', // tipoCliente
			0, // valorDiaria
			'', // regionalBeneficiario
			'', // tipoControle
			3, // diariasAutorizadas
			'123', // codigoHospital
			'456', // codigoPlano
			'Plano A', // nomePlano
			'789', // codigoEmpresa
			'Empresa X', // nomeEmpresa
			'Ativo', // statusPlano
			new Date('2022-01-01'),
			1, // patientId
		);
		conflictValidationGateway =
			{} as unknown as jest.Mocked<ConflictValidationFactoryGateway>;

		strategyManagerMock = {
			traverseStrategies: jest.fn().mockResolvedValue(undefined),
		} as unknown as jest.Mocked<StrategyManager>;

		(StrategyManager as jest.Mock).mockImplementation(
			() => strategyManagerMock,
		);

		minhaOperdoraConflictValidationPolicy =
			new MinhaOperadoraConflictValidationPolicy(
				censoDados,
				conflictValidationGateway,
			);
	});

	it('deve iniciar a validação de conflito do censo', async () => {
		await minhaOperdoraConflictValidationPolicy.validate();

		expect(strategyManagerMock.traverseStrategies).toHaveBeenCalled();
	});

	it('deve retornar nada se não tiver um patient id', async () => {
		censoDados.patientId = null;

		await minhaOperdoraConflictValidationPolicy.validate();

		expect(strategyManagerMock.traverseStrategies.length).toBe(0);
	});
});
