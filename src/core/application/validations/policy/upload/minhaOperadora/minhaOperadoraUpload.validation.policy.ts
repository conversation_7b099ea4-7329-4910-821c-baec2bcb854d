import { File } from 'src/core/domain/File';
import { UploadValidationPolicy } from '../../abstract/upload.validation.policy';
import { HeaderValidation } from '../../../header.validation';
import * as fs from 'fs';
import * as readline from 'readline';
import { expectedHeadersMinhaOperadora } from 'src/helpers/headers/expectedHeaderMinhaOperadora.helper';

export class MinhaOperadoraUploadValidationPolicy
	implements UploadValidationPolicy
{
	constructor(private readonly file: File) {}

	public async validate(): Promise<void> {
		await this.validateHeaderCenso();
	}

	private async validateHeaderCenso(): Promise<void> {
		const headerValidation = new HeaderValidation(
			this.file.caminhoInterno,
			expectedHeadersMinhaOperadora,
			fs,
			readline,
		);
		await headerValidation.validate();
	}
}
