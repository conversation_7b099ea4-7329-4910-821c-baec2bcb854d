import { File } from 'src/core/domain/File';
import { HeaderValidation } from '../../../header.validation';
import { InvalidHeaderException } from 'src/shared/exceptions/upload/InvalidHeader.exception';
import { UVXUploadValdidationPolicy } from './UVXUpload.validation.policy';

jest.mock('../../../header.validation');

describe('MinhaOperadoraValidationPolicy', () => {
	let uvxUploadValidationPolicy: UVXUploadValdidationPolicy;
	let mockFile: File;

	beforeEach(() => {
		mockFile = new File(
			'test.csv',
			'text/csv',
			100,
			'/path/to/test.csv',
			10,
			null,
			'hash_value',
		);
		uvxUploadValidationPolicy = new UVXUploadValdidationPolicy(mockFile);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('should pass validation when header contains all expected columns', async () => {
		(HeaderValidation.prototype.validate as jest.Mock).mockResolvedValueOnce(
			undefined,
		);

		await expect(uvxUploadValidationPolicy.validate()).resolves.not.toThrow();
		expect(HeaderValidation.prototype.validate).toHaveBeenCalledTimes(1);
	});

	it('should throw InvalidHeaderException when header is missing expected columns', async () => {
		(HeaderValidation.prototype.validate as jest.Mock).mockRejectedValueOnce(
			new InvalidHeaderException(400, 'Missing headers', ['data']),
		);

		await expect(uvxUploadValidationPolicy.validate()).rejects.toThrow(
			InvalidHeaderException,
		);
		expect(HeaderValidation.prototype.validate).toHaveBeenCalledTimes(1);
	});
});
