import { HeaderValidation } from '../../../header.validation';
import { UploadValidationPolicy } from '../../abstract/upload.validation.policy';
import { File } from 'src/core/domain/File';
import * as fs from 'fs';
import * as readline from 'readline';
import { expectedHeadersUVX } from 'src/helpers/headers/expectedHeaderUVX.helper';

export class UVXUploadValdidationPolicy implements UploadValidationPolicy {
	constructor(private readonly file: File) {}

	public async validate(): Promise<void> {
		await this.validateHeaderCenso();
	}

	private async validateHeaderCenso(): Promise<void> {
		const headerValidation = new HeaderValidation(
			this.file.caminhoInterno,
			expectedHeadersUVX,
			fs,
			readline,
		);
		await headerValidation.validate();
	}
}
