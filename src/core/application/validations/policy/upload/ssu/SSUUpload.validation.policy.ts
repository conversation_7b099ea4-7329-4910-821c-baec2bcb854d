import { File } from 'src/core/domain/File';
import { UploadValidationPolicy } from '../../abstract/upload.validation.policy';
import { expectedHeadersSSU } from 'src/helpers/headers/expectedHeaderSSU.helper';
import { HeaderValidation } from '../../../header.validation';
import * as fs from 'fs';
import * as readline from 'readline';

export class SSUUploadValidationPolicy implements UploadValidationPolicy {
	constructor(private readonly file: File) {}

	public async validate(): Promise<void> {
		await this.validateHeaderCenso();
	}

	private async validateHeaderCenso(): Promise<void> {
		const headerValidation = new HeaderValidation(
			this.file.caminhoInterno,
			expectedHeadersSSU,
			fs,
			readline,
		);
		await headerValidation.validate();
	}
}
