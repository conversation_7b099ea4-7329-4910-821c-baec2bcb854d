import { HeaderValidation } from '../../../header.validation';
import { UploadValidationPolicy } from '../../abstract/upload.validation.policy';
import { File } from 'src/core/domain/File';
import * as fs from 'fs';
import * as readline from 'readline';
import { expectedHeadersSFO } from 'src/helpers/headers/expectedHeaderSFO.helper';

export class SFOUploadValidationPolicy implements UploadValidationPolicy {
	constructor(private readonly file: File) {}

	public async validate(): Promise<void> {
		await this.validateHeaderCenso();
	}

	private async validateHeaderCenso(): Promise<void> {
		const headerValidation = new HeaderValidation(
			this.file.caminhoInterno,
			expectedHeadersSFO,
			fs,
			readline,
		);
		await headerValidation.validate();
	}
}
