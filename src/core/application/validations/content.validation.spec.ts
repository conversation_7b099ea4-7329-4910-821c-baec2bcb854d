import { ContentValidation } from './content.validation';

describe('ContentValidation', () => {
	it('should pass validate with all required fields', () => {
		const contentValidation = new ContentValidation(['field1', 'field2']);
		const row = { field1: 'value1', field2: 'value2' };

		expect(() => contentValidation.validate(row)).not.toThrow();
	});

	it('should throw InvalidContentException with missing required fields', () => {
		const contentValidation = new ContentValidation(['field1', 'field2']);
		const row = { field1: 'value1' };

		expect(() => contentValidation.validate(row)).toThrow(
			'Por favor, verifique a coluna que não foi preenchida e preencha-a antes de importar o arquivo novamente.',
		);
	});
});
