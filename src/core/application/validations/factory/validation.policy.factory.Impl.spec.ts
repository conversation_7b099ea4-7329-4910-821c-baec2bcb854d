import { File } from 'src/core/domain/File';
import { ValidationPolicyFactoryImpl } from './validation.policy.factory.Impl';
import { CensoDados } from 'src/core/domain/CensoDados';
import { SSUUploadValidationPolicy } from '../policy/upload/ssu/SSUUpload.validation.policy';
import { MinhaOperadoraUploadValidationPolicy } from '../policy/upload/minhaOperadora/minhaOperadoraUpload.validation.policy';
import { SSUConflictValidationPolicy } from '../policy/conflict/ssu/SSUConflict.validation.policy';
import { MinhaOperadoraConflictValidationPolicy } from '../policy/conflict/minhaOperadora/minhaOperadoraConflict.validation.policy';
import { UVXUploadValdidationPolicy } from '../policy/upload/uvx/UVXUpload.validation.policy';
import { UVXConflictValidationPolicy } from '../policy/conflict/uvx/UVXConflict.validation.policy';
import { SFOUploadValidationPolicy } from '../policy/upload/sfo/SFOUpload.validation.policy';

describe('ValidationPolicyFactoryImpl', () => {
	let factory: ValidationPolicyFactoryImpl;
	let mockFile: File;
	let censoDados: CensoDados;
	beforeEach(() => {
		factory = new ValidationPolicyFactoryImpl();
		mockFile = new File(
			'test.csv',
			'text/csv',
			100,
			'/path/to/test.csv',
			10,
			null,
			'hash_value',
		);

		censoDados = new CensoDados(
			1, // id
			2, // censoId
			10, // companyId,
			1, //userId
			new Date(), // dataCriacao
			1, // conflito
			new Date(), // data
			'São Paulo', // municipio
			'Hospital São Luiz', // hospitalCredenciado
			'Controle A', // controle
			new Date('2000-01-01'), // dtNascimento
			new Date('2024-10-10'), // dataInternacao
			null, // dataAlta
			'', // motivoAlta
			'Diagnóstico A', // diagnostico
			'', // diagnosticoSecundario
			null, // previsaoAlta
			'Eletiva', // caraterInternacao
			'Clínica', // tipoInternacao
			'12345', // codigoGuia
			'', // altoCustoStatus
			'João Silva', // nomeBeneficiario
			'*********', // codBeneficiario
			'Campinas', // cidadeBeneficiario
			'SP', // estadoBeneficiario
			false, // recemNascido
			'', // tipoCliente
			0, // valorDiaria
			'', // regionalBeneficiario
			'', // tipoControle
			3, // diariasAutorizadas
			'123', // codigoHospital
			'456', // codigoPlano
			'Plano A', // nomePlano
			'789', // codigoEmpresa
			'Empresa X', // nomeEmpresa
			'Ativo', // statusPlano
			new Date('2022-01-01'),
		);
	});

	it.each([
		[10, SSUUploadValidationPolicy],
		[3, MinhaOperadoraUploadValidationPolicy],
		[22, UVXUploadValdidationPolicy],
		[2, SFOUploadValidationPolicy],
		[999999, MinhaOperadoraUploadValidationPolicy],
	])(
		'deve retornar o UploadValidationPolicy correto para companyId %i',
		(companyId, expectedPolicy) => {
			Reflect.set(mockFile, 'companyId', companyId);
			const policy = factory.createUploadValidationFactory(mockFile);
			expect(policy).toBeInstanceOf(expectedPolicy);
		},
	);

	it.each([
		[10, SSUConflictValidationPolicy],
		[3, MinhaOperadoraConflictValidationPolicy],
		[22, UVXConflictValidationPolicy],
		[99999, MinhaOperadoraConflictValidationPolicy],
	])(
		'deve retornar um ConflictValidationPolicy para companyId valido',
		(companyId, conflictValidationPolicy) => {
			Reflect.set(censoDados, 'companyId', companyId);
			const policy = factory.createConflictValidationFactory(censoDados);
			expect(policy).toHaveProperty('validate');
			expect(typeof policy.validate).toBe('function');
			expect(policy).toBeInstanceOf(conflictValidationPolicy);
		},
	);
});
