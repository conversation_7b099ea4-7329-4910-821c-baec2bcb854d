import { File } from 'src/core/domain/File';
import { UploadValidationPolicy } from '../../policy/abstract/upload.validation.policy';
import { ConflictValidationPolicy } from '../../policy/abstract/conflict.validation.policy';
import { CensoDados } from 'src/core/domain/CensoDados';

export interface ValidationPolicyFactory {
	createUploadValidationFactory(file: File): UploadValidationPolicy;

	createConflictValidationFactory(
		censoDados: CensoDados,
	): ConflictValidationPolicy;
}
