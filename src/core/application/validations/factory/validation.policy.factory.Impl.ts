import { File } from 'src/core/domain/File';
import { SSUUploadValidationPolicy } from '../policy/upload/ssu/SSUUpload.validation.policy';
import { UploadValidationPolicy } from '../policy/abstract/upload.validation.policy';
import { ValidationPolicyFactory } from './abstract/validation.policy.factory';
import { ConflictValidationPolicy } from '../policy/abstract/conflict.validation.policy';
import { SSUConflictValidationPolicy } from '../policy/conflict/ssu/SSUConflict.validation.policy';
import { ConflictValidationFactoryGateway } from '../../gateway/validation/conflictValidation.factory.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { MinhaOperadoraUploadValidationPolicy } from '../policy/upload/minhaOperadora/minhaOperadoraUpload.validation.policy';
import { MinhaOperadoraConflictValidationPolicy } from '../policy/conflict/minhaOperadora/minhaOperadoraConflict.validation.policy';
import { UVXUploadValdidationPolicy } from '../policy/upload/uvx/UVXUpload.validation.policy';
import { UVXConflictValidationPolicy } from '../policy/conflict/uvx/UVXConflict.validation.policy';
import Operadoras from 'src/shared/operadoras.enum';
import { SFOUploadValidationPolicy } from '../policy/upload/sfo/SFOUpload.validation.policy';

export class ValidationPolicyFactoryImpl implements ValidationPolicyFactory {
	constructor(
		private readonly conflictValidationFactoryGateway?: ConflictValidationFactoryGateway,
	) {}

	public createUploadValidationFactory(file: File): UploadValidationPolicy {
		switch (file.companyId.toString()) {
			case Operadoras.SEGUROS_UNIMED.toString():
				return new SSUUploadValidationPolicy(file);
			case Operadoras.MINHA_OPERADORA.toString():
				return new MinhaOperadoraUploadValidationPolicy(file);
			case Operadoras.UNIMED_VITORIA.toString():
				return new UVXUploadValdidationPolicy(file);
			case Operadoras.SAO_FRANCISCO.toString():
				return new SFOUploadValidationPolicy(file);
			default:
				return new MinhaOperadoraUploadValidationPolicy(file);
		}
	}

	public createConflictValidationFactory(
		censoDados: CensoDados,
	): ConflictValidationPolicy {
		switch (censoDados.companyId) {
			case Operadoras.SEGUROS_UNIMED:
				return new SSUConflictValidationPolicy(
					censoDados,
					this.conflictValidationFactoryGateway,
				);
			case Operadoras.MINHA_OPERADORA:
				return new MinhaOperadoraConflictValidationPolicy(
					censoDados,
					this.conflictValidationFactoryGateway,
				);
			case Operadoras.UNIMED_VITORIA:
				return new UVXConflictValidationPolicy(
					censoDados,
					this.conflictValidationFactoryGateway,
				);
			default:
				return new MinhaOperadoraConflictValidationPolicy(
					censoDados,
					this.conflictValidationFactoryGateway,
				);
		}
	}
}
