import { HeaderValidation } from './header.validation';
import readline from 'readline';
import fs from 'fs';
import { InvalidHeaderException } from 'src/shared/exceptions/upload/InvalidHeader.exception';
import { sanitizeHeader } from 'src/helpers/sanitizeHeader.helper';
jest.mock('fs');
jest.mock('readline');

describe('HeaderValidation', () => {
	const filePath = '/path/to/file.csv';
	const expectedHeader = ['Column1', 'Column2', 'Column3'];
	const sanitizedExpectedHeader = expectedHeader.map(sanitizeHeader);
	let mockFs: jest.Mocked<typeof fs>;
	let mockReadline: jest.Mocked<typeof readline>;
	let headerValidation: HeaderValidation;
	let mockFileStream: jest.Mocked<fs.ReadStream>;

	beforeEach(() => {
		mockFs = {
			createReadStream: jest.fn(),
		} as unknown as jest.Mocked<typeof fs>;

		mockReadline = {
			createInterface: jest.fn(),
		} as unknown as jest.Mocked<typeof readline>;

		headerValidation = new HeaderValidation(
			filePath,
			sanitizedExpectedHeader,
			mockFs,
			mockReadline,
		);

		mockFileStream = {
			close: jest.fn(),
		} as unknown as jest.Mocked<fs.ReadStream>;
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('deve passar na validação quando o cabeçalho contiver todas as colunas esperadas', async () => {
		const headerLine = 'Column1,Column2,Column3';
		const mockReadlineInterface = {
			on: jest.fn(
				(
					event: 'line' | 'error' | 'close',
					callback: (line: string) => void,
				) => {
					if (event === 'line') callback(headerLine);
					return mockReadlineInterface;
				},
			) as jest.Mock,
			close: jest.fn(),
		} as unknown as jest.Mocked<readline.Interface>;

		mockFs.createReadStream.mockReturnValueOnce({} as fs.ReadStream);
		mockReadline.createInterface.mockReturnValueOnce(mockReadlineInterface);

		await expect(headerValidation.validate()).resolves.not.toThrow();

		expect(mockReadlineInterface.on).toHaveBeenCalledWith(
			'line',
			expect.any(Function),
		);
	});

	it('deve corretamente detectar os delimitadores e parsear o header', async () => {
		const semicolonHeaderLine = 'Column1;Column2;Column3';
		const commaHeaderLine = 'Column1,Column2,Column3';

		const mockReadlineInterface = {
			on: jest.fn((event: string, callback: (...args: string[]) => void) => {
				if (event === 'line') {
					callback(semicolonHeaderLine);
					callback(commaHeaderLine);
				}
				if (event === 'close') callback();
				return mockReadlineInterface;
			}),
			close: jest.fn(),
		} as unknown as jest.Mocked<readline.Interface>;

		mockFs.createReadStream.mockReturnValueOnce(mockFileStream);
		mockReadline.createInterface.mockReturnValueOnce(mockReadlineInterface);

		const headerValidation = new HeaderValidation(
			filePath,
			sanitizedExpectedHeader,
			mockFs,
			mockReadline,
		);

		await expect(headerValidation.validate()).resolves.not.toThrow();

		expect(mockReadlineInterface.on).toHaveBeenCalledWith(
			'line',
			expect.any(Function),
		);
		expect(mockFileStream.close).toHaveBeenCalled();
	});

	it('deve lançar uma exceção InvalidHeaderException quando o cabeçalho estiver faltando colunas esperadas', async () => {
		const headerLine = 'Column1,Column2';

		const mockReadlineInterface = {
			on: jest.fn((event: string, callback: (...args: string[]) => void) => {
				if (event === 'line') callback(headerLine);
				return mockReadlineInterface;
			}) as unknown as jest.Mock,
			close: jest.fn(),
		} as unknown as jest.Mocked<readline.Interface>;

		mockFs.createReadStream.mockReturnValueOnce({} as fs.ReadStream);
		mockReadline.createInterface.mockReturnValueOnce(mockReadlineInterface);

		await expect(headerValidation.validate()).rejects.toThrow(
			InvalidHeaderException,
		);

		expect(mockReadlineInterface.on).toHaveBeenCalledWith(
			'line',
			expect.any(Function),
		);
	});

	it('deve lidar com erros de leitura de arquivo de forma adequada', async () => {
		const error = new Error('Erro na leitura do arquivo');

		const mockReadlineInterface = {
			on: jest.fn((event: string, callback: (...args: Error[]) => void) => {
				if (event === 'error') callback(error);
				return mockReadlineInterface;
			}) as unknown as jest.Mock,
			close: jest.fn(),
		} as unknown as jest.Mocked<readline.Interface>;

		mockFs.createReadStream.mockReturnValueOnce({} as fs.ReadStream);
		mockReadline.createInterface.mockReturnValueOnce(mockReadlineInterface);

		await expect(headerValidation.validate()).rejects.toThrow(
			'Erro na leitura do arquivo',
		);
	});

	it('deve garantir que o método fileStream.close seja chamado após o evento close', async () => {
		const headerLine = 'Column1,Column2,Column3';

		const mockReadlineInterface = {
			on: jest.fn(
				(
					event: 'line' | 'error' | 'close',
					callback: (line: string) => void,
				) => {
					if (event === 'line') callback(headerLine);
					if (event === 'close') callback(null);
					return mockReadlineInterface;
				},
			) as jest.Mock,
			close: jest.fn(),
		} as unknown as jest.Mocked<readline.Interface>;

		mockFs.createReadStream.mockReturnValueOnce(mockFileStream);
		mockReadline.createInterface.mockReturnValueOnce(mockReadlineInterface);

		const headerValidation = new HeaderValidation(
			filePath,
			sanitizedExpectedHeader,
			mockFs,
			mockReadline,
		);

		await expect(headerValidation.validate()).resolves.not.toThrow();

		expect(mockReadlineInterface.close).toHaveBeenCalled();

		expect(mockFileStream.close).toHaveBeenCalled();
	});
});
