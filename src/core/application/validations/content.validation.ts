import { InvalidContentException } from 'src/shared/exceptions/upload/InvalidContent.exception';

export class ContentValidation {
	constructor(private readonly requiredFields: string[]) {}

	public validate(row: Record<string, string | undefined>): void {
		const missingFields = this.requiredFields.filter((field) => !row[field]);
		if (missingFields.length > 0) {
			throw new InvalidContentException(
				400,
				`Por favor, verifique a coluna que não foi preenchida e preencha-a antes de importar o arquivo novamente.`,
				missingFields,
			);
		}
	}
}
