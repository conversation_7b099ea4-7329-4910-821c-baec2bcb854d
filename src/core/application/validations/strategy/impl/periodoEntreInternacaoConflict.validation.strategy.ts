import { CensoDados } from 'src/core/domain/CensoDados';
import { ConflictValidationStrategy } from '../abstract/conflict.validation.strategy';
import { ConflictValidationFactoryGateway } from 'src/core/application/gateway/validation/conflictValidation.factory.gateway';
import { TipoConflito } from 'src/core/domain/TipoConflito';

export class PeriodoEntreInternacaoConflictValidationStrategy
	implements ConflictValidationStrategy
{
	constructor(
		private readonly censoDados: CensoDados,
		private readonly conflictValidationFactoryGateway: ConflictValidationFactoryGateway,
		private readonly conflictList: TipoConflito[],
	) {}

	public async validate(): Promise<TipoConflito[]> {
		const conflictGateway = this.conflictValidationFactoryGateway.execute(
			this.censoDados,
			PeriodoEntreInternacaoConflictValidationStrategy.name,
		);
		this.conflictList.push(await conflictGateway.validate());
		return this.conflictList;
	}
}
