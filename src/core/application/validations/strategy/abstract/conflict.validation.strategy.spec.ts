import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { CadastroInternacaoFuturaSemAltaConflictValidationStrategy } from '../impl/cadastroInternacaoFuturaSemAltaConflict.validation.strategy';
import { DataEntradaInferiorDataNascimentoConflictValidationStrategy } from '../impl/dataEntradaInferiorDataNascimentoConflict.validation.strategy';
import { DataNascimentoFuturoConflictValidationStrategy } from '../impl/dataNascimentoFuturoConflict.validation.strategy';
import { DataEntradaSuperiorAltaConflictValidationStrategy } from '../impl/dataEntradaSuperiorAltaConflict.validation.strategy';
import { MesmoCodigoGuiaInternacaoConflictValidationStrategy } from '../impl/mesmoCodigoGuiaInternacaoConflict.validation.strategy';
import { NomeNascimentoConflictValidationStrategy } from '../impl/nomeNascimentoConflict.validation.strategy';
import { PeriodoEntreInternacaoConflictValidationStrategy } from '../impl/periodoEntreInternacaoConflict.validation.strategy';
import { CodigoBeneficiarioDuplicadoConflictValidationStrategy } from '../impl/codigoBeneficiarioDuplicadoConflict.validation.strategy';

describe('ConflictValidationStrategy', () => {
	// Mock do CensoDados
	const censoDadosMock = new CensoDados(
		1, // id
		1, // censoId
		1, // companyId
		1, // userId
		new Date('2024-10-08'), // dataCriacao
		1, // conflito
		new Date('2024-10-09'), // data
		'São Paulo', // municipio
		'Hospital São Luiz', // hospitalCredenciado
		'Controle A', // controle
		new Date('1990-01-01'), // dtNascimento
		new Date('2024-10-07'), // dataInternacao
		null, // dataAlta
		'', // motivoAlta
		'Diagnostico A', // diagnostico
		'Diagnostico Secundário A', // diagnosticoSecundario
		null, // previsaoAlta
		'Eletiva', // caraterInternacao
		'Clínica', // tipoInternacao
		'123456', // codigoGuia
		'', // altoCustoStatus
		'Maria Souza', // nomeBeneficiario
		'*********', // codBeneficiario
		'Campinas', // cidadeBeneficiario
		'SP', // estadoBeneficiario
		false, // recemNascido
		'', // tipoCliente
		0, // valorDiaria
		'', // regionalBeneficiario
		'', // tipoControle
		3, // diariasAutorizadas
		'123', // codigoHospital
		'456', // codigoPlano
		'Plano A', // nomePlano
		'789', // codigoEmpresa
		'Empresa A', // nomeEmpresa
		'Ativo', // statusPlano
		new Date('2022-01-01'), // dataPlanoDesde
	);

	const conflictValidationGatewayMock = {
		validate: jest.fn(),
	};

	const conflictValidationFactoryGatewayMock = {
		execute: jest.fn().mockReturnValue(conflictValidationGatewayMock),
	};

	const conflictList: TipoConflito[] = [];
	const strategys = [
		new CodigoBeneficiarioDuplicadoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new CadastroInternacaoFuturaSemAltaConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new DataEntradaInferiorDataNascimentoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new DataNascimentoFuturoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new DataEntradaSuperiorAltaConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new MesmoCodigoGuiaInternacaoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new NomeNascimentoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
		new PeriodoEntreInternacaoConflictValidationStrategy(
			censoDadosMock,
			conflictValidationFactoryGatewayMock,
			conflictList,
		),
	];

	it.each(strategys)(
		'deve chamar o gateway ao iniciar a validação do %s',
		async (strategy) => {
			await strategy.validate();
			expect(conflictValidationFactoryGatewayMock.execute).toHaveBeenCalled();
			expect(conflictValidationFactoryGatewayMock.execute).toHaveBeenCalledWith(
				censoDadosMock,
				strategy.constructor.name,
			);
		},
	);
});
