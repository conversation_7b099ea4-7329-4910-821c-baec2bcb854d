import { ConflictValidationStrategy } from './abstract/conflict.validation.strategy';
import { StrategyManager } from './manager.strategy';

describe('StrategyManager', () => {
	let strategyManager: StrategyManager;
	let strategyMock1: ConflictValidationStrategy;
	let strategyMock2: ConflictValidationStrategy;

	beforeEach(() => {
		strategyMock1 = {
			validate: jest.fn(),
		} as unknown as ConflictValidationStrategy;
		strategyMock2 = {
			validate: jest.fn(),
		} as unknown as ConflictValidationStrategy;

		const strategiesMap = new Map<string, ConflictValidationStrategy>();
		strategiesMap.set('strategy1', strategyMock1);
		strategiesMap.set('strategy2', strategyMock2);

		strategyManager = new StrategyManager(strategiesMap);
	});

	describe('traverseStrategies', () => {
		it('should call validate on all strategies in the map', async () => {
			await strategyManager.traverseStrategies();

			expect(strategyMock1.validate).toHaveBeenCalled();
			expect(strategyMock2.validate).toHaveBeenCalled();
		});

		it('should not throw error when no strategies are provided', async () => {
			const emptyStrategyManager = new StrategyManager(new Map());

			await expect(
				emptyStrategyManager.traverseStrategies(),
			).resolves.not.toThrow();
		});
	});
});
