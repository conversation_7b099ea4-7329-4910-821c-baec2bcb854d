import { InvalidHeaderException } from 'src/shared/exceptions/upload/InvalidHeader.exception';
import * as fs from 'fs';
import * as readline from 'readline';
import { sanitizeHeader } from 'src/helpers/sanitizeHeader.helper';

export class HeaderValidation {
	constructor(
		private readonly filePath: string,
		private readonly expectedHeader: string[],
		private readonly fsModule: typeof fs,
		private readonly readlineModule: typeof readline,
	) {}

	public async validate(): Promise<void> {
		const fileHeaders = (await this.getHeaderFromFile(this.filePath)).map(
			(header) => sanitizeHeader(header),
		);

		const missingHeaders = this.expectedHeader.filter((expectedHeader) => {
			return !fileHeaders.includes(sanitizeHeader(expectedHeader));
		});

		if (missingHeaders.length > 0) {
			throw new InvalidHeaderException(
				400,
				`Os títulos ${missingHeaders.map((header) => `"${header}"`).join(', ')} estão faltando. Verifique os títulos e colunas necessárias em nosso modelo`,
				missingHeaders,
			);
		}
	}

	private getHeaderFromFile(filePath: string): Promise<string[]> {
		return new Promise((resolve, reject) => {
			const fileStream = this.fsModule.createReadStream(filePath, {
				encoding: 'utf-8',
			});
			const rl = this.readlineModule.createInterface({
				input: fileStream,
				crlfDelay: Infinity,
			});

			rl.on('line', (line) => {
				rl.close();

				const delimiter = line.includes(';') ? ';' : ',';
				const headers = line.split(delimiter).map((header) => header.trim());
				resolve(headers);
			});

			rl.on('error', (error) => {
				reject(error);
			});

			rl.on('close', () => {
				fileStream.close();
			});
		});
	}
}
