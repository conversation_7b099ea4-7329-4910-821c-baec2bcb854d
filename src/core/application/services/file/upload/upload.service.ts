import { File } from 'src/core/domain/File';

export abstract class UploadService {
	public async execute(file: File): Promise<number> {
		await this.validate(file);
		await this.upload(file);
		return await this.persistFile(file);
	}

	protected abstract validate(file: File): Promise<void>;

	protected abstract deleteFile(filePath: string): Promise<void>;

	protected abstract upload(file: File): Promise<void>;

	protected abstract persistFile(file: File): Promise<number>;
}
