import { UploadCensoService } from './uploadCenso.service';
import { File } from 'src/core/domain/File';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { FileParserValidatorGateway } from 'src/core/application/gateway/parser/fileParserValidator.gateway';
import { UploadFileGateway } from 'src/core/application/gateway/file/upload/uploadFile.gateway';
import { InsertCensoService } from '../../../censo/insertCenso.service';
import MockDate from 'mockdate';
import { InsereCenso } from 'src/core/application/dto/InsereCenso';
import { DeleteFileService } from '../../deleteFile.service';

describe('UploadCensoService', () => {
	let mockFile: File;
	let uploadCensoService: UploadCensoService.UploadCensoService;
	let mockValidationPolicyFactory: jest.Mocked<ValidationPolicyFactory>;
	let mockFileParserValidatorGateway: jest.Mocked<FileParserValidatorGateway>;
	let mockUploadFileGateway: jest.Mocked<UploadFileGateway>;
	let mockInsertCensoService: jest.Mocked<InsertCensoService.InsertCensoService>;
	let mockDeleteFileService: jest.Mocked<DeleteFileService.DeleteFileService>;
	beforeEach(() => {
		mockFile = new File(
			'test-file.csv',
			'text/csv',
			1024,
			'/path/to/file',
			10,
			1,
			'hash-value',
			'mocked-url',
		);

		mockValidationPolicyFactory = {
			createUploadValidationFactory: jest.fn().mockReturnValue({
				validate: jest.fn().mockResolvedValue(undefined),
			}),
			createConflictValidationFactory: jest.fn().mockReturnValue({
				validate: jest.fn().mockResolvedValue(undefined),
			}),
		} as jest.Mocked<ValidationPolicyFactory>;

		mockFileParserValidatorGateway = {
			parse: jest.fn().mockResolvedValue(undefined),
		} as jest.Mocked<FileParserValidatorGateway>;

		mockUploadFileGateway = {
			upload: jest.fn().mockResolvedValue('mocked-url'),
		} as jest.Mocked<UploadFileGateway>;

		mockInsertCensoService = {
			insert: jest.fn().mockResolvedValue(undefined),
		} as unknown as jest.Mocked<InsertCensoService.InsertCensoService>;

		mockDeleteFileService = {
			deleteFile: jest.fn().mockResolvedValue(undefined),
		};
		uploadCensoService = new UploadCensoService.UploadCensoService(
			mockFileParserValidatorGateway,
			mockValidationPolicyFactory,
			mockUploadFileGateway,
			mockInsertCensoService,
			mockDeleteFileService,
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	it('deve chamar ValidationPolicyFactory.createUploadValidationFactory e validar', async () => {
		await uploadCensoService.execute(mockFile);

		expect(
			mockValidationPolicyFactory.createUploadValidationFactory,
		).toHaveBeenCalledWith(mockFile);

		const validationPolicy =
			mockValidationPolicyFactory.createUploadValidationFactory(mockFile);

		expect(validationPolicy.validate).toHaveBeenCalled();
	});

	it('deve chamar FileParserValidatorGateway.parse com o arquivo correto', async () => {
		await uploadCensoService.execute(mockFile);

		expect(mockFileParserValidatorGateway.parse).toHaveBeenCalledWith(mockFile);
	});

	it('deve chamar UploadFileGateway.upload com o arquivo correto', async () => {
		await uploadCensoService.execute(mockFile);

		expect(mockUploadFileGateway.upload).toHaveBeenCalledWith(mockFile);
	});

	it('deve mockar fs.unlink e lidar com a exclusão do arquivo de forma adequada', async () => {
		await uploadCensoService.execute(mockFile);

		expect(mockDeleteFileService.deleteFile).toHaveBeenCalledWith(
			mockFile.caminhoInterno,
		);
	});

	it('deve lançar um erro se a validação falhar e chamar deleteFile', async () => {
		const validationError = new Error('Falha na validação');

		mockValidationPolicyFactory.createUploadValidationFactory.mockReturnValue({
			validate: jest.fn().mockRejectedValue(validationError),
		});

		await expect(uploadCensoService.execute(mockFile)).rejects.toThrow(
			'Falha na validação',
		);

		expect(mockDeleteFileService.deleteFile).toHaveBeenCalledWith(
			mockFile.caminhoInterno,
		);
	});

	it('deve lançar um erro se o parsing falhar e chamar deleteFile', async () => {
		const parsingError = new Error('Falha no parsing');

		mockFileParserValidatorGateway.parse.mockRejectedValue(parsingError);

		await expect(uploadCensoService.execute(mockFile)).rejects.toThrow(
			'Falha no parsing',
		);

		expect(mockDeleteFileService.deleteFile).toHaveBeenCalledWith(
			mockFile.caminhoInterno,
		);
	});

	it('deve persistir o arquivo após o upload', async () => {
		const mockDate = '2024-11-28T12:00:00Z';
		MockDate.set(mockDate);
		const toInsertCenso = new InsereCenso(
			new Date(),
			mockFile.companyId,
			mockFile.userId,
			mockFile.caminhoExterno,
			mockFile.nome,
			mockFile.hash,
			mockFile.totalLinhas,
		);

		await uploadCensoService.execute(mockFile);

		expect(mockInsertCensoService.insert).toHaveBeenCalledWith(toInsertCenso);
	});

	it('deve lançar um erro se o total de linhas for 0', async () => {
		mockFileParserValidatorGateway.parse.mockResolvedValue(0);

		await expect(uploadCensoService.execute(mockFile)).rejects.toThrow(
			'Arquivo vazio',
		);
	});
});
