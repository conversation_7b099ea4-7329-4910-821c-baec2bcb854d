import { File } from 'src/core/domain/File';
import { UploadService } from '../upload.service';
import { InvalidContentException } from 'src/shared/exceptions/upload/InvalidContent.exception';
import { FileParserValidatorGateway } from 'src/core/application/gateway/parser/fileParserValidator.gateway';
import { ValidationPolicyFactory } from 'src/core/application/validations/factory/abstract/validation.policy.factory';
import { UploadFileGateway } from 'src/core/application/gateway/file/upload/uploadFile.gateway';
import { InsertCensoService } from '../../../censo/insertCenso.service';
import { InsereCenso } from 'src/core/application/dto/InsereCenso';
import { DeleteFileService } from '../../deleteFile.service';

export namespace UploadCensoService {
	export class UploadCensoService extends UploadService {
		constructor(
			private readonly fileParserValidatorGateway: FileParserValidatorGateway,
			private readonly validationPolicyFactory: ValidationPolicyFactory,
			private readonly uploadFileGateway: UploadFileGateway,
			private readonly insertCensoService: InsertCensoService.InsertCensoService,
			private readonly deleteFileService: DeleteFileService.DeleteFileService,
		) {
			super();
		}

		protected async validate(file: File): Promise<void> {
			try {
				const validationPolicy =
					this.validationPolicyFactory.createUploadValidationFactory(file);
				await validationPolicy.validate();
				file.totalLinhas = await this.fileParserValidatorGateway.parse(file);
				if (file.totalLinhas === 0) {
					throw new InvalidContentException(400, 'Arquivo vazio', []);
				}
			} catch (error) {
				this.deleteFile(file.caminhoInterno);
				throw error;
			}
		}

		protected async deleteFile(filePath: string): Promise<void> {
			await this.deleteFileService.deleteFile(filePath);
		}

		protected async upload(file: File): Promise<void> {
			file.caminhoExterno = await this.uploadFileGateway.upload(file);
			this.deleteFile(file.caminhoInterno);
		}

		protected async persistFile(file: File): Promise<number> {
			const insereCenso = InsereCenso.fromFile(file);
			return await this.insertCensoService.insert(insereCenso);
		}
	}
}
