import { promises as fs } from 'fs';
import { DeleteFileService } from './deleteFile.service';

jest.mock('fs', () => ({
	promises: {
		unlink: jest.fn(),
	},
}));

describe('DeleteFileService', () => {
	let deleteFileService: DeleteFileService.DeleteFileService;

	beforeEach(() => {
		deleteFileService = new DeleteFileService.DeleteFileService();
	});

	it('deve deletar arquivo', async () => {
		const filePath = 'path/to/file';
		deleteFileService.deleteFile(filePath);
		expect(fs.unlink).toHaveBeenCalledWith(filePath);
	});

	it('deve dar erro ao deletar arquivo inexistente', async () => {
		const filePath = 'path/to/file';
		fs.unlink = jest
			.fn()
			.mockRejectedValueOnce(new Error('Arquivo não encontrado'));
		await expect(deleteFileService.deleteFile(filePath)).rejects.toThrow(
			'Arquivo não encontrado',
		);
		expect(fs.unlink).toHaveBeenCalledWith(filePath);
	});
});
