import { DownloadModeloCensoService } from './downloadModeloCenso.service';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';
import { ModelTemplateFactory } from 'src/core/application/gateway/file/download/factory/abstract/modelTemplate.factory';
import { DownloadService } from '../download.service';

describe('DownloadModeloCensoService', () => {
	let service: DownloadModeloCensoService;
	let mockDownloadFileGateway: jest.Mocked<DownloadFileGateway>;
	let mockModelTemplateFactory: jest.Mocked<ModelTemplateFactory>;

	beforeEach(() => {
		mockDownloadFileGateway = {
			downloadCsvToTemplate: jest.fn(),
		} as unknown as jest.Mocked<DownloadFileGateway>;

		mockModelTemplateFactory = {
			generateTemplateToFile: jest.fn(),
		} as unknown as jest.Mocked<ModelTemplateFactory>;

		service = new DownloadModeloCensoService(
			mockDownloadFileGateway,
			mockModelTemplateFactory,
		);
	});

	afterEach(() => {
		jest.clearAllMocks();
	});

	describe('download', () => {
		it('deve chamar o factory e o gateway corretamente', async () => {
			const companyId = 1;
			const mockTemplate = ['header1', 'header2', 'header3'];
			const mockCsvContent = 'header1,header2,header3\n';

			mockModelTemplateFactory.generateTemplateToFile.mockReturnValue(
				mockTemplate,
			);
			mockDownloadFileGateway.downloadCsvToTemplate.mockResolvedValue(
				mockCsvContent,
			);

			const result = await service.execute(companyId);

			expect(
				mockModelTemplateFactory.generateTemplateToFile,
			).toHaveBeenCalledTimes(1);
			expect(
				mockModelTemplateFactory.generateTemplateToFile,
			).toHaveBeenCalledWith(companyId);

			expect(
				mockDownloadFileGateway.downloadCsvToTemplate,
			).toHaveBeenCalledTimes(1);
			expect(
				mockDownloadFileGateway.downloadCsvToTemplate,
			).toHaveBeenCalledWith(mockTemplate);

			expect(result).toBe(mockCsvContent);
		});

		it('deve propagar erros do ModelTemplateFactory', async () => {
			const companyId = 1;
			const mockError = new Error('Factory error');
			mockModelTemplateFactory.generateTemplateToFile.mockImplementation(() => {
				throw mockError;
			});

			// Act & Assert
			await expect(service.execute(companyId)).rejects.toThrow(mockError);
			expect(
				mockDownloadFileGateway.downloadCsvToTemplate,
			).not.toHaveBeenCalled();
		});

		it('deve propagar erros do DownloadFileGateway', async () => {
			const companyId = 1;
			const mockTemplate = ['header1', 'header2'];
			const mockError = new Error('Gateway error');

			mockModelTemplateFactory.generateTemplateToFile.mockReturnValue(
				mockTemplate,
			);
			mockDownloadFileGateway.downloadCsvToTemplate.mockRejectedValue(
				mockError,
			);

			// Act & Assert
			await expect(service.execute(companyId)).rejects.toThrow(mockError);

			expect(
				mockModelTemplateFactory.generateTemplateToFile,
			).toHaveBeenCalled();
			expect(mockDownloadFileGateway.downloadCsvToTemplate).toHaveBeenCalled();
		});
	});

	describe('herança de DownloadService', () => {
		it('deve estender corretamente a classe DownloadService', () => {
			expect(service).toBeInstanceOf(DownloadService);
		});
	});
});
