import { DownloadService } from '../download.service';
import { DownloadFileGateway } from 'src/core/application/gateway/file/download/downloadFile.gateway';
import { ModelTemplateFactory } from 'src/core/application/gateway/file/download/factory/abstract/modelTemplate.factory';

export class DownloadModeloCensoService extends DownloadService {
	constructor(
		private readonly downloadFileGateway: DownloadFileGateway,
		private readonly modelTemplateFactory: ModelTemplateFactory,
	) {
		super();
	}

	protected download(companyId: number): Promise<string> {
		const modelFile =
			this.modelTemplateFactory.generateTemplateToFile(companyId);

		return this.downloadFileGateway.downloadCsvToTemplate(modelFile);
	}
}
