import { CompanyConfig } from 'src/core/domain/CompanyConfig';
import { FindCompanyConfigGateway } from '../../gateway/companyConfig/findCompanyConfig.gateway';

export class FindCompanyConfigService {
	constructor(
		private readonly findCompanyConfigGateway: FindCompanyConfigGateway,
	) {}
	public async findCompanyConfigByCompanyId(
		companyId: number,
	): Promise<CompanyConfig> {
		return this.findCompanyConfigGateway.findCompanyConfigByCompanyId(
			companyId,
		);
	}
}
