import { FindCompanyConfigService } from './findCompanyConfig.service';
import { FindCompanyConfigGateway } from '../../gateway/companyConfig/findCompanyConfig.gateway';
import { CompanyConfig } from 'src/core/domain/CompanyConfig';

describe('FindCompanyConfigService', () => {
	let findCompanyConfigService: FindCompanyConfigService;
	let findCompanyConfigGateway: FindCompanyConfigGateway;

	const mockCompanyConfig: CompanyConfig = {
		['teste']: [{ id: 1, value: '1', enabled: true, name: '1' }],
	};

	beforeEach(() => {
		findCompanyConfigGateway = {
			findCompanyConfigByCompanyId: jest.fn(),
		};

		findCompanyConfigService = new FindCompanyConfigService(
			findCompanyConfigGateway,
		);
	});

	describe('findCompanyConfigByCompanyId', () => {
		it('should call findCompanyConfigByCompanyId on the gateway with the correct parameters and return the result', async () => {
			const companyId = 123;

			(
				findCompanyConfigGateway.findCompanyConfigByCompanyId as jest.Mock
			).mockResolvedValue(mockCompanyConfig);

			const result =
				await findCompanyConfigService.findCompanyConfigByCompanyId(companyId);

			expect(
				findCompanyConfigGateway.findCompanyConfigByCompanyId,
			).toHaveBeenCalledWith(companyId);
			expect(result).toEqual(mockCompanyConfig);
		});

		it('should throw an error when the gateway throws an error', async () => {
			const companyId = 123;
			const errorMessage = 'Failed to fetch company config';

			(
				findCompanyConfigGateway.findCompanyConfigByCompanyId as jest.Mock
			).mockRejectedValue(new Error(errorMessage));

			await expect(
				findCompanyConfigService.findCompanyConfigByCompanyId(companyId),
			).rejects.toThrow(errorMessage);

			expect(
				findCompanyConfigGateway.findCompanyConfigByCompanyId,
			).toHaveBeenCalledWith(companyId);
		});

		it('should return empty config when company id is not provided', async () => {
			const companyId: number = null;
			const emptyConfig = {};

			(
				findCompanyConfigGateway.findCompanyConfigByCompanyId as jest.Mock
			).mockResolvedValue(emptyConfig);

			const result =
				await findCompanyConfigService.findCompanyConfigByCompanyId(companyId);

			expect(
				findCompanyConfigGateway.findCompanyConfigByCompanyId,
			).toHaveBeenCalledWith(companyId);
			expect(result).toEqual(emptyConfig);
		});
	});
});
