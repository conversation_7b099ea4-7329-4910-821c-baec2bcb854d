import { DeleteCensoGateway } from '../../gateway/censo/deleteCenso.gateway';
import { DeleteCensoService } from './deleteCenso.service';

describe('ChangeCensoStatusService', () => {
	let deleteCensoGatewayMock: jest.Mocked<DeleteCensoGateway>;
	let deleteCensoService: DeleteCensoService.DeleteCensoService;

	beforeEach(() => {
		deleteCensoGatewayMock = {
			delete: jest.fn(),
		};

		deleteCensoService = new DeleteCensoService.DeleteCensoService(
			deleteCensoGatewayMock,
		);
	});

	it('deve mudar o status do censo de id passado ', async () => {
		const censoId = 1;
		await deleteCensoService.delete(censoId);
		expect(deleteCensoGatewayMock.delete).toHaveBeenCalledWith(1);
		expect(deleteCensoGatewayMock.delete).toHaveBeenCalledTimes(1);
	});
});
