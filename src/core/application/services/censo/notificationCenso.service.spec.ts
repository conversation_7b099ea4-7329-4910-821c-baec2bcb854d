import { NotificationCensoService } from 'src/core/application/services/censo/notificationCenso.service';
import { EventBus } from 'src/configuration/socket/abstract/event-bus';
import { ProcessaCenso } from '../../dto/ProcessaCenso';
import { CENSO_LINHA_PROCESSADA_EVENT } from 'src/helpers/events.helper';
import { ChangeCensoStatusGateway } from '../../gateway/censo/changeCensoStatus.gateway';
import { ChangeCensoStatusService } from './changeCensoStatus.service';

describe('NotificationCensoService', () => {
	let notificationCensoService: NotificationCensoService.NotificationCensoService;
	let mockEventBus: jest.Mocked<EventBus>;
	let clientsMap: Map<string, string>;
	let changeCensoStatusGateway: jest.Mocked<ChangeCensoStatusGateway>;
	let changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService;
	let processaCenso: ProcessaCenso;

	beforeEach(() => {
		mockEventBus = {
			emit: jest.fn(),
			on: jest.fn(),
		};
		clientsMap = new Map<string, string>();
		processaCenso = new ProcessaCenso(
			1,
			'/mock/caminho',
			0,
			0,
			'/mock/caminho-interno',
			{ key: 'value' },
			'mock-id-socket',
			0,
			0,
		);

		changeCensoStatusGateway = {
			changeStatus: jest.fn(),
		};

		changeCensoStatusService =
			new ChangeCensoStatusService.ChangeCensoStatusService(
				changeCensoStatusGateway,
			);

		notificationCensoService =
			new NotificationCensoService.NotificationCensoService(
				clientsMap,
				mockEventBus,
				changeCensoStatusService,
			);
	});

	it('deve adicionar um cliente quando o método connect for chamado', () => {
		const clientId = 'client-1';

		notificationCensoService.connect(processaCenso.idSocket, clientId);

		expect(clientsMap.has(processaCenso.idSocket)).toBe(true);
		expect(clientsMap.get(processaCenso.idSocket)).toBe(clientId);
	});

	it('deve remover um cliente quando o método disconect for chamado', () => {
		const clientId = 'client-1';
		notificationCensoService.connect(processaCenso.idSocket, clientId);

		expect(clientsMap.has(processaCenso.idSocket)).toBe(true);

		notificationCensoService.disconect(clientId);

		expect(clientsMap.has(processaCenso.idSocket)).toBe(false);
	});

	it('deve emitir um evento quando o método sendNotification for chamado', () => {
		notificationCensoService.sendNotification(processaCenso);
		const totalLinha = processaCenso.totalLinhas;
		const linhaAtual = processaCenso.linhaAtual;
		const percentual = (linhaAtual / totalLinha) * 100;

		processaCenso.porcentagem = percentual;
		expect(mockEventBus.emit).toHaveBeenCalledWith(
			CENSO_LINHA_PROCESSADA_EVENT,
			processaCenso,
		);
	});

	it('nao deve trocar o status do censo para "verificar" quando nao tiver terminado o processamento', () => {
		processaCenso.porcentagem = 0;
		notificationCensoService.sendNotification(processaCenso);
		expect(changeCensoStatusGateway.changeStatus).toHaveBeenCalledTimes(0);
	});

	it('deve trocar o status do censo para "verificar" quando tiver terminado o processamento', () => {
		processaCenso.linhaAtual = 1;
		processaCenso.totalLinhas = 1;
		notificationCensoService.sendNotification(processaCenso);
		expect(changeCensoStatusGateway.changeStatus).toHaveBeenCalledTimes(1);
		expect(changeCensoStatusGateway.changeStatus).toHaveBeenCalledWith(
			processaCenso.censoId.toString(),
			'verificar',
		);
	});
});
