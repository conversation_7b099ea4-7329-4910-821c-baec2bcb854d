import { InsertCensoGateway } from '../../gateway/censo/insertCenso.gateway';
import { InsertCensoService } from './insertCenso.service';
import { InsereCenso } from '../../dto/InsereCenso';
import MockDate from 'mockdate';
import { DuplicatedCensoException } from 'src/shared/exceptions/rule/duplicatedCenso.exception';

describe(InsertCensoService.InsertCensoService.name, () => {
	let mockInsertCensoGateway: jest.Mocked<InsertCensoGateway>;
	let insertCensoService: InsertCensoService.InsertCensoService;
	let insereCenso: InsereCenso;

	beforeEach(() => {
		mockInsertCensoGateway = {
			insert: jest.fn(),
		};
		insertCensoService = new InsertCensoService.InsertCensoService(
			mockInsertCensoGateway,
		);

		MockDate.set('2021-09-01T00:00:00.000Z');
		insereCenso = new InsereCenso(
			new Date(),
			1,
			1,
			'diretorio',
			'hash',
			'nome',
		);
	});

	it('deve inserir censo', () => {
		mockInsertCensoGateway.insert.mockResolvedValue(1);

		const result = insertCensoService.insert(insereCenso);

		expect(mockInsertCensoGateway.insert).toHaveBeenCalledWith(insereCenso);
		expect(result).resolves.toBe(1);
	});

	it('deve retornar erro ao inserir censo com hash ja existente', () => {
		mockInsertCensoGateway.insert.mockRejectedValueOnce(
			new DuplicatedCensoException(
				400,
				'Censo with the given hash already exists',
			),
		);

		const result = insertCensoService.insert(insereCenso);

		expect(mockInsertCensoGateway.insert).toHaveBeenCalledWith(insereCenso);
		expect(result).rejects.toThrow(
			new DuplicatedCensoException(
				400,
				'Censo with the given hash already exists',
			),
		);
	});
});
