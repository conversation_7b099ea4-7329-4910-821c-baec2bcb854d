import { RetornoDeleteCenso } from '../../dto/RetornoDeleteCenso';
import { DeleteCensoGateway } from '../../gateway/censo/deleteCenso.gateway';

export namespace DeleteCensoService {
	export class DeleteCensoService {
		constructor(private readonly deleteCensoGateway: DeleteCensoGateway) {}

		public async delete(censoId: number): Promise<RetornoDeleteCenso> {
			return await this.deleteCensoGateway.delete(censoId);
		}
	}
}
