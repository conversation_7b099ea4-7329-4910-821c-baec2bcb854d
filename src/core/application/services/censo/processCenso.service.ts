import { ProcessaCenso } from '../../dto/ProcessaCenso';
import { RetornoProcessaCenso } from '../../dto/RetornoProcessaCenso';
import { ProcessCensoGateway } from '../../gateway/censo/processCenso.gateway';
import { DownloadFileGateway } from '../../gateway/file/download/downloadFile.gateway';
import { DeleteFileService } from '../file/deleteFile.service';
import { ChangeCensoStatusService } from './changeCensoStatus.service';

export namespace ProcessCensoService {
	export class ProcessCensoService {
		constructor(
			private readonly downloadFileGateway: DownloadFileGateway,
			private readonly processCensoGateway: ProcessCensoGateway,
			private readonly deleteFileService: DeleteFileService.DeleteFileService,
			private readonly changeStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		) {}

		async process(processaCenso: ProcessaCenso): Promise<RetornoProcessaCenso> {
			const retrievedFile = await this.downloadFileGateway.downloadFile(
				processaCenso.censoPath,
			);
			processaCenso.censoInternalPath = retrievedFile;
			await this.changeStatusService.changeStatus(
				processaCenso.censoId.toString(),
				'processando',
			);
			const idSocket = await this.processCensoGateway.process(processaCenso);
			await this.deleteFile(processaCenso.censoInternalPath);
			return idSocket;
		}

		private async deleteFile(filePath: string): Promise<void> {
			await this.deleteFileService.deleteFile(filePath);
		}
	}
}
