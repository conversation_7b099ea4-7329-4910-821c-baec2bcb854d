import { ProcessCensoService } from 'src/core/application/services/censo/processCenso.service';
import { DownloadFileGateway } from '../../gateway/file/download/downloadFile.gateway';
import { ProcessCensoGateway } from '../../gateway/censo/processCenso.gateway';
import { ProcessaCenso } from '../../dto/ProcessaCenso';
import { DeleteFileService } from '../file/deleteFile.service';
import { ChangeCensoStatusService } from './changeCensoStatus.service';
import { ChangeCensoStatusGateway } from '../../gateway/censo/changeCensoStatus.gateway';

describe('ProcessCensoService', () => {
	let downloadFileGateway: jest.Mocked<DownloadFileGateway>;
	let processaCensoGateway: jest.Mocked<ProcessCensoGateway>;
	let processCensoService: ProcessCensoService.ProcessCensoService;
	let deleteFileService: jest.Mocked<DeleteFileService.DeleteFileService>;
	let changeCensoStatusGateway: jest.Mocked<ChangeCensoStatusGateway>;
	let changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService;
	let processaCenso: ProcessaCenso;

	beforeEach(() => {
		downloadFileGateway = {
			downloadFile: jest.fn(),
			downloadCsvToTemplate: jest.fn(),
		};

		processaCensoGateway = {
			process: jest.fn(),
		};

		deleteFileService = {
			deleteFile: jest.fn(),
		};

		changeCensoStatusGateway = {
			changeStatus: jest.fn(),
		};

		changeCensoStatusService =
			new ChangeCensoStatusService.ChangeCensoStatusService(
				changeCensoStatusGateway,
			);

		processCensoService = new ProcessCensoService.ProcessCensoService(
			downloadFileGateway,
			processaCensoGateway,
			deleteFileService,
			changeCensoStatusService,
		);

		processaCenso = new ProcessaCenso(0, 'path/to/file', 0, 0, 'internal/path');
	});

	it('deve processar arquivo existente ', async () => {
		downloadFileGateway.downloadFile.mockResolvedValueOnce('internal/path');

		await processCensoService.process(processaCenso);
		expect(downloadFileGateway.downloadFile).toHaveBeenCalledWith(
			processaCenso.censoPath,
		);
	});

	it('deve dar erro ao processar arquivo inexistente ', async () => {
		downloadFileGateway.downloadFile.mockRejectedValueOnce(
			new Error('Arquivo não encontrado'),
		);

		await expect(processCensoService.process(processaCenso)).rejects.toThrow(
			'Arquivo não encontrado',
		);
	});

	it('deve chamar deleteFile para deletar o arquivo após processamento', async () => {
		downloadFileGateway.downloadFile.mockResolvedValueOnce('internal/path');

		await processCensoService.process(processaCenso);

		expect(deleteFileService.deleteFile).toHaveBeenCalledWith(
			processaCenso.censoInternalPath,
		);
	});

	it('deve chamar changeStatus para alterar o status do censo pré processamento', async () => {
		await processCensoService.process(processaCenso);
		expect(changeCensoStatusGateway.changeStatus).toHaveBeenCalledWith(
			'0',
			'processando',
		);
	});
});
