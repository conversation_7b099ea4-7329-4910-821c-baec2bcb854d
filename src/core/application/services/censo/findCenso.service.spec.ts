/* eslint-disable @typescript-eslint/no-explicit-any */
import { FindCensoGateway } from '../../gateway/censo/findCenso.gateway';
import { Censo } from 'src/core/domain/Censo';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { FindCensoService } from './findCenso.service';

describe('FindCensoService', () => {
	let findCensoService: FindCensoService.FindCensoService;
	let findCensoGatewayMock: jest.Mocked<FindCensoGateway>;
	let censosMock: Censo[];

	beforeEach(() => {
		findCensoGatewayMock = {
			findByCompanyId: jest.fn(),
			findListagem: jest.fn(),
			findById: jest.fn(),
			findByFileHash: jest.fn(),
			findModalStateByUser: jest.fn(),
		} as unknown as jest.Mocked<FindCensoGateway>;

		findCensoService = new FindCensoService.FindCensoService(
			findCensoGatewayMock,
		);

		censosMock = [
			new Censo(
				'1',
				new Date(),
				'1',
				100,
				'user1',
				{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
				{ name: 'Operadora A' },
				{ name: 'User A' },
				'path/to/censo',
				'censo',
			),
		];
	});

	describe('findByCompanyId', () => {
		it('deve chamar findByCompanyId e retornar censos', async () => {
			const companyId = 1;

			findCensoGatewayMock.findByCompanyId.mockResolvedValue(censosMock);

			const result = await findCensoService.findByCompanyId(companyId);

			expect(findCensoGatewayMock.findByCompanyId).toHaveBeenCalledWith(
				companyId,
			);

			expect(result).toEqual(censosMock);
		});
	});

	describe('findModalStateByUser', () => {
		it('deve chamar findModalStateByUser e retornar censos', async () => {
			const userId = 1;
			const censoMock = new Censo(
				'1',
				new Date(),
				'1',
				100,
				'user1',
				{ id: '1', descricao: 'Ativo', cor: 'green', designStatus: 'active' },
				{ name: 'Operadora A' },
				{ name: 'User A' },
				'/diretorio/salvo',
				'censo',
			);

			findCensoGatewayMock.findModalStateByUser.mockResolvedValue(censoMock);

			const result = await findCensoService.findModalStateByUser(userId);

			expect(findCensoGatewayMock.findModalStateByUser).toHaveBeenCalledWith(
				userId,
			);

			expect(result).toEqual(censoMock);
		});
	});
	describe('findListagem', () => {
		it('deve chamar findListagem e retornar CensoListagem', async () => {
			const companyId = 1;
			const paginacaoMock = new Paginacao(1, 10, 'searchQuery');
			const censoListagemMock = new CensoListagem(1, 10, censosMock);

			findCensoGatewayMock.findListagem.mockResolvedValue(censoListagemMock);

			const result = await findCensoService.findListagem(
				companyId,
				paginacaoMock,
			);

			expect(findCensoGatewayMock.findListagem).toHaveBeenCalledWith(
				companyId,
				{
					page: paginacaoMock.page,
					limit: paginacaoMock.limit,
					search: paginacaoMock.search,
				},
			);

			expect(result).toEqual(censoListagemMock);
		});

		it('deve chamar findListagem com paginação vazia e retornar CensoListagem', async () => {
			const companyId = 1;
			const paginacaoMock = new Paginacao(1, 10);
			const censoListagemMock = new CensoListagem(1, 10, censosMock);

			findCensoGatewayMock.findListagem.mockResolvedValue(censoListagemMock);

			const result = await findCensoService.findListagem(
				companyId,
				paginacaoMock,
			);

			expect(findCensoGatewayMock.findListagem).toHaveBeenCalledWith(
				companyId,
				{
					page: paginacaoMock.page,
					limit: paginacaoMock.limit,
					search: undefined,
				},
			);

			expect(result).toEqual(censoListagemMock);
		});

		it('deve chamar findById com id valido', async () => {
			const id = 1;
			await findCensoService.findById(id);
			expect(findCensoGatewayMock.findById).toHaveBeenCalledWith(id);
		});

		it('deve chamar findByFileHash com hash valido', async () => {
			const hash = 'aaaaaa';
			await findCensoService.findByCensoHash(hash);
			expect(findCensoGatewayMock.findByFileHash).toHaveBeenCalledWith(hash);
		});
	});
});
