import { InsereCenso } from '../../dto/InsereCenso';
import { InsertCensoGateway } from '../../gateway/censo/insertCenso.gateway';

export namespace InsertCensoService {
	export class InsertCensoService {
		constructor(private readonly insertCensoGateway: InsertCensoGateway) {}

		async insert(insereCenso: InsereCenso): Promise<number> {
			return await this.insertCensoGateway.insert(insereCenso);
		}
	}
}
