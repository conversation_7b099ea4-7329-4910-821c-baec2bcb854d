import { ChangeCensoStatusGateway } from '../../gateway/censo/changeCensoStatus.gateway';
import { ChangeCensoStatusService } from './changeCensoStatus.service';

describe('ChangeCensoStatusService', () => {
	let changeCensoStatusGatewayMock: jest.Mocked<ChangeCensoStatusGateway>;
	let changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService;

	beforeEach(() => {
		changeCensoStatusGatewayMock = {
			changeStatus: jest.fn(),
		};

		changeCensoStatusService =
			new ChangeCensoStatusService.ChangeCensoStatusService(
				changeCensoStatusGatewayMock,
			);
	});

	it('deve mudar o status do censo de id passado ', async () => {
		await changeCensoStatusService.changeStatus('id', 'verificar');
		expect(changeCensoStatusGatewayMock.changeStatus).toHaveBeenCalledWith(
			'id',
			'verificar',
		);
		expect(changeCensoStatusGatewayMock.changeStatus).toHaveBeenCalledTimes(1);
	});
});
