import { CENSO_LINHA_PROCESSADA_EVENT } from 'src/helpers/events.helper';
import { ProcessaCenso } from '../../dto/ProcessaCenso';
import { EventBus } from 'src/configuration/socket/abstract/event-bus';
import { ChangeCensoStatusService } from './changeCensoStatus.service';

export namespace NotificationCensoService {
	export class NotificationCensoService {
		constructor(
			public readonly clients: Map<string, string>,
			private eventBus: EventBus,
			private readonly changeCensoStatusService: ChangeCensoStatusService.ChangeCensoStatusService,
		) {}

		public connect(receivedUuid: string, socketIoId: string): void {
			this.clients.set(receivedUuid, socketIoId);
		}

		public disconect(clientId: string): void {
			this.clients.forEach((value, key) => {
				if (value === clientId) {
					this.clients.delete(key);
				}
			});
		}

		public sendNotification(processaCenso: ProcessaCenso): void {
			const totalLinha = processaCenso.totalLinhas;
			const linhaAtual = processaCenso.linhaAtual;
			const percentual = (linhaAtual / totalLinha) * 100;
			processaCenso.porcentagem = percentual;
			this.verifyAndChangeStatus(processaCenso);
			this.eventBus.emit(CENSO_LINHA_PROCESSADA_EVENT, processaCenso);
		}

		private verifyAndChangeStatus(processaCenso: ProcessaCenso): void {
			if (processaCenso.porcentagem !== 100) {
				return;
			}
			this.changeCensoStatusService.changeStatus(
				processaCenso.censoId.toString(),
				'verificar',
			);
		}
	}
}
