import { Censo } from 'src/core/domain/Censo';
import { FindCensoGateway } from '../../gateway/censo/findCenso.gateway';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { OrdenacaoCensoListagem } from '../../dto/OrdenacaoCensoListagem';

export namespace FindCensoService {
	export class FindCensoService {
		constructor(private readonly findCensoGateway: FindCensoGateway) {}

		public async findByCompanyId(companyId: number): Promise<Censo[]> {
			return await this.findCensoGateway.findByCompanyId(companyId);
		}

		public async findModalStateByUser(userId: number): Promise<Censo> {
			return await this.findCensoGateway.findModalStateByUser(userId);
		}

		public async findListagem(
			companyId: number,
			query: Paginacao & OrdenacaoCensoListagem,
		): Promise<CensoListagem> {
			return await this.findCensoGateway.findListagem(companyId, query);
		}

		public async findByCensoHash(censoHash: string): Promise<Censo> {
			return await this.findCensoGateway.findByFileHash(censoHash);
		}

		public async findById(id: number): Promise<Censo> {
			return await this.findCensoGateway.findById(id);
		}
	}
}
