import { ChangeCensoStatusGateway } from '../../gateway/censo/changeCensoStatus.gateway';

export namespace ChangeCensoStatusService {
	export class ChangeCensoStatusService {
		constructor(
			private readonly changeCensoStatusGateway: ChangeCensoStatusGateway,
		) {}

		public async changeStatus(censoId: string, status: string): Promise<void> {
			await this.changeCensoStatusGateway.changeStatus(censoId, status);
		}
	}
}
