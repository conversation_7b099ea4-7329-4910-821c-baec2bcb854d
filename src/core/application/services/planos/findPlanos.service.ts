import { FindPlanosGateway } from '../../gateway/planos/findPlanos.gateway';
import { Planos } from '../../../domain/Planos';

export class FindPlanosService {
	constructor(private findPlanosGateway: FindPlanosGateway) {}

	public async searchPlanos(
		nome: string,
		companyId: number,
		limit: number,
	): Promise<Planos[]> {
		return this.findPlanosGateway.searchPlanos(nome, companyId, limit);
	}
}
