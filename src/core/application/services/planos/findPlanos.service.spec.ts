import { FindPlanosService } from './findPlanos.service';
import { FindPlanosGateway } from '../../gateway/planos/findPlanos.gateway';
import { Planos } from '../../../domain/Planos';
import { TipoContrato } from '../../../domain/TipoContrato';

describe('FindPlanosService', () => {
	let findPlanosService: FindPlanosService;
	let findPlanosGateway: FindPlanosGateway;
	const mockTipoContrato = new TipoContrato(1, 'Test Contrato 1', 'TC1');

	beforeEach(async () => {
		findPlanosGateway = {
			searchPlanos: jest.fn(),
		};

		findPlanosService = new FindPlanosService(findPlanosGateway);
	});

	describe('searchPlanos', () => {
		it('should call searchPlanos on the FindPlanosGateway with the correct parameters and return the result', async () => {
			const nome = 'Test Plano';
			const companyId = 123;
			const limit = 10;

			const mockPlanos: Planos[] = [
				{
					id: 1,
					nome: 'Test Plano 1',
					tipoContrato: mockTipoContrato,
				},
				{ id: 2, nome: 'Test Plano 2', tipoContrato: mockTipoContrato },
			];

			jest
				.spyOn(findPlanosGateway, 'searchPlanos')
				.mockResolvedValue(mockPlanos);

			const result = await findPlanosService.searchPlanos(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockPlanos);
			expect(findPlanosGateway.searchPlanos).toHaveBeenCalledWith(
				nome,
				companyId,
				limit,
			);
		});

		it('should call searchPlanos on the FindPlanosGateway with the null nome parameter and return the result ', async () => {
			const nome: string = null;
			const companyId = 123;
			const limit = 10;

			const mockPlanos: Planos[] = [
				{
					id: 1,
					nome: 'Test Plano 1',
					tipoContrato: mockTipoContrato,
				},
				{ id: 2, nome: 'Test Plano 2', tipoContrato: mockTipoContrato },
			];

			jest
				.spyOn(findPlanosGateway, 'searchPlanos')
				.mockResolvedValue(mockPlanos);

			const result = await findPlanosService.searchPlanos(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockPlanos);
			expect(findPlanosGateway.searchPlanos).toHaveBeenCalledWith(
				nome,
				companyId,
				limit,
			);
		});
	});
});
