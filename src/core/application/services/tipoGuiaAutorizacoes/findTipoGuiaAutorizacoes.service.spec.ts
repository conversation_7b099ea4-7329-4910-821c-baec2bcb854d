import { FindTipoGuiaAutorizacoesService } from './findTipoGuiaAutorizacoes.service';
import { FindTipoGuiaAutorizacoesGateway } from '../../gateway/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoes } from '../../../domain/TipoGuiaAutorizacoes';
import { CriptografiaService } from '../criptografia/criptografia.service';

describe('FindTipoGuiaAutorizacoesService', () => {
	let findTipoGuiaAutorizacoesService: FindTipoGuiaAutorizacoesService;
	let findTipoGuiaAutorizacoesGateway: FindTipoGuiaAutorizacoesGateway;
	let criptografiaService: CriptografiaService;

	beforeEach(async () => {
		findTipoGuiaAutorizacoesGateway = {
			searchTipoGuiaAutorizacoes: jest.fn(),
		};

		criptografiaService = {
			encrypt: jest.fn(),
			decrypt: jest.fn(),
		} as unknown as CriptografiaService;

		findTipoGuiaAutorizacoesService = new FindTipoGuiaAutorizacoesService(
			findTipoGuiaAutorizacoesGateway,
			criptografiaService,
		);
	});

	describe('searchTipoGuiaAutorizacoes', () => {
		it('should call searchTipoGuiaAutorizacoes on the gateway with the correct parameters and encrypt the ids', async () => {
			const name = 'Test Tipo';
			const companyId = 123;
			const limit = 10;

			const mockTipoGuiaAutorizacoes: TipoGuiaAutorizacoes[] = [
				new TipoGuiaAutorizacoes(1, 'Tipo 1', 123, true),
				new TipoGuiaAutorizacoes(2, 'Tipo 2', 123, true),
			];

			jest
				.spyOn(findTipoGuiaAutorizacoesGateway, 'searchTipoGuiaAutorizacoes')
				.mockResolvedValue(mockTipoGuiaAutorizacoes);

			jest
				.spyOn(criptografiaService, 'encrypt')
				.mockImplementation((text) => `encrypted_${text}`);

			const result =
				await findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			// Assert gateway was called with correct parameters
			expect(
				findTipoGuiaAutorizacoesGateway.searchTipoGuiaAutorizacoes,
			).toHaveBeenCalledWith(name, companyId, limit);

			// Assert encryption was called for each id and companyId
			expect(criptografiaService.encrypt).toHaveBeenCalledWith('1');
			expect(criptografiaService.encrypt).toHaveBeenCalledWith('2');
			expect(criptografiaService.encrypt).toHaveBeenCalledWith('123');
			expect(criptografiaService.encrypt).toHaveBeenCalledTimes(4); // 2 ids + 2 companyIds

			// Assert result has encrypted ids
			expect(result).toHaveLength(2);
			expect(result[0].id).toBe('encrypted_1');
			expect(result[0].companyId).toBe('encrypted_123');
			expect(result[1].id).toBe('encrypted_2');
			expect(result[1].companyId).toBe('encrypted_123');
		});

		it('should handle empty results from the gateway', async () => {
			const name = 'Test Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(findTipoGuiaAutorizacoesGateway, 'searchTipoGuiaAutorizacoes')
				.mockResolvedValue([]);

			const result =
				await findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(
				findTipoGuiaAutorizacoesGateway.searchTipoGuiaAutorizacoes,
			).toHaveBeenCalledWith(name, companyId, limit);
			expect(result).toHaveLength(0);
			expect(criptografiaService.encrypt).not.toHaveBeenCalled();
		});

		it('should handle null results from the gateway', async () => {
			const name = 'Test Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(findTipoGuiaAutorizacoesGateway, 'searchTipoGuiaAutorizacoes')
				.mockResolvedValue(null);

			const result =
				await findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				);

			expect(
				findTipoGuiaAutorizacoesGateway.searchTipoGuiaAutorizacoes,
			).toHaveBeenCalledWith(name, companyId, limit);
			expect(result).toBeNull();
			expect(criptografiaService.encrypt).not.toHaveBeenCalled();
		});

		it('should handle errors from the gateway', async () => {
			const name = 'Test Tipo';
			const companyId = 123;
			const limit = 10;

			jest
				.spyOn(findTipoGuiaAutorizacoesGateway, 'searchTipoGuiaAutorizacoes')
				.mockRejectedValue(new Error('Gateway Error'));

			await expect(
				findTipoGuiaAutorizacoesService.searchTipoGuiaAutorizacoes(
					name,
					companyId,
					limit,
				),
			).rejects.toThrow('Gateway Error');

			expect(
				findTipoGuiaAutorizacoesGateway.searchTipoGuiaAutorizacoes,
			).toHaveBeenCalledWith(name, companyId, limit);
			expect(criptografiaService.encrypt).not.toHaveBeenCalled();
		});
	});
});
