import { FindTipoGuiaAutorizacoesGateway } from '../../gateway/tipoGuiaAutorizacoes/findTipoGuiaAutorizacoes';
import { TipoGuiaAutorizacoes } from 'src/core/domain/TipoGuiaAutorizacoes';
import { CriptografiaService } from '../criptografia/criptografia.service';

export class FindTipoGuiaAutorizacoesService {
	constructor(
		private readonly findTipoGuiaAutorizacoesGateway: FindTipoGuiaAutorizacoesGateway,
		private readonly criptografiaService: CriptografiaService,
	) {}

	async searchTipoGuiaAutorizacoes(
		name: string,
		companyId: number,
		limit: number,
	): Promise<TipoGuiaAutorizacoes[]> {
		const tipoGuia =
			await this.findTipoGuiaAutorizacoesGateway.searchTipoGuiaAutorizacoes(
				name,
				companyId,
				limit,
			);

		if (!tipoGuia) return null;
		return tipoGuia.map((tipo) => {
			tipo.id = this.criptografiaService.encrypt(tipo.id.toString());
			tipo.companyId = this.criptografiaService.encrypt(
				tipo.companyId.toString(),
			);
			return tipo;
		});
	}
}
