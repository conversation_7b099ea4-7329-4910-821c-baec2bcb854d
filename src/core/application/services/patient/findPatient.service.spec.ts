import { Patient } from 'src/core/domain/Patient';
import { FindPatientService } from './findPatient.service';
import { FindPatientGateway } from '../../gateway/patient/findPatient.gateway';

describe('FindPatientService', () => {
	let findPatientService: FindPatientService;
	let findPatientGateway: FindPatientGateway;

	beforeEach(async () => {
		findPatientGateway = {
			findByCodBeneficiary: jest.fn(),
			findByCodBeneficiaryAndNotName: jest.fn(),
			findByNameBirthAndNotCodBeneficiary: jest.fn(),
		};

		findPatientService = new FindPatientService(findPatientGateway);
	});

	it('should call findByCodBeneficiary on the gateway and return the result', async () => {
		const mockPatient = {
			id: 1,
			enabled: 1,
			created: new Date(),
			updated: new Date(),
			userId: 123,
			name: '<PERSON>',
			email: '<EMAIL>',
			motherName: '<PERSON>',
			companyId: 1,
			gender: 'M',
			codBeneficiario: '12345678901',
		} as Patient;

		(findPatientGateway.findByCodBeneficiary as jest.Mock).mockResolvedValue(
			mockPatient,
		);

		const result = await findPatientService.findPatientByCodBeneficiary(
			'12345678901',
			1,
		);

		expect(findPatientGateway.findByCodBeneficiary).toHaveBeenCalled();
		expect(result).toEqual(mockPatient);
	});
});
