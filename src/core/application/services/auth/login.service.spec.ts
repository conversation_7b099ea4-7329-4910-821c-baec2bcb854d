import { LoginService } from 'src/core/application/services/auth/login.service';
import { LoginGateway } from '../../gateway/auth/login.gateway';
import { User } from 'src/core/domain/User';

describe('LoginService', () => {
	let loginService: LoginService.LoginService;
	let loginGateway: LoginGateway;

	beforeEach(async () => {
		loginGateway = {
			login: jest.fn(),
		};

		loginService = new LoginService.LoginService(loginGateway);
	});

	it('should call login to LoginGatewayImpl with the correct parameters and return the result', async () => {
		const mockAuth = { token: 'mock_token' };
		const mockRequest: User = {
			id: 1,
			email: '<EMAIL>',
			password: 'password123',
			companyId: '3',
			name: 'user',
		};
		jest.spyOn(loginGateway, 'login').mockResolvedValue(mockAuth);

		const result = await loginService.login(mockRequest);

		expect(result).toEqual(mockAuth);
		expect(loginGateway.login).toHaveBeenCalledWith(mockRequest);
	});
});
