import { ValidateUserService } from './validateUser.service';
import { ValidateUserGateway } from '../../gateway/auth/validateUser.gateway';
import { User } from 'src/core/domain/User';

describe('ValidateUserService', () => {
	let validateUserService: ValidateUserService.ValidateUserService;
	let validateUserGateway: ValidateUserGateway;

	beforeEach(async () => {
		validateUserGateway = {
			validateUser: jest.fn(),
		};

		validateUserService = new ValidateUserService.ValidateUserService(
			validateUserGateway,
		);
	});

	it('must call validateUser on the ValidateUserGateway with the correct parameters and return the result', async () => {
		const mockUser: User = {
			id: 1,
			email: '<EMAIL>',
			name: 'User',
			companyId: '3',
		};

		jest.spyOn(validateUserGateway, 'validateUser').mockResolvedValue(mockUser);

		const result = await validateUserService.validateUser(
			'<EMAIL>',
			'valid_password',
		);

		expect(result).toEqual(mockUser);
		expect(validateUserGateway.validateUser).toHaveBeenCalledWith(
			'<EMAIL>',
			'valid_password',
		);
	});

	it('should return null if the user is not found', async () => {
		jest.spyOn(validateUserGateway, 'validateUser').mockResolvedValue(null);

		const result = await validateUserService.validateUser(
			'<EMAIL>',
			'invalid_password',
		);

		expect(result).toBeNull();
		expect(validateUserGateway.validateUser).toHaveBeenCalledWith(
			'<EMAIL>',
			'invalid_password',
		);
	});
});
