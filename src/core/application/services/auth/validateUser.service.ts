import { User } from 'src/core/domain/User';
import { ValidateUserGateway } from '../../gateway/auth/validateUser.gateway';

export namespace ValidateUserService {
	export class ValidateUserService {
		constructor(private readonly validateGateway: ValidateUserGateway) {}

		async validateUser(email: string, password: string): Promise<User> {
			return this.validateGateway.validateUser(email, password);
		}
	}
}
