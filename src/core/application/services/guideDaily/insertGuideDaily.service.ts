import { GuideDaily } from 'src/core/domain/GuideDaily';
import { InsertGuideDailyGateway } from '../../gateway/guideDaily/insertGuideDaily.gateway';

export class InsertGuideDailyService {
	constructor(private insertGuideDailyGateway: InsertGuideDailyGateway) {}

	public async insertGuideDaily(guideDaily: GuideDaily): Promise<GuideDaily> {
		return await this.insertGuideDailyGateway.insertGuideDaily(guideDaily);
	}
}
