import { InsertGuideDailyGateway } from '../../gateway/guideDaily/insertGuideDaily.gateway';
import { GuideDaily } from 'src/core/domain/GuideDaily';
import { InsertGuideDailyService } from './insertGuideDaily.service';

describe('InsertGuideDailyService', () => {
	let insertGuideDailyService: InsertGuideDailyService;
	let insertGuideDailyGatewayMock: jest.Mocked<InsertGuideDailyGateway>;

	beforeEach(() => {
		insertGuideDailyGatewayMock = {
			insertGuideDaily: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideDailyGateway>;

		insertGuideDailyService = new InsertGuideDailyService(
			insertGuideDailyGatewayMock,
		);
	});

	it('deve chamar insertGuideDailyGateway.insertGuideDaily com o GuideDaily fornecido e retornar o resultado', async () => {
		const dummyGuideDaily = new GuideDaily(
			null,
			{ id: 1 },
			'Test accommodation',
			1,
			2,
			3,
			0,
			123,
			100.0,
			10,
			1,
			new Date('2023-01-01T00:00:00Z'),
			new Date('2023-01-02T00:00:00Z'),
			4,
			0,
			11,
			'testIntegration',
			new Date('2023-01-03T00:00:00Z'),
		);

		const returnedGuideDaily = new GuideDaily(
			456,
			{ id: 1 },
			'Test accommodation',
			1,
			2,
			3,
			0,
			123,
			100.0,
			10,
			1,
			new Date('2023-01-01T00:00:00Z'),
			new Date('2023-01-02T00:00:00Z'),
			4,
			0,
			11,
			'testIntegration',
			new Date('2023-01-03T00:00:00Z'),
		);

		insertGuideDailyGatewayMock.insertGuideDaily.mockResolvedValue(
			returnedGuideDaily,
		);

		const result =
			await insertGuideDailyService.insertGuideDaily(dummyGuideDaily);

		expect(insertGuideDailyGatewayMock.insertGuideDaily).toHaveBeenCalledTimes(
			1,
		);
		expect(insertGuideDailyGatewayMock.insertGuideDaily).toHaveBeenCalledWith(
			dummyGuideDaily,
		);
		expect(result).toEqual(returnedGuideDaily);
	});
});
