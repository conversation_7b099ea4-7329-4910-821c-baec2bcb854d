import { AutorizacoesListagemDto } from '../../dto/AutorizacoesListagem';
import { FiltrosAutorizacoesListagemDto } from '../../dto/FiltrosAutorizacoesListagem';
import { Paginacao } from '../../dto/Paginacao';
import { FindAutorizacoesGateway } from '../../gateway/autorizacoes/findAutorizacoes.gateway';

export class FindAutorizacoesService {
	constructor(
		private readonly findAutorizacoesGateway: FindAutorizacoesGateway,
	) {}

	public async findListagem(
		companyId: number,
		filtros: Paginacao & FiltrosAutorizacoesListagemDto,
	): Promise<AutorizacoesListagemDto> {
		return await this.findAutorizacoesGateway.findListagem(companyId, filtros);
	}
}
