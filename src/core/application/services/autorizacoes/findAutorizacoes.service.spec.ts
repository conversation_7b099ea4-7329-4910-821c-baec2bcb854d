import { FindAutorizacoesService } from './findAutorizacoes.service';
import { FindAutorizacoesGateway } from '../../gateway/autorizacoes/findAutorizacoes.gateway';
import { AutorizacoesListagemDto } from '../../dto/AutorizacoesListagem';
import { Paginacao } from '../../dto/Paginacao';
import { FiltrosAutorizacoesListagemDto } from '../../dto/FiltrosAutorizacoesListagem';

describe('FindAutorizacoesService', () => {
	let findAutorizacoesService: FindAutorizacoesService;
	let findAutorizacoesGatewayMock: jest.Mocked<FindAutorizacoesGateway>;

	const mockAutorizacao = {
		id: 1,
		nomePaciente: '<PERSON>',
		codBeneficiario: '123456',
		carater: 'Eletivo',
		dataPedido: '01/01/2024',
		dataLimite: new Date(),
		pac: 'PAC123',
		prestador: 'Hospital ABC',
		status: {
			id: '1',
			descricao: 'Em Análise',
			cor: 'yellow',
		},
	} as unknown as AutorizacoesListagemDto['autorizacoes'][number];

	const mockListagem: AutorizacoesListagemDto = {
		pagina: 1,
		quantidadeTotal: 1,
		autorizacoes: [mockAutorizacao],
	};

	beforeEach(() => {
		findAutorizacoesGatewayMock = {
			findListagem: jest.fn(),
		} as unknown as jest.Mocked<FindAutorizacoesGateway>;

		findAutorizacoesService = new FindAutorizacoesService(
			findAutorizacoesGatewayMock,
		);
	});

	describe('findListagem', () => {
		it('deve chamar o gateway e retornar a listagem de autorizações', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
				search: 'João',
				dataInicio: new Date('2024-01-01'),
				dataFim: new Date('2024-01-31'),
			};

			findAutorizacoesGatewayMock.findListagem.mockResolvedValueOnce(
				mockListagem,
			);

			const result = await findAutorizacoesService.findListagem(
				companyId,
				filtros,
			);

			expect(findAutorizacoesGatewayMock.findListagem).toHaveBeenCalledWith(
				companyId,
				filtros,
			);
			expect(findAutorizacoesGatewayMock.findListagem).toHaveBeenCalledTimes(1);

			expect(result).toBeDefined();
			expect(result.pagina).toBe(mockListagem.pagina);
			expect(result.quantidadeTotal).toBe(mockListagem.quantidadeTotal);
			expect(result.autorizacoes).toHaveLength(1);
			expect(result.autorizacoes[0]).toEqual(mockAutorizacao);
		});

		it('deve retornar lista vazia quando não encontrar autorizações', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
			};

			const emptyListagem: AutorizacoesListagemDto = {
				pagina: 1,
				quantidadeTotal: 0,
				autorizacoes: [],
			};

			findAutorizacoesGatewayMock.findListagem.mockResolvedValueOnce(
				emptyListagem,
			);

			const result = await findAutorizacoesService.findListagem(
				companyId,
				filtros,
			);

			expect(findAutorizacoesGatewayMock.findListagem).toHaveBeenCalledWith(
				companyId,
				filtros,
			);
			expect(result.autorizacoes).toHaveLength(0);
			expect(result.quantidadeTotal).toBe(0);
		});

		it('deve propagar erro quando gateway falhar', async () => {
			const companyId = 1;
			const filtros: Paginacao & FiltrosAutorizacoesListagemDto = {
				page: 1,
				limit: 10,
			};

			const errorMessage = 'Erro ao buscar autorizações';
			findAutorizacoesGatewayMock.findListagem.mockRejectedValueOnce(
				new Error(errorMessage),
			);

			await expect(
				findAutorizacoesService.findListagem(companyId, filtros),
			).rejects.toThrow(errorMessage);

			expect(findAutorizacoesGatewayMock.findListagem).toHaveBeenCalledWith(
				companyId,
				filtros,
			);
		});
	});
});
