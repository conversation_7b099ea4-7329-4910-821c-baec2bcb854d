import { InsertBedHospitalGateway } from 'src/core/application/gateway/bedHospital/insertBedHospital.gateway';
import { BedHospital } from 'src/core/domain/BedHospital';
import { Hospital } from 'src/core/domain/Hospital';
import { InsertBedHospitalService } from './insertBedHospital.service';

describe('InsertBedHospitalService', () => {
	let insertBedHospitalService: InsertBedHospitalService;
	let insertBedHospitalGateway: jest.Mocked<InsertBedHospitalGateway>;

	beforeEach(() => {
		insertBedHospitalGateway = {
			insertBedHospital: jest.fn(),
		} as unknown as jest.Mocked<InsertBedHospitalGateway>;

		insertBedHospitalService = new InsertBedHospitalService(
			insertBedHospitalGateway,
		);
	});

	it('should insert a bed hospital and return the inserted bed hospital', async () => {
		const bedHospitalInput = new BedHospital(
			null, // id
			new Date('2022-01-01T00:00:00Z'), // created
			new Date('2022-01-01T00:00:00Z'), // admissionIn
			'Room 101', // accommodation
			true, // isCenso
			false, // isTransfer
			150.75, // price
			true, // enabled
			{ id: 10 } as Hospital,
			new Date('2022-01-02T00:00:00Z'), // updated
		);

		const insertedBedHospital = new BedHospital(
			10, // id gerado
			bedHospitalInput.created,
			bedHospitalInput.admissionIn,
			bedHospitalInput.accommodation,
			bedHospitalInput.isCenso,
			bedHospitalInput.isTransfer,
			bedHospitalInput.price,
			bedHospitalInput.enabled,
			bedHospitalInput.hospital,
			bedHospitalInput.updated,
			bedHospitalInput.changed,
			bedHospitalInput.admissionOut,
			bedHospitalInput.patientWayId,
			bedHospitalInput.bedNumber,
			bedHospitalInput.speciality,
			bedHospitalInput.accommodationCustom,
			bedHospitalInput.userId,
			bedHospitalInput.accommodationIsolation,
			bedHospitalInput.codIntegration,
		);

		insertBedHospitalGateway.insertBedHospital.mockResolvedValue(
			insertedBedHospital,
		);

		const result =
			await insertBedHospitalService.insertBedHospital(bedHospitalInput);

		expect(insertBedHospitalGateway.insertBedHospital).toHaveBeenCalledWith(
			bedHospitalInput,
		);
		expect(result).toEqual(insertedBedHospital);
	});
});
