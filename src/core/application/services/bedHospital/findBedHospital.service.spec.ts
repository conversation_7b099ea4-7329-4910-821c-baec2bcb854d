import { BedHospital } from 'src/core/domain/BedHospital';
import { FindBedHospitalGateway } from '../../gateway/bedHospital/findBedHospital.gateway';
import { FindBedHospitalService } from './findBedHospital.service';

describe('FindBedHospitalService', () => {
	let findBedHospitalGateway: jest.Mocked<FindBedHospitalGateway>;
	let findBedHospitalService: FindBedHospitalService;

	beforeEach(() => {
		findBedHospitalGateway = {
			findByAdmissionInAndHospitalization: jest.fn(),
		} as unknown as jest.Mocked<FindBedHospitalGateway>;

		findBedHospitalService = new FindBedHospitalService(findBedHospitalGateway);
	});

	it('should find bed hospital by admission in and hospitalization', () => {
		const bedHospital = new BedHospital(
			1,
			new Date(),
			new Date(),
			'',
			true,
			false,
			1,
			true,
			{ id: 1 },
			new Date(),
			new Date(),
			new Date(),
			1,
			'',
			'',
			'',
			null,
			'',
			1,
		);
		findBedHospitalGateway.findByAdmissionInAndHospitalization.mockResolvedValue(
			bedHospital,
		);

		const result = findBedHospitalService.findByAdmissionInAndHospitalization(
			new Date(),
			1,
		);

		expect(result).resolves.toEqual(bedHospital);
	});
});
