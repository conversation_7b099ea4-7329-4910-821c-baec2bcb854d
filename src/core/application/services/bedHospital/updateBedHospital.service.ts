import { BedHospital } from 'src/core/domain/BedHospital';
import { UpdateBedHospitalGateway } from '../../gateway/bedHospital/updateBedHospital.gateway';
import { UpdateResult } from 'typeorm';

export class UpdateBedHospitalService {
	constructor(
		private readonly updateBedHospitalGateway: UpdateBedHospitalGateway,
	) {}

	public async update(
		id: number,
		bedHospital: Partial<BedHospital>,
	): Promise<UpdateResult> {
		const result = await this.updateBedHospitalGateway.update(id, bedHospital);
		return result;
	}
}
