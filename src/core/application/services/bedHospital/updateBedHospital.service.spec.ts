import { BedHospital } from 'src/core/domain/BedHospital';
import { UpdateBedHospitalGateway } from '../../gateway/bedHospital/updateBedHospital.gateway';
import { UpdateBedHospitalService } from './updateBedHospital.service';
import { UpdateResult } from 'typeorm';

describe('UpdateBedHospitalService', () => {
	let updateBedHospitalGateway: jest.Mocked<UpdateBedHospitalGateway>;
	let updateBedHospitalService: UpdateBedHospitalService;

	beforeEach(() => {
		updateBedHospitalGateway = {
			update: jest.fn(),
		} as unknown as jest.Mocked<UpdateBedHospitalGateway>;

		updateBedHospitalService = new UpdateBedHospitalService(
			updateBedHospitalGateway,
		);
	});

	it('should update bed hospital', () => {
		const bedHospital = new BedHospital(
			1,
			new Date(),
			new Date(),
			'',
			true,
			false,
			1,
			true,
			{ id: 1 },
			new Date(),
			new Date(),
			new Date(),
			1,
			'',
			'',
			'',
			null,
			'',
			1,
		);
		updateBedHospitalGateway.update.mockResolvedValue(new UpdateResult());

		const result = updateBedHospitalService.update(1, bedHospital);

		expect(result).resolves.toEqual(new UpdateResult());
	});
});
