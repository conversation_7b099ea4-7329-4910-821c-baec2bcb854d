import { BedHospital } from 'src/core/domain/BedHospital';
import { InsertBedHospitalGateway } from '../../gateway/bedHospital/insertBedHospital.gateway';

export class InsertBedHospitalService {
	constructor(
		private readonly insertBedHospitalGateway: InsertBedHospitalGateway,
	) {}

	public async insertBedHospital(
		bedHospital: BedHospital,
	): Promise<BedHospital> {
		const result =
			await this.insertBedHospitalGateway.insertBedHospital(bedHospital);
		return result;
	}
}
