import { BedHospital } from 'src/core/domain/BedHospital';
import { FindBedHospitalGateway } from '../../gateway/bedHospital/findBedHospital.gateway';

export class FindBedHospitalService {
	constructor(
		private readonly findBedHospitalGateway: FindBedHospitalGateway,
	) {}

	public async findByAdmissionInAndHospitalization(
		admissionIn: Date,
		hospitalizationId: number,
	): Promise<BedHospital> {
		return await this.findBedHospitalGateway.findByAdmissionInAndHospitalization(
			admissionIn,
			hospitalizationId,
		);
	}
}
