import { FindCensoDadosService } from './findCensoDados.service';
import { FindCensoDadosGateway } from '../../gateway/censoDados/findCensoDados.gateway';
import { CensoDadosListagem } from '../../dto/CensoDadosListagem';
import { Paginacao } from '../../dto/Paginacao';
import { CensoDados } from 'src/core/domain/CensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { CensoDadosMapper } from 'src/shared/mapper/censoDados/censoDados.mapper';
import { CensoDadosEntity } from 'src/gateway/entities/censoDados.entity';
import { SeverityCensoConflicts } from '../../enums/typeConflicts/SeverityCensoConflicts.enum';

const mockCensoDados = new CensoDados(
	1,
	100,
	200,
	1,
	new Date('2023-01-01'),
	1,
	new Date('2023-02-01'),
	'São Paulo',
	'Hospital X',
	'Controle 123',
	new Date('1990-01-01'),
	new Date('2023-01-10'),
	new Date('2023-01-15'),
	'Alta Médica',
	'Diagnóstico Principal',
	'Diagnóstico Secundário',
	new Date('2023-01-20'),
	'Urgente',
	'Clínica',
	'GUIA123',
	'Ativo',
	'José da Silva',
	'1234567890',
	'São Paulo',
	'SP',
	false,
	'Tipo A',
	0,
	'SP-Regional',
	'Controle Tipo 1',
	10,
	'HOSP123',
	'PLANO123',
	'Plano de Saúde XYZ',
	'EMPRESA123',
	'Empresa Teste',
	'Ativo',
	new Date('2023-01-01'),
	null,
	null,
	[new TipoConflito(1, 'conflito', SeverityCensoConflicts.ERROR)],
);

const mockCensoDadosEntity: CensoDadosEntity = {
	id: 1001,
	dataCriacao: new Date('2023-01-01T10:00:00Z'),
	dataExclusao: new Date('2023-06-01T10:00:00Z'),
	dataEdicao: new Date('2023-04-01T10:00:00Z'),
	dataExpiracao: new Date('2023-12-31T10:00:00Z'),
	censoId: 1,
	companyId: 2001,
	userId: 3011,
	conflito: 1,
	data: new Date('2023-05-01T10:00:00Z'),
	municipio: 'São Paulo',
	hospitalCredenciado: 'Hospital da Beneficência',
	controle: 'ABC123DEF',
	dtNascimento: new Date('1985-09-10T00:00:00Z'),
	dataInternacao: new Date('2023-05-10T10:00:00Z'),
	dataAlta: new Date('2023-05-20T10:00:00Z'),
	motivoAlta: 'Alta médica após tratamento de urgência',
	diagnostico: 'Pneumonia bilateral',
	diagnosticoSecundario: 'Infecção respiratória',
	previsaoAlta: new Date('2023-05-25T10:00:00Z'),
	caraterInternacao: 'Urgente',
	tipoInternacao: 'Clínica geral',
	codigoGuia: 'GU456123',
	altoCustoStatus: 'Aprovado',
	nomeBeneficiario: 'João da Silva',
	codBeneficiario: '1234567890',
	cidadeBeneficiario: 'São Paulo',
	estadoBeneficiario: 'SP',
	recemNascido: false,
	tipoCliente: 'A',
	valorDiaria: '500.00',
	regionalBeneficiario: 'São Paulo - Região Central',
	tipoControle: 'Controle intensivo',
	diariasAutorizadas: 10,
	codigoHospital: 'HOSP12345',
	codigoPlano: 'PLANO67890',
	nomePlano: 'Plano de Saúde Básico',
	codigoEmpresa: 'EMPRESA1001',
	nomeEmpresa: 'Empresa XYZ Ltda',
	statusPlano: 'Ativo',
	dataPlanoDesde: new Date('2023-01-01T10:00:00Z'),
	censo: null,
	linhasConflitadas: [],
	acomodacao: '',
};

const mockCensoDadosListagem = new CensoDadosListagem(1, 10, 10, 10, [
	mockCensoDados,
]);

describe('FindCensoDadosService', () => {
	let findCensoDadosService: FindCensoDadosService.FindCensoDadosService;
	let findCensoDadosGatewayMock: FindCensoDadosGateway;

	beforeEach(async () => {
		findCensoDadosGatewayMock = {
			findByCensoId: jest.fn(),
			findListagem: jest.fn(),
		} as unknown as jest.Mocked<FindCensoDadosGateway>;

		findCensoDadosService = new FindCensoDadosService.FindCensoDadosService(
			findCensoDadosGatewayMock,
		);
	});

	it('deve chamar o gateway e retornar os CensoDados por CensoId', async () => {
		const censoId = 100;

		(
			findCensoDadosGatewayMock.findByCensoId as jest.Mock
		).mockResolvedValueOnce([mockCensoDados]);

		const result = await findCensoDadosService.findByCensoId(censoId);

		expect(findCensoDadosGatewayMock.findByCensoId).toHaveBeenCalledWith(
			censoId,
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toStrictEqual(mockCensoDados);
	});

	it('deve chamar o gateway e retornar os CensoDados por CensoId passando pelo mapper', async () => {
		const censoId = 100;

		const censoDadosMapper =
			CensoDadosMapper.toCensoDados(mockCensoDadosEntity);

		(
			findCensoDadosGatewayMock.findByCensoId as jest.Mock
		).mockResolvedValueOnce([censoDadosMapper]);

		const result = await findCensoDadosService.findByCensoId(censoId);

		expect(findCensoDadosGatewayMock.findByCensoId).toHaveBeenCalledWith(
			censoId,
		);

		expect(result).toHaveLength(1);
		expect(result[0]).toStrictEqual(censoDadosMapper);
	});

	it('deve chamar o gateway e retornar a listagem de CensoDados', async () => {
		const censoId = 100;
		const pagination: Paginacao = { page: 1, limit: 10 };

		(findCensoDadosGatewayMock.findListagem as jest.Mock).mockResolvedValueOnce(
			mockCensoDadosListagem,
		);

		const result = await findCensoDadosService.findListagem(
			censoId,
			{ onlyError: false, onlyWarning: false },
			pagination,
		);

		expect(findCensoDadosGatewayMock.findListagem).toHaveBeenCalledWith(
			censoId,
			{ onlyError: false, onlyWarning: false },
			pagination,
		);

		expect(result).toBeDefined();
		expect(result.page).toBe(pagination.page);
		expect(result.totalQuantity).toBe(mockCensoDadosListagem.totalQuantity);
		expect(result.censoData).toHaveLength(1);
		expect(result).toStrictEqual(mockCensoDadosListagem);
	});
});
