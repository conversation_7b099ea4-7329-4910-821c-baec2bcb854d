import { CensoDados } from 'src/core/domain/CensoDados';
import { EditCensoDadosGateway } from '../../gateway/censoDados/editCensoDados.gateway';

export namespace EditCensoDadosService {
	export class EditCensoDadosService {
		constructor(
			private readonly editCensoDadosGateway: EditCensoDadosGateway,
		) {}

		async editCensoDados(censoDados: CensoDados[]): Promise<void> {
			await this.editCensoDadosGateway.editCensoDados(censoDados);
		}
	}
}
