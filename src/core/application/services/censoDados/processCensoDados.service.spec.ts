import { CensoDados } from 'src/core/domain/CensoDados';
import { FindCensoDadosService } from './findCensoDados.service';
import { ProcessCensoDadosService } from './processCensoDados.service';
import { CensoDadosChainFactory } from './chain/factory/censoDadosChain.factory';
import { RegistrarPacienteHandler } from './chain/registrarPaciente.handler';
import { ChangeCensoStatusService } from '../censo/changeCensoStatus.service';

describe('ProcessCensoDadosService', () => {
	let findCensoDadosService: jest.Mocked<FindCensoDadosService.FindCensoDadosService>;
	let processCensoDadosService: ProcessCensoDadosService.ProcessCensoDadosService;
	let censoDadosChainFactory: jest.Mocked<CensoDadosChainFactory>;
	let registrarPacienteHandler: jest.Mocked<RegistrarPacienteHandler>;
	let changeCensoStatusService: jest.Mocked<ChangeCensoStatusService.ChangeCensoStatusService>;
	beforeEach(() => {
		process.env.GET_CENSO_DADOS_TO_SEND_LIMIT = '10';
		registrarPacienteHandler = {
			handle: jest.fn(),
			next: null,
		} as unknown as jest.Mocked<RegistrarPacienteHandler>;
		censoDadosChainFactory = {
			createChain: jest.fn().mockReturnValue(registrarPacienteHandler),
		} as unknown as jest.Mocked<CensoDadosChainFactory>;

		findCensoDadosService = {
			findListagem: jest.fn(),
		} as unknown as jest.Mocked<FindCensoDadosService.FindCensoDadosService>;

		changeCensoStatusService = {
			changeStatus: jest.fn(),
		} as unknown as jest.Mocked<ChangeCensoStatusService.ChangeCensoStatusService>;

		processCensoDadosService =
			new ProcessCensoDadosService.ProcessCensoDadosService(
				findCensoDadosService,
				censoDadosChainFactory,
				changeCensoStatusService,
			);
	});

	it('deve chamar findListagem com página 0 e entrar em loop quando quantidadeTotal > 0', async () => {
		const initialResponse = {
			page: 1,
			totalQuantity: 10,
			totalAmountErrors: 0,
			totalAmountWarnings: 0,
			censoData: [
				{ id: 1, companyId: 10 } as CensoDados,
				{ id: 2, companyId: 10 } as CensoDados,
				{ id: 3, companyId: 10 } as CensoDados,
				{ id: 4, companyId: 10 } as CensoDados,
				{ id: 5, companyId: 10 } as CensoDados,
				{ id: 6, companyId: 10 } as CensoDados,
				{ id: 7, companyId: 10 } as CensoDados,
				{ id: 8, companyId: 10 } as CensoDados,
				{ id: 9, companyId: 10 } as CensoDados,
				{ id: 10, companyId: 10 } as CensoDados,
			],
		};

		findCensoDadosService.findListagem.mockResolvedValue(initialResponse);
		await processCensoDadosService.processCensoDados(1);

		expect(findCensoDadosService.findListagem).toHaveBeenCalledTimes(1);
		expect(findCensoDadosService.findListagem).toHaveBeenCalledWith(
			1,
			{
				onlyError: false,
				onlyWarning: false,
				noRowsWithWarnings: true,
			},
			{
				limit: 10,
				page: 1,
			},
		);
		expect(censoDadosChainFactory.createChain).toHaveBeenCalledWith(10);
		expect(registrarPacienteHandler.handle).toHaveBeenCalledTimes(10);
		expect(changeCensoStatusService.changeStatus).toHaveBeenCalled();
	});

	it('nao deve chamar findListagem quando quantidade total for 0 ', async () => {
		const initialResponse = {
			page: 0,
			totalQuantity: 0,
			totalAmountErrors: 0,
			totalAmountWarnings: 0,
			censoData: [] as CensoDados[],
		};

		findCensoDadosService.findListagem.mockResolvedValueOnce(initialResponse);

		await processCensoDadosService.processCensoDados(0);
		expect(censoDadosChainFactory.createChain).not.toHaveBeenCalled();
		expect(registrarPacienteHandler.handle).not.toHaveBeenCalled();
	});

	it('deve lançar um ErrorOnCensoDadosProcessmentException ao algo dar errado no meio do processo', async () => {
		findCensoDadosService.findListagem.mockRejectedValue(new Error());
		expect(
			async () => await processCensoDadosService.processCensoDados(1),
		).rejects.toThrow(
			'Um erro ocorreu ao enviar os dados do censo por favor contate o suporte',
		);
	});
});
