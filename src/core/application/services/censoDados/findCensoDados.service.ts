import { CensoDadosFilters } from '../../dto/CensoDadosFilters';
import { CensoDadosListagem } from '../../dto/CensoDadosListagem';
import { Paginacao } from '../../dto/Paginacao';
import { FindCensoDadosGateway } from '../../gateway/censoDados/findCensoDados.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';

export namespace FindCensoDadosService {
	export class FindCensoDadosService {
		constructor(
			private readonly findCensoDadosGateway: FindCensoDadosGateway,
		) {}

		public async findByCensoId(censoId: number): Promise<CensoDados[]> {
			return await this.findCensoDadosGateway.findByCensoId(censoId);
		}

		public async findListagem(
			censoId: number,
			filters: CensoDadosFilters,
			{ page, limit, search }: Paginacao,
		): Promise<CensoDadosListagem> {
			return await this.findCensoDadosGateway.findListagem(censoId, filters, {
				page,
				limit,
				search,
			});
		}
	}
}
