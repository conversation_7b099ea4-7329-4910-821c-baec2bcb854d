import { CensoDados } from 'src/core/domain/CensoDados';
import { EditCensoDadosGateway } from '../../gateway/censoDados/editCensoDados.gateway';
import { EditCensoDadosService } from './editCensoDados.service';

describe('editCensoDadosService', () => {
	let censoDados: CensoDados[];
	let editCensoDadosGateway: jest.Mocked<EditCensoDadosGateway>;
	let editCensoDadosService: EditCensoDadosService.EditCensoDadosService;
	beforeEach(() => {
		censoDados = [
			new CensoDados(
				1,
				100,
				200,
				1,
				new Date('2023-01-01'),
				1,
				new Date('2023-02-01'),
				'São Paulo',
				'Hospital X',
				'Controle 123',
				new Date('1990-01-01'),
				new Date('2023-01-10'),
				new Date('2023-01-15'),
				'Alta Médica',
				'<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> Principal',
				'<PERSON>agnóstico Secundário',
				new Date('2023-01-20'),
				'Urgent<PERSON>',
				'Cl<PERSON><PERSON>',
				'GUIA123',
				'Ativo',
				'<PERSON>',
				'1234567890',
				'São Paulo',
				'SP',
				false,
				'Tipo A',
				0,
				'SP-Regional',
				'Controle Tipo 1',
				10,
				'HOSP123',
				'PLANO123',
				'Plano de Saúde XYZ',
				'EMPRESA123',
				'Empresa Teste',
				'Ativo',
				new Date('2023-01-01'),
				null,
				null,
				[],
			),
		];
		editCensoDadosGateway = {
			editCensoDados: jest.fn(),
		} as jest.Mocked<EditCensoDadosGateway>;

		editCensoDadosService = new EditCensoDadosService.EditCensoDadosService(
			editCensoDadosGateway,
		);
	});

	it('deve fazer a edição de uma lista de CensoDados ', () => {
		editCensoDadosService.editCensoDados(censoDados);

		expect(editCensoDadosGateway.editCensoDados).toHaveBeenCalledWith(
			censoDados,
		);
		expect(editCensoDadosGateway.editCensoDados).toHaveBeenCalledTimes(1);
	});
});
