import { InsertCensoDadosGateway } from '../../gateway/censoDados/insertCensoDados.gateway';
import { InsereCensoDados } from '../../dto/InsereCensoDados';
import { TipoConflito } from 'src/core/domain/TipoConflito';
export namespace InsertCensoDadosService {
	export class InsertCensoDadosService {
		constructor(
			private readonly insertCensoDadosGateway: InsertCensoDadosGateway,
		) {}

		public async insert(insereCensoDados: InsereCensoDados): Promise<void> {
			insereCensoDados.censoDados.conflito = this.analyzeIsConflict(
				insereCensoDados.tiposConflito,
			);
			await this.insertCensoDadosGateway.insert(insereCensoDados);
		}

		private analyzeIsConflict(tiposConflito: TipoConflito[]): number {
			if (tiposConflito) {
				const isConflito = tiposConflito.some((conflito) => conflito != null);

				return isConflito ? 1 : 0;
			}
			return 0;
		}
	}
}
