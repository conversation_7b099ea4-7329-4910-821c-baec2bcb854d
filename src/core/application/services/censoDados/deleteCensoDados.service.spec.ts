import { DeleteCensoDadosGateway } from '../../gateway/censoDados/deleteCensoDados.gateway';
import { DeleteCensoDadosService } from './deleteCensoDados.service';

describe('DeleteCensoDadosService', () => {
	let deleteCensoDadosGateway: jest.Mocked<DeleteCensoDadosGateway>;
	let deleteCensoDadosService: DeleteCensoDadosService;

	beforeEach(() => {
		deleteCensoDadosGateway = {
			deleteBatch: jest.fn(),
		} as jest.Mocked<DeleteCensoDadosGateway>;

		deleteCensoDadosService = new DeleteCensoDadosService(
			deleteCensoDadosGateway,
		);
	});

	it('deve deletar um batch de CensoDados ', async () => {
		const censoDataIds = [1, 2, 3];
		await deleteCensoDadosService.deleteBatch(censoDataIds);
		expect(deleteCensoDadosGateway.deleteBatch).toHaveBeenCalledWith(
			censoDataIds,
		);
		expect(deleteCensoDadosGateway.deleteBatch).toHaveBeenCalledTimes(1);
	});
});
