import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertHospitalService } from '../../hospital/insert/insertHospital.service';

export class RegistrarHospitalHandler extends CensoDadosBaseHandler {
	constructor(private insertHospitalService: InsertHospitalService) {
		super();
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		if (!censoDados.hospitalCredenciado) {
			return;
		}
		const ids = await this.insertHospitalService.insertHospital(censoDados);
		dependentDto.hospitalId = ids.hospitalId;
		dependentDto.hospitalCompanyId = ids.hospitalCompanyId;
		await super.handle(censoDados, dependentDto);
		return;
	}
}
