import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarGuideDailyHandler } from './registrarGuideDaily.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideDailyService } from '../../guideDaily/insertGuideDaily.service';
import { GuideDaily } from 'src/core/domain/GuideDaily';

describe('RegistrarGuideDailyHandler', () => {
	let registrarGuideDailyHandler: RegistrarGuideDailyHandler;
	let insertGuideDailyServiceMock: jest.Mocked<InsertGuideDailyService>;
	let nextHandlerMock: CensoDadosBaseHandler;

	const mockCensoDados: CensoDados = {
		dataInternacao: new Date('2023-03-10T00:00:00Z'),
		acomodacao: 'Acomodação Teste',
	} as CensoDados;

	beforeEach(() => {
		insertGuideDailyServiceMock = {
			insertGuideDaily: jest
				.fn()
				.mockResolvedValue(new GuideDaily(456, { id: 123 })),
		} as unknown as jest.Mocked<InsertGuideDailyService>;

		registrarGuideDailyHandler = new RegistrarGuideDailyHandler(
			insertGuideDailyServiceMock,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
			setNext: jest.fn(),
		} as unknown as CensoDadosBaseHandler;
	});

	it('deve chamar insertGuideDailyService.insertGuideDaily e atualizar dependentDto.guideDailyId sem chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.guideId = 789;

		await registrarGuideDailyHandler.handle(mockCensoDados, dependentDto);

		expect(insertGuideDailyServiceMock.insertGuideDaily).toHaveBeenCalledTimes(
			1,
		);

		const guideDailyArg =
			insertGuideDailyServiceMock.insertGuideDaily.mock.calls[0][0];
		expect(guideDailyArg).toBeInstanceOf(GuideDaily);

		expect(dependentDto.guideDailyId).toBe(456);
	});

	it('deve chamar o próximo handler se configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.guideId = 789;
		registrarGuideDailyHandler.setNext(nextHandlerMock);

		await registrarGuideDailyHandler.handle(mockCensoDados, dependentDto);

		expect(insertGuideDailyServiceMock.insertGuideDaily).toHaveBeenCalledTimes(
			1,
		);
		expect(dependentDto.guideDailyId).toBe(456);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
