import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarHospitalHandler } from './registrarHospital.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertHospitalService } from '../../hospital/insert/insertHospital.service';

describe('RegistrarHospitalHandler', () => {
	let registrarHospitalHandler: RegistrarHospitalHandler;
	let nextHandlerMock: CensoDadosBaseHandler;
	let insertHospitalService: jest.Mocked<InsertHospitalService>;

	const mockCensoDados: CensoDados = {
		hospitalCredenciado: 'Hospital XYZ',
		companyId: 10,
		codigoEmpresa: '1234',
	} as CensoDados;

	beforeEach(() => {
		insertHospitalService = {
			insertHospital: jest.fn().mockResolvedValue({
				hospitalId: 123,
				hospitalCompanyId: 456,
			}),
		} as unknown as jest.Mocked<InsertHospitalService>;

		registrarHospitalHandler = new RegistrarHospitalHandler(
			insertHospitalService,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
		} as unknown as CensoDadosBaseHandler;
	});

	it('não deve chamar insertHospitalService se o codigo empresa for null', async () => {
		const censoDadosSemHospital = {
			codigoEmpresa: undefined,
		} as CensoDados;
		const dependentDto = new DependentDto(0);

		await registrarHospitalHandler.handle(censoDadosSemHospital, dependentDto);

		expect(insertHospitalService.insertHospital).not.toHaveBeenCalled();
		expect(dependentDto.hospitalId).toBeUndefined();
		expect(dependentDto.hospitalCompanyId).toBeUndefined();
	});

	it('não deve chamar insertHospitalService.insertHospital se hospitalCredenciado não existir', async () => {
		const censoDadosSemHospital = {
			hospitalCredenciado: undefined,
		} as CensoDados;
		const dependentDto = new DependentDto(0);

		await registrarHospitalHandler.handle(censoDadosSemHospital, dependentDto);

		expect(insertHospitalService.insertHospital).not.toHaveBeenCalled();
		expect(dependentDto.hospitalId).toBeUndefined();
		expect(dependentDto.hospitalCompanyId).toBeUndefined();
	});

	it('deve chamar insertHospitalService.insertHospital e atualizar dependentDto sem chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(0);

		await registrarHospitalHandler.handle(mockCensoDados, dependentDto);

		expect(insertHospitalService.insertHospital).toHaveBeenCalledWith(
			mockCensoDados,
		);
		expect(dependentDto.hospitalId).toBe(123);
		expect(dependentDto.hospitalCompanyId).toBe(456);
	});

	it('deve chamar o próximo handler se configurado', async () => {
		const dependentDto = new DependentDto(0);
		registrarHospitalHandler.setNext(nextHandlerMock);

		await registrarHospitalHandler.handle(mockCensoDados, dependentDto);

		expect(insertHospitalService.insertHospital).toHaveBeenCalledWith(
			mockCensoDados,
		);
		expect(dependentDto.hospitalId).toBe(123);
		expect(dependentDto.hospitalCompanyId).toBe(456);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
