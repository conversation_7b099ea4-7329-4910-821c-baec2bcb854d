import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarGuideStatusHandler } from './registrarGuideStatus.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideStatusService } from '../../guideStatus/insertGuideStatus.service';
import { GuideStatus } from 'src/core/domain/GuideStatus';

describe('RegistrarGuideStatusHandler', () => {
	let registrarGuideStatusHandler: RegistrarGuideStatusHandler;
	let insertGuideStatusServiceMock: jest.Mocked<InsertGuideStatusService>;
	let nextHandlerMock: CensoDadosBaseHandler;

	const mockCensoDados: CensoDados = {
		dataInternacao: new Date('2023-03-10T00:00:00Z'),
		acomodacao: 'Acomodação Teste',
		userId: 42,
	} as CensoDados;

	beforeEach(() => {
		insertGuideStatusServiceMock = {
			insertGuideService: jest
				.fn()
				.mockResolvedValue(new GuideStatus(321, { id: 789 })),
		} as unknown as jest.Mocked<InsertGuideStatusService>;

		registrarGuideStatusHandler = new RegistrarGuideStatusHandler(
			insertGuideStatusServiceMock,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
			setNext: jest.fn(),
		} as unknown as CensoDadosBaseHandler;
	});

	it('deve chamar insertGuideStatusService.insertGuideService e atualizar dependentDto.guideStatusId sem chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.guideId = 555;

		await registrarGuideStatusHandler.handle(mockCensoDados, dependentDto);

		expect(
			insertGuideStatusServiceMock.insertGuideService,
		).toHaveBeenCalledTimes(1);

		const guideStatusArg =
			insertGuideStatusServiceMock.insertGuideService.mock.calls[0][0];
		expect(guideStatusArg).toBeInstanceOf(GuideStatus);

		expect(dependentDto.guideStatusId).toBe(321);
	});

	it('deve chamar o próximo handler se configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.guideId = 555;
		registrarGuideStatusHandler.setNext(nextHandlerMock);

		await registrarGuideStatusHandler.handle(mockCensoDados, dependentDto);

		expect(
			insertGuideStatusServiceMock.insertGuideService,
		).toHaveBeenCalledTimes(1);
		expect(dependentDto.guideStatusId).toBe(321);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
