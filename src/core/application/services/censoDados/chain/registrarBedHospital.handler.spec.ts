import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarBedHospitalHandler } from './registrarBedHospital.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertBedHospitalService } from '../../bedHospital/insertBedHospital.service';
import { BedHospital } from 'src/core/domain/BedHospital';
import { FindBedHospitalService } from '../../bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from '../../bedHospital/updateBedHospital.service';

describe('RegistrarBedHospitalHandler', () => {
	let registrarBedHospitalHandler: RegistrarBedHospitalHandler;
	let nextHandlerMock: CensoDadosBaseHandler;
	let insertBedHospitalService: jest.Mocked<InsertBedHospitalService>;
	let findBedHospitalService: jest.Mocked<FindBedHospitalService>;
	let updateBedHospitalService: jest.Mocked<UpdateBedHospitalService>;

	const mockCensoDados: CensoDados = {
		dataInternacao: new Date('2023-03-10T00:00:00Z'),
		acomodacao: 'Acomodação Teste',
	} as CensoDados;

	beforeEach(() => {
		insertBedHospitalService = {
			insertBedHospital: jest.fn().mockResolvedValue({ id: 100 }),
		} as unknown as jest.Mocked<InsertBedHospitalService>;

		findBedHospitalService = {
			findByAdmissionInAndHospitalization: jest.fn().mockResolvedValue(null),
		} as unknown as jest.Mocked<FindBedHospitalService>;

		updateBedHospitalService = {
			update: jest.fn().mockResolvedValue(undefined),
		} as unknown as jest.Mocked<UpdateBedHospitalService>;

		registrarBedHospitalHandler = new RegistrarBedHospitalHandler(
			insertBedHospitalService,
			findBedHospitalService,
			updateBedHospitalService,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
		} as unknown as CensoDadosBaseHandler;
	});

	it('should upsert BedHospital and set bedHospitalId when handle is called', async () => {
		const censoDados = {
			dataInternacao: new Date('2023-01-01'),
			dataAlta: new Date('2023-01-10'),
			acomodacao: 'ENFERMARIA',
			userId: 1,
		} as CensoDados;

		const dependentDto: DependentDto = {
			hospitalId: 1,
			patientWay: 12345,
			bedHospitalId: undefined,
		};

		await registrarBedHospitalHandler.handle(censoDados, dependentDto);

		expect(dependentDto.bedHospitalId).toBe(100);
		expect(insertBedHospitalService.insertBedHospital).toHaveBeenCalled();
	});

	it('should update existing BedHospital if it exists', async () => {
		const censoDados = {
			dataInternacao: new Date('2023-01-01'),
			dataAlta: new Date('2023-01-10'),
			acomodacao: 'ENFERMARIA',
			userId: 1,
		} as CensoDados;

		const dependentDto: DependentDto = {
			hospitalId: 1,
			patientWay: 12345,
			bedHospitalId: undefined,
		};

		const existingBed = { id: 99 } as BedHospital;

		findBedHospitalService.findByAdmissionInAndHospitalization.mockResolvedValue(
			existingBed,
		);

		await registrarBedHospitalHandler.handle(censoDados, dependentDto);

		expect(dependentDto.bedHospitalId).toBe(99);
		expect(updateBedHospitalService.update).toHaveBeenCalledWith(99, {
			accommodation: censoDados.acomodacao,
			price: censoDados.valorDiaria,
			admissionOut: censoDados.dataAlta,
		});
	});

	it('deve chamar insertBedHospitalService.insertBedHospital e atualizar dependentDto sem chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(100);

		await registrarBedHospitalHandler.handle(mockCensoDados, dependentDto);

		expect(insertBedHospitalService.insertBedHospital).toHaveBeenCalledTimes(1);

		const bedHospitalArg =
			insertBedHospitalService.insertBedHospital.mock.calls[0][0];
		expect(bedHospitalArg).toBeInstanceOf(BedHospital);

		expect(dependentDto.bedHospitalId).toBe(100);
	});

	it('deve chamar o próximo handler se configurado', async () => {
		const dependentDto = new DependentDto(100);
		registrarBedHospitalHandler.setNext(nextHandlerMock);

		await registrarBedHospitalHandler.handle(mockCensoDados, dependentDto);

		expect(insertBedHospitalService.insertBedHospital).toHaveBeenCalledTimes(1);
		expect(dependentDto.bedHospitalId).toBe(100);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
