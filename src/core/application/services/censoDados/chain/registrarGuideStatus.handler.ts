import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideStatusService } from '../../guideStatus/insertGuideStatus.service';
import { GuideStatus } from 'src/core/domain/GuideStatus';

export class RegistrarGuideStatusHandler extends CensoDadosBaseHandler {
	constructor(
		private readonly insertGuideStatusService: InsertGuideStatusService,
	) {
		super();
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		const guideSatus = new GuideStatus(null, { id: dependentDto.guideId });
		const result =
			await this.insertGuideStatusService.insertGuideService(guideSatus);
		dependentDto.guideStatusId = result.id;
		await super.handle(censoDados, dependentDto);
	}
}
