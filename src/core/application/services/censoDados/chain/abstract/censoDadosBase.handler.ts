import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosHandler } from './censoDados.handler';
import { DependentDto } from '../dto/dependent.dto';

export abstract class CensoDadosBaseHandler implements CensoDadosHandler {
	protected next: CensoDadosBaseHandler;
	constructor() {}

	public setNext(handler: CensoDadosBaseHandler): void {
		this.next = handler;
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		if (this.next == null) {
			return;
		}
		console.info(`
			invoking handler : ${this.next.constructor.name} with :
			| ${censoDados.id}
			| ${censoDados.nomeBeneficiario}
			| ${censoDados.codBeneficiario}
		  `);
		await this.next.handle(censoDados, dependentDto);
	}
}
