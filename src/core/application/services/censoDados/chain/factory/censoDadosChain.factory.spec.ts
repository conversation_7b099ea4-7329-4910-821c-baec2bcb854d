import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { CensoDadosChainBuilder } from '../abstract/censoDadosChain.builder';
import { RegistrarPacienteHandler } from '../registrarPaciente.handler';
import { CensoDadosChainFactory } from './censoDadosChain.factory';
import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { InsertHospitalService } from '../../../hospital/insert/insertHospital.service';
import { InsertBedHospitalService } from '../../../bedHospital/insertBedHospital.service';
import { InsertFormCidsService } from '../../../formCids/InsertFormCids.service';
import { InsertGuideService } from '../../../guide/insertGuide.service';
import { InsertGuideDailyService } from '../../../guideDaily/insertGuideDaily.service';
import { InsertGuideStatusService } from '../../../guideStatus/insertGuideStatus.service';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';
import { FindBedHospitalService } from '../../../bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from '../../../bedHospital/updateBedHospital.service';

describe('CensoDadosChainFactory', () => {
	let censoDadosChainFactory: CensoDadosChainFactory;
	let censoDadosChainBuilder: jest.Mocked<CensoDadosChainBuilder>;
	let registrarPacienteHandler: jest.Mocked<RegistrarPacienteHandler>;
	let insertPatientGateway: jest.Mocked<InsertPatientGateway>;
	let insertPatientWayGateway: jest.Mocked<InsertHospitalizationGateway>;
	let insertHospitalService: jest.Mocked<InsertHospitalService>;
	let insertBedHospitalService: jest.Mocked<InsertBedHospitalService>;
	let insertFormCidsService: jest.Mocked<InsertFormCidsService>;
	let insertGuideService: jest.Mocked<InsertGuideService>;
	let insertGuideDailyService: jest.Mocked<InsertGuideDailyService>;
	let insertGuideStatusService: jest.Mocked<InsertGuideStatusService>;
	let findPatientGateway: jest.Mocked<FindPatientGateway>;
	let findHospitalizationGateway: jest.Mocked<FindHospitalizationGateway>;
	let updateHospitalizationGateway: jest.Mocked<UpdateHospitalizationGateway>;
	let findBedHospitalService: FindBedHospitalService;
	let updateBedHospitalService: UpdateBedHospitalService;

	beforeEach(() => {
		insertPatientGateway = {
			insertPatient: jest.fn(),
		} as jest.Mocked<InsertPatientGateway>;

		insertPatientWayGateway = {
			insert: jest.fn(),
		} as jest.Mocked<InsertHospitalizationGateway>;

		registrarPacienteHandler = {
			handle: jest.fn(),
			setNext: jest.fn(),
			next: registrarPacienteHandler,
		} as unknown as jest.Mocked<RegistrarPacienteHandler>;

		censoDadosChainBuilder = {
			make: jest.fn().mockReturnValue(registrarPacienteHandler),
			setNext: jest.fn().mockReturnThis(),
		} as jest.Mocked<CensoDadosChainBuilder>;

		insertHospitalService = {
			insertHospital: jest.fn(),
		} as unknown as jest.Mocked<InsertHospitalService>;

		insertBedHospitalService = {
			insertBedHospital: jest.fn(),
		} as unknown as jest.Mocked<InsertBedHospitalService>;

		insertFormCidsService = {
			insertFormCids: jest.fn(),
		} as unknown as jest.Mocked<InsertFormCidsService>;

		insertGuideService = {
			insertGuide: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideService>;

		insertGuideDailyService = {
			insertGuideDaily: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideDailyService>;

		insertGuideStatusService = {
			insertGuideService: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideStatusService>;

		findPatientGateway = {
			findByCodBeneficiary: jest.fn(),
			findByCodBeneficiaryAndNotName: jest.fn(),
			findByNameBirthAndNotCodBeneficiary: jest.fn(),
		} as jest.Mocked<FindPatientGateway>;

		findHospitalizationGateway = {
			findActiveHospitalizationByPatient: jest.fn(),
			findBeetweenPeriods: jest.fn(),
			findByAdmissionInAndPatient: jest.fn(),
			findByCodBeneficiario: jest.fn(),
			findByCodigoGuia: jest.fn(),
			findByNomeNascimento: jest.fn(),
		} as jest.Mocked<FindHospitalizationGateway>;

		updateHospitalizationGateway = {
			update: jest.fn(),
		} as jest.Mocked<UpdateHospitalizationGateway>;

		findBedHospitalService = {
			findByAdmissionInAndHospitalization: jest.fn(),
		} as unknown as FindBedHospitalService;

		updateBedHospitalService = {
			update: jest.fn(),
		} as unknown as UpdateBedHospitalService;

		censoDadosChainFactory = new CensoDadosChainFactory(
			censoDadosChainBuilder,
			insertPatientGateway,
			insertPatientWayGateway,
			insertHospitalService,
			insertBedHospitalService,
			insertFormCidsService,
			insertGuideService,
			insertGuideDailyService,
			insertGuideStatusService,
			findPatientGateway,
			findHospitalizationGateway,
			updateHospitalizationGateway,
			findBedHospitalService,
			updateBedHospitalService,
		);
	});

	it.each([10, 3, 22, 99])(
		'deve criar uma cadeia de censoDados',
		(companyId) => {
			const createdChain = censoDadosChainFactory.createChain(companyId);
			expect(createdChain).toHaveProperty('next');
		},
	);
});
