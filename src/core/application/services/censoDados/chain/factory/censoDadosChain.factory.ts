import { CensoDadosChainBuilder } from '../abstract/censoDadosChain.builder';
import { RegistrarPacienteHandler } from '../registrarPaciente.handler';
import { CensoDadosBaseHandler } from '../abstract/censoDadosBase.handler';
import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { RegistrarHospitalizationHandler } from '../registrarHospitalization.handler';
import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { RegistrarHospitalHandler } from '../registrarHospital.handler';
import { InsertHospitalService } from '../../../hospital/insert/insertHospital.service';
import { RegistrarBedHospitalHandler } from '../registrarBedHospital.handler';
import { InsertBedHospitalService } from '../../../bedHospital/insertBedHospital.service';
import { InsertFormCidsService } from '../../../formCids/InsertFormCids.service';
import { RegistrarFormCidsHandler } from '../registrarFormCids.handler';
import { RegistrarGuideChainHandler } from '../registrarGuideChain.handler';
import { InsertGuideService } from '../../../guide/insertGuide.service';
import { RegistrarGuideDailyHandler } from '../registrarGuideDaily.handler';
import { InsertGuideDailyService } from '../../../guideDaily/insertGuideDaily.service';
import { RegistrarGuideStatusHandler } from '../registrarGuideStatus.handler';
import { InsertGuideStatusService } from '../../../guideStatus/insertGuideStatus.service';
import Operadoras from 'src/shared/operadoras.enum';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';
import { FindBedHospitalService } from '../../../bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from '../../../bedHospital/updateBedHospital.service';

export class CensoDadosChainFactory {
	constructor(
		private readonly censoDadosChainBuilder: CensoDadosChainBuilder,
		private readonly insertPatientGateway: InsertPatientGateway,
		private readonly insertHospitalizationGateway: InsertHospitalizationGateway,
		private readonly insertHospitalService: InsertHospitalService,
		private readonly insertBedHospitalService: InsertBedHospitalService,
		private readonly insertFormCidsService: InsertFormCidsService,
		private readonly insertGuideService: InsertGuideService,
		private readonly insertGuideDailyService: InsertGuideDailyService,
		private readonly insertGuideStatusService: InsertGuideStatusService,
		private readonly findPatientGateway: FindPatientGateway,
		private readonly findHospitalizationGateway: FindHospitalizationGateway,
		private readonly updateHospitalizationGateway: UpdateHospitalizationGateway,
		private readonly findBedHospitalService: FindBedHospitalService,
		private readonly updateBedHospitalService: UpdateBedHospitalService,
	) {}

	public createChain(companyId: number): CensoDadosBaseHandler {
		switch (companyId) {
			case Operadoras.SEGUROS_UNIMED: {
				return this.censoDadosChainBuilder
					.setNext(
						new RegistrarPacienteHandler(
							this.insertPatientGateway,
							this.findPatientGateway,
						),
					)
					.setNext(
						new RegistrarHospitalizationHandler(
							this.insertHospitalizationGateway,
							this.findHospitalizationGateway,
							this.updateHospitalizationGateway,
						),
					)
					.setNext(new RegistrarHospitalHandler(this.insertHospitalService))
					.setNext(
						new RegistrarBedHospitalHandler(
							this.insertBedHospitalService,
							this.findBedHospitalService,
							this.updateBedHospitalService,
						),
					)
					.setNext(new RegistrarFormCidsHandler(this.insertFormCidsService))
					.setNext(new RegistrarGuideChainHandler(this.insertGuideService))
					.setNext(new RegistrarGuideDailyHandler(this.insertGuideDailyService))
					.setNext(
						new RegistrarGuideStatusHandler(this.insertGuideStatusService),
					)
					.make();
			}
			case Operadoras.MINHA_OPERADORA: {
				return this.censoDadosChainBuilder
					.setNext(
						new RegistrarPacienteHandler(
							this.insertPatientGateway,
							this.findPatientGateway,
						),
					)
					.setNext(
						new RegistrarHospitalizationHandler(
							this.insertHospitalizationGateway,
							this.findHospitalizationGateway,
							this.updateHospitalizationGateway,
						),
					)
					.setNext(new RegistrarHospitalHandler(this.insertHospitalService))
					.setNext(
						new RegistrarBedHospitalHandler(
							this.insertBedHospitalService,
							this.findBedHospitalService,
							this.updateBedHospitalService,
						),
					)
					.setNext(new RegistrarFormCidsHandler(this.insertFormCidsService))
					.setNext(new RegistrarGuideChainHandler(this.insertGuideService))
					.setNext(new RegistrarGuideDailyHandler(this.insertGuideDailyService))
					.setNext(
						new RegistrarGuideStatusHandler(this.insertGuideStatusService),
					)
					.make();
			}
			case Operadoras.UNIMED_VITORIA: {
				return this.censoDadosChainBuilder
					.setNext(
						new RegistrarPacienteHandler(
							this.insertPatientGateway,
							this.findPatientGateway,
						),
					)
					.setNext(
						new RegistrarHospitalizationHandler(
							this.insertHospitalizationGateway,
							this.findHospitalizationGateway,
							this.updateHospitalizationGateway,
						),
					)
					.setNext(new RegistrarHospitalHandler(this.insertHospitalService))
					.setNext(
						new RegistrarBedHospitalHandler(
							this.insertBedHospitalService,
							this.findBedHospitalService,
							this.updateBedHospitalService,
						),
					)
					.setNext(new RegistrarFormCidsHandler(this.insertFormCidsService))
					.setNext(new RegistrarGuideChainHandler(this.insertGuideService))
					.setNext(new RegistrarGuideDailyHandler(this.insertGuideDailyService))
					.setNext(
						new RegistrarGuideStatusHandler(this.insertGuideStatusService),
					)
					.make();
			}
			default:
				return this.createChain(Operadoras.MINHA_OPERADORA);
		}
	}
}
