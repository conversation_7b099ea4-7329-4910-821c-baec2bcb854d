import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideService } from '../../guide/insertGuide.service';
import { Guide } from 'src/core/domain/Guide';

export class RegistrarGuideChainHandler extends CensoDadosBaseHandler {
	constructor(private insertGuideService: InsertGuideService) {
		super();
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		const guide = new Guide(
			null,
			{ id: dependentDto.patient },
			{ id: dependentDto.patientWay },
			{ id: dependentDto.hospitalId },
			null,
			null,
			null,
			censoDados.codigoGuia,
			censoDados.data,
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			null,
			1,
			censoDados.userId,
		);
		const result = await this.insertGuideService.insertGuide(guide);
		dependentDto.guideId = result.id;
		await super.handle(censoDados, dependentDto);
		return;
	}
}
