import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarPacienteHandler } from './registrarPaciente.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { DependentDto } from './dto/dependent.dto';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';
import { Patient } from 'src/core/domain/Patient';

describe('RegistrarPacienteHandler', () => {
	let registrarPacienteHandler: RegistrarPacienteHandler;
	let nextHandlerMock: CensoDadosBaseHandler;
	let insertPatientGateway: jest.Mocked<InsertPatientGateway>;
	const patient = 1;
	const mockCensoDados = new CensoDados(
		1,
		100,
		200,
		1,
		new Date('2023-01-01'),
		1,
		new Date('2023-02-01'),
		'São Paulo',
		'Hospital X',
		'Controle 123',
		new Date('1990-01-01'),
		new Date('2023-01-10'),
		new Date('2023-01-15'),
		'Alta Médica',
		'Diagnóstico Principal',
		'Diagnóstico Secundário',
		new Date('2023-01-20'),
		'Urgente',
		'Clínica',
		'GUIA123',
		'Ativo',
		'José da Silva',
		'**********',
		'São Paulo',
		'SP',
		false,
		'Tipo A',
		0,
		'SP-Regional',
		'Controle Tipo 1',
		10,
		'HOSP123',
		'PLANO123',
		'Plano de Saúde XYZ',
		'EMPRESA123',
		'Empresa Teste',
		'Ativo',
		new Date('2023-01-01'),
		null,
	);
	let findPatientGateway: jest.Mocked<FindPatientGateway>;

	beforeEach(() => {
		insertPatientGateway = {
			insertPatient: jest.fn().mockResolvedValue(patient),
		} as jest.Mocked<InsertPatientGateway>;
		findPatientGateway = {
			findByCodBeneficiary: jest.fn(),
			findByCodBeneficiaryAndNotName: jest.fn(),
			findByNameBirthAndNotCodBeneficiary: jest.fn(),
		} as jest.Mocked<FindPatientGateway>;

		registrarPacienteHandler = new RegistrarPacienteHandler(
			insertPatientGateway,
			findPatientGateway,
		);

		nextHandlerMock = new RegistrarPacienteHandler(
			insertPatientGateway,
			findPatientGateway,
		);
	});

	it('deve nao registrar um paciente se ele ja existir e chamar o próximo handler quando configurado', async () => {
		const spyOnNextHandle = jest.spyOn(nextHandlerMock, 'handle');
		const dependtDto = new DependentDto(patient);
		findPatientGateway.findByCodBeneficiary.mockReturnValue({
			id: 1,
		} as unknown as Promise<Patient>);
		registrarPacienteHandler.setNext(nextHandlerMock);
		await registrarPacienteHandler.handle(mockCensoDados, dependtDto);

		expect(findPatientGateway.findByCodBeneficiary).toHaveBeenCalled();
		expect(registrarPacienteHandler['next']).toBe(nextHandlerMock);
		expect(insertPatientGateway.insertPatient).not.toHaveBeenCalled();
		expect(spyOnNextHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
	});
	it('deve não registrar um paciente e não chamar o próximo handler', () => {
		const spyOnHandle = jest.spyOn(registrarPacienteHandler, 'handle');
		const dependtDto = new DependentDto(1);
		const spyOnNextHandle = jest.spyOn(nextHandlerMock, 'handle');

		registrarPacienteHandler.handle(mockCensoDados, dependtDto);
		findPatientGateway.findByCodBeneficiary.mockReturnValue({
			id: 1,
		} as unknown as Promise<Patient>);

		expect(spyOnHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
		expect(findPatientGateway.findByCodBeneficiary).toHaveBeenCalledTimes(1);
		expect(insertPatientGateway.insertPatient).toHaveBeenCalledTimes(0);
		expect(spyOnNextHandle).not.toHaveBeenCalled();
	});

	it('deve registrar um paciente e não chamar o próximo handler', async () => {
		const spyOnHandle = jest.spyOn(registrarPacienteHandler, 'handle');
		const dependtDto = new DependentDto(1);
		const spyOnNextHandle = jest.spyOn(nextHandlerMock, 'handle');

		await registrarPacienteHandler.handle(mockCensoDados, dependtDto);
		findPatientGateway.findByCodBeneficiary.mockReturnValue(null);

		expect(spyOnHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
		expect(findPatientGateway.findByCodBeneficiary).toHaveBeenCalledTimes(1);
		expect(insertPatientGateway.insertPatient).toHaveBeenCalledTimes(1);
		expect(spyOnNextHandle).not.toHaveBeenCalled();
	});

	it('deve registrar um paciente e chamar o próximo handler quando configurado', async () => {
		const spyOnNextHandle = jest.spyOn(nextHandlerMock, 'handle');
		const dependtDto = new DependentDto(patient);

		registrarPacienteHandler.setNext(nextHandlerMock);
		await registrarPacienteHandler.handle(mockCensoDados, dependtDto);
		expect(registrarPacienteHandler['next']).toBe(nextHandlerMock);
		expect(insertPatientGateway.insertPatient).toHaveBeenCalledTimes(2);

		expect(spyOnNextHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
	});
});
