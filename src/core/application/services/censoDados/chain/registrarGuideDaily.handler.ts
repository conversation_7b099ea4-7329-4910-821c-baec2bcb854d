import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideDailyService } from '../../guideDaily/insertGuideDaily.service';
import { GuideDaily } from 'src/core/domain/GuideDaily';

export class RegistrarGuideDailyHandler extends CensoDadosBaseHandler {
	constructor(private insertGuideDailyService: InsertGuideDailyService) {
		super();
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		const guideDaily = new GuideDaily(
			null,
			{ id: dependentDto.guideId },
			censoDados.acomodacao,
			null,
			censoDados.diariasAutorizadas,
			null,
			1,
			censoDados.userId,
			Number(censoDados.valorDiaria),
			null,
			1,
			censoDados.dataInternacao,
			censoDados.dataAlta,
			null,
			null,
			null,
			null,
			null,
		);
		const result =
			await this.insertGuideDailyService.insertGuideDaily(guideDaily);
		dependentDto.guideDailyId = result.id;
		await super.handle(censoDados, dependentDto);
		return;
	}
}
