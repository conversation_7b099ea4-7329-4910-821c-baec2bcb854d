import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { Patient } from 'src/core/domain/Patient';
import { RegistrarHospitalizationHandler } from './registrarHospitalization.handler';
import { DependentDto } from './dto/dependent.dto';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';
import { Hospitalization } from 'src/core/domain/Hospitalization';

describe('RegistrarPatientWayHandler', () => {
	const patient = {
		id: 1,
	} as Patient;
	const patientWay = 1;
	let mockCensoDados = new CensoDados(
		1,
		100,
		200,
		1,
		new Date('2023-01-01'),
		1,
		new Date('2023-02-01'),
		'São Paulo',
		'Hospital X',
		'Controle 123',
		new Date('1990-01-01'),
		new Date('2023-01-10'),
		new Date('2023-01-15'),
		'Alta Médica',
		'Diagnóstico Principal',
		'Diagnóstico Secundário',
		new Date('2023-01-20'),
		'Urgente',
		'Clínica',
		'GUIA123',
		'Ativo',
		'José da Silva',
		'**********',
		'São Paulo',
		'SP',
		false,
		'Tipo A',
		0,
		'SP-Regional',
		'Controle Tipo 1',
		10,
		'HOSP123',
		'PLANO123',
		'Plano de Saúde XYZ',
		'EMPRESA123',
		'Empresa Teste',
		'Ativo',
		new Date('2023-01-01'),
		null,
	);
	let insertPatientWayGateway: jest.Mocked<InsertHospitalizationGateway>;
	let registrarPatientWayHandler: RegistrarHospitalizationHandler;
	let registrarPatientWayNextHandler: RegistrarHospitalizationHandler;
	let findHospitalizationGateway: jest.Mocked<FindHospitalizationGateway>;
	let updateHospitalizationGateway: jest.Mocked<UpdateHospitalizationGateway>;

	beforeEach(() => {
		insertPatientWayGateway = {
			insert: jest.fn().mockResolvedValue(patientWay),
		} as jest.Mocked<InsertHospitalizationGateway>;

		findHospitalizationGateway = {
			findActiveHospitalizationByPatient: jest.fn(),
			findBeetweenPeriods: jest.fn(),
			findByAdmissionInAndPatient: jest.fn(),
			findByCodBeneficiario: jest.fn(),
			findByCodigoGuia: jest.fn(),
			findByNomeNascimento: jest.fn(),
		} as jest.Mocked<FindHospitalizationGateway>;

		updateHospitalizationGateway = {
			update: jest.fn(),
		} as jest.Mocked<UpdateHospitalizationGateway>;

		registrarPatientWayHandler = new RegistrarHospitalizationHandler(
			insertPatientWayGateway,
			findHospitalizationGateway,
			updateHospitalizationGateway,
		);
		registrarPatientWayNextHandler = new RegistrarHospitalizationHandler(
			insertPatientWayGateway,
			findHospitalizationGateway,
			updateHospitalizationGateway,
		);

		jest.resetAllMocks();
		jest.restoreAllMocks();
	});

	it('deve atualizar a internação caso ela já exista ', async () => {
		const spyOnHandle = jest.spyOn(registrarPatientWayHandler, 'handle');
		const dependtDto = new DependentDto(patient.id, patientWay);
		const spyOnNextHandle = jest.spyOn(
			registrarPatientWayNextHandler,
			'handle',
		);

		findHospitalizationGateway.findByAdmissionInAndPatient.mockResolvedValue({
			id: 1,
		} as unknown as Hospitalization);

		registrarPatientWayHandler.setNext(registrarPatientWayNextHandler);

		await registrarPatientWayHandler.handle(mockCensoDados, dependtDto);

		expect(spyOnHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
		expect(updateHospitalizationGateway.update).toHaveBeenCalled();
		expect(insertPatientWayGateway.insert).not.toHaveBeenCalled();
		expect(registrarPatientWayHandler['next']).toBe(
			registrarPatientWayNextHandler,
		);
		expect(spyOnNextHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
	});

	it('deve registrar uma internação e chamar o próximo handler quando configurado', async () => {
		const spyOnHandle = jest.spyOn(registrarPatientWayHandler, 'handle');
		const dependtDto = new DependentDto(patient.id, patientWay);
		const spyOnNextHandle = jest.spyOn(
			registrarPatientWayNextHandler,
			'handle',
		);

		registrarPatientWayHandler.setNext(registrarPatientWayNextHandler);

		await registrarPatientWayHandler.handle(mockCensoDados, dependtDto);

		expect(spyOnHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
		expect(insertPatientWayGateway.insert).toHaveBeenCalled();
		expect(registrarPatientWayHandler['next']).toBe(
			registrarPatientWayNextHandler,
		);
		expect(spyOnNextHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
	});

	it('nao deve registrar internacao caso data internação seja null', async () => {
		const spyOnHandle = jest.spyOn(registrarPatientWayHandler, 'handle');
		const dependtDto = new DependentDto(patient.id, patientWay);
		const spyOnNextHandle = jest.spyOn(
			registrarPatientWayNextHandler,
			'handle',
		);
		mockCensoDados = new CensoDados(
			1,
			100,
			200,
			1,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			null,
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			'**********',
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
		);
		registrarPatientWayHandler.setNext(registrarPatientWayNextHandler);

		await registrarPatientWayHandler.handle(mockCensoDados, dependtDto);

		expect(spyOnHandle).toHaveBeenCalledWith(mockCensoDados, dependtDto);
		expect(insertPatientWayGateway.insert).not.toHaveBeenCalled();
		expect(registrarPatientWayHandler['next']).toBe(
			registrarPatientWayNextHandler,
		);
		expect(spyOnNextHandle).not.toHaveBeenCalled();
	});
});
