import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertFormCidsService } from '../../formCids/InsertFormCids.service';
import { FormCids } from 'src/core/domain/FormCids';

export class RegistrarFormCidsHandler extends CensoDadosBaseHandler {
	constructor(private readonly insertFormCidsService: InsertFormCidsService) {
		super();
	}

	public setNext(handler: CensoDadosBaseHandler): void {
		super.setNext(handler);
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		const formCids = new FormCids(
			null,
			null,
			1,
			1,
			censoDados.userId,
			true,
			true,
			{ id: dependentDto.patientWay },
			null,
			null,
			null,
			null,
		);
		this.insertFormCidsService.insertFormCids(formCids);
		await super.handle(censoDados, dependentDto);
		return;
	}
}
