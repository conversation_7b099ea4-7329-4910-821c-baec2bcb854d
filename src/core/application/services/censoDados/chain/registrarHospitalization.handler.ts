import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertHospitalizationGateway } from 'src/core/application/gateway/hospitalization/insertHospitalization.gateway';
import { HospitalizationMapper } from 'src/shared/mapper/hospitalization/HospitalizationMapper';
import { PatientMapper } from 'src/shared/mapper/hospitalization/PatientMapper';
import { Patient } from 'src/core/domain/Patient';
import { FindHospitalizationGateway } from 'src/core/application/gateway/hospitalization/findHospitalization.gateway';
import { UpdateHospitalizationGateway } from 'src/core/application/gateway/hospitalization/updateHospitalization.gateway';

export class RegistrarHospitalizationHandler extends CensoDadosBaseHandler {
	constructor(
		private readonly insertHospitalizationGateway: InsertHospitalizationGateway,
		private readonly findHospitalizationGateway: FindHospitalizationGateway,
		private readonly updateHospitalizationGateway: UpdateHospitalizationGateway,
	) {
		super();
	}

	public setNext(handler: CensoDadosBaseHandler): void {
		super.setNext(handler);
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		if (!censoDados.dataInternacao) {
			console.info(
				`FLUXO DAS CADEIAS DE INSERCAO INTERROMPIDO O DADO censoDados.dataInternacao ESTA FALTANDO `,
			);
			return;
		}

		const patientWay = HospitalizationMapper.fromCensoDados(
			censoDados,
			PatientMapper.fromCensoDados(censoDados),
		);
		patientWay.patient = { id: dependentDto.patient } as Patient;

		const existPatientWay =
			await this.findHospitalizationGateway.findByAdmissionInAndPatient(
				censoDados.dataInternacao,
				patientWay.patient.id,
			);

		if (existPatientWay) {
			await this.updateHospitalizationGateway.update(existPatientWay.id, {
				admissionOut: censoDados.dataAlta,
				numeroGuia: censoDados.codigoGuia,
				admissionIn: censoDados.dataInternacao,
				carater: censoDados.caraterInternacao,
				altaPrevista: censoDados.previsaoAlta,
				caraterTipo: censoDados.tipoInternacao,
			});
			dependentDto.patientWay = existPatientWay.id;
			await super.handle(censoDados, dependentDto);
			return;
		}

		const createdPatientWayId =
			await this.insertHospitalizationGateway.insert(patientWay);
		dependentDto.patientWay = createdPatientWayId;
		await super.handle(censoDados, dependentDto);
	}
}
