import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarFormCidsHandler } from './registrarFormCids.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertFormCidsService } from '../../formCids/InsertFormCids.service';
import { FormCids } from 'src/core/domain/FormCids';

describe('RegistrarFormCidsHandler', () => {
	let registrarFormCidsHandler: RegistrarFormCidsHandler;
	let insertFormCidsService: jest.Mocked<InsertFormCidsService>;
	let nextHandlerMock: CensoDadosBaseHandler;

	const mockCensoDados: CensoDados = {
		dataInternacao: new Date('2023-03-10T00:00:00Z'),
		acomodacao: 'Acomodação Teste',
		userId: 42,
	} as CensoDados;

	beforeEach(() => {
		insertFormCidsService = {
			insertFormCids: jest.fn().mockResolvedValue(undefined),
		} as unknown as jest.Mocked<InsertFormCidsService>;

		registrarFormCidsHandler = new RegistrarFormCidsHandler(
			insertFormCidsService,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
			setNext: jest.fn(),
		} as unknown as CensoDadosBaseHandler;
	});

	it('deve chamar insertFormCidsService.insertFormCids com uma instância de FormCids e não chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.patientWay = 200;

		await registrarFormCidsHandler.handle(mockCensoDados, dependentDto);

		expect(insertFormCidsService.insertFormCids).toHaveBeenCalledTimes(1);
		const formCidsArg = insertFormCidsService.insertFormCids.mock.calls[0][0];
		expect(formCidsArg).toBeInstanceOf(FormCids);

		expect(formCidsArg.id).toBeNull();
		expect(formCidsArg.userId).toBe(mockCensoDados.userId);
		expect(formCidsArg.patientWay).toEqual({ id: dependentDto.patientWay });
	});

	it('deve chamar o próximo handler se este estiver configurado', async () => {
		const dependentDto = new DependentDto(100);
		dependentDto.patientWay = 200;
		registrarFormCidsHandler.setNext(nextHandlerMock);

		await registrarFormCidsHandler.handle(mockCensoDados, dependentDto);

		expect(insertFormCidsService.insertFormCids).toHaveBeenCalledTimes(1);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
