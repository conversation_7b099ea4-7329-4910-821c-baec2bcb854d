import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertBedHospitalService } from '../../bedHospital/insertBedHospital.service';
import { BedHospital } from 'src/core/domain/BedHospital';
import { FindBedHospitalService } from '../../bedHospital/findBedHospital.service';
import { UpdateBedHospitalService } from '../../bedHospital/updateBedHospital.service';

export class RegistrarBedHospitalHandler extends CensoDadosBaseHandler {
	constructor(
		private readonly insertBedHospitalService: InsertBedHospitalService,
		private readonly findBedHospitalService: FindBedHospitalService,
		private readonly updateBedHospitalService: UpdateBedHospitalService,
	) {
		super();
	}

	public async handle(
		censoDados: CensoDados,
		dependentDto: DependentDto,
	): Promise<void> {
		const bedHospital = new BedHospital(
			null,
			new Date(),
			censoDados.dataInternacao,
			censoDados.acomodacao,
			true,
			false,
			censoDados.valorDiaria,
			true,
			{ id: dependentDto.hospitalId },
			null,
			null,
			censoDados.dataAlta,
			dependentDto.patientWay,
			null,
			null,
			censoDados.acomodacao,
			censoDados.userId,
			null,
			null,
		);

		dependentDto.bedHospitalId = await this.upsertBedHospitalAndGetId(
			bedHospital,
			dependentDto,
		);
		await super.handle(censoDados, dependentDto);
	}

	private upsertBedHospitalAndGetId = async (
		bedHospital: BedHospital,
		dependentDto: DependentDto,
	): Promise<number> => {
		const existBed =
			await this.findBedHospitalService.findByAdmissionInAndHospitalization(
				bedHospital.admissionIn,
				dependentDto.patientWay,
			);

		if (existBed) {
			await this.updateBedHospitalService.update(existBed.id, {
				admissionOut: bedHospital.admissionOut,
				accommodation: bedHospital.accommodation,
				price: bedHospital.price,
			});
			return existBed.id;
		}

		const result =
			await this.insertBedHospitalService.insertBedHospital(bedHospital);

		return result.id;
	};
}
