import { CensoDados } from 'src/core/domain/CensoDados';
import { RegistrarGuideChainHandler } from './registrarGuideChain.handler';
import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { DependentDto } from './dto/dependent.dto';
import { InsertGuideService } from '../../guide/insertGuide.service';
import { Guide } from 'src/core/domain/Guide';

describe('RegistrarGuideChainHandler', () => {
	let registrarGuideChainHandler: RegistrarGuideChainHandler;
	let nextHandlerMock: CensoDadosBaseHandler;
	let insertGuideService: jest.Mocked<InsertGuideService>;

	const mockCensoDados: CensoDados = {
		dataInternacao: new Date('2023-03-10T00:00:00Z'),
		acomodacao: 'Acomodação Teste',
	} as CensoDados;

	beforeEach(() => {
		insertGuideService = {
			insertGuide: jest.fn().mockResolvedValue({ id: 123 }),
		} as unknown as jest.Mocked<InsertGuideService>;

		registrarGuideChainHandler = new RegistrarGuideChainHandler(
			insertGuideService,
		);

		nextHandlerMock = {
			handle: jest.fn().mockResolvedValue(undefined),
		} as unknown as CensoDadosBaseHandler;
	});

	it('deve chamar insertGuideService.insertGuide e atualizar dependentDto sem chamar próximo handler se este não estiver configurado', async () => {
		const dependentDto = new DependentDto(100);

		await registrarGuideChainHandler.handle(mockCensoDados, dependentDto);

		expect(insertGuideService.insertGuide).toHaveBeenCalledTimes(1);

		const guideArg = insertGuideService.insertGuide.mock.calls[0][0];
		expect(guideArg).toBeInstanceOf(Guide);

		expect(dependentDto.guideId).toBe(123);
	});

	it('deve chamar o próximo handler se configurado', async () => {
		const dependentDto = new DependentDto(100);
		registrarGuideChainHandler.setNext(nextHandlerMock);

		await registrarGuideChainHandler.handle(mockCensoDados, dependentDto);

		expect(insertGuideService.insertGuide).toHaveBeenCalledTimes(1);
		expect(dependentDto.guideId).toBe(123);
		expect(nextHandlerMock.handle).toHaveBeenCalledWith(
			mockCensoDados,
			dependentDto,
		);
	});
});
