import { InsertPatientGateway } from 'src/core/application/gateway/patient/insertPatient.gateway';
import { CensoDadosChainBuilder } from './abstract/censoDadosChain.builder';
import { CensoDadosChainBuilderImpl } from './censoDadosChain.builder.impl';
import { RegistrarPacienteHandler } from './registrarPaciente.handler';
import { FindPatientGateway } from 'src/core/application/gateway/patient/findPatient.gateway';

describe('CensoDadosChainBuilderImpl', () => {
	let censoDadosChainBuilder: CensoDadosChainBuilder;
	let insertPatientGateway: jest.Mocked<InsertPatientGateway>;
	let findPatientGateway: jest.Mocked<FindPatientGateway>;

	beforeEach(() => {
		insertPatientGateway = {
			insertPatient: jest.fn(),
		} as jest.Mocked<InsertPatientGateway>;
		findPatientGateway = {
			findByCodBeneficiary: jest.fn(),
			findByCodBeneficiaryAndNotName: jest.fn(),
			findByNameBirthAndNotCodBeneficiary: jest.fn(),
		} as jest.Mocked<FindPatientGateway>;
		censoDadosChainBuilder = new CensoDadosChainBuilderImpl();
	});

	it('deve criar uma cadeia de censoDados', () => {
		const registrarPacienteHandlerTopo = new RegistrarPacienteHandler(
			insertPatientGateway,
			findPatientGateway,
		);
		const registrarPacienteHandlerFundo = new RegistrarPacienteHandler(
			insertPatientGateway,
			findPatientGateway,
		);

		const createdChain = censoDadosChainBuilder
			.setNext(registrarPacienteHandlerTopo)
			.setNext(registrarPacienteHandlerFundo)
			.make();

		expect(createdChain['next']).toBe(registrarPacienteHandlerFundo);
	});

	it('nao deve criar uma cadeia de censoDados', () => {
		const createdChain = censoDadosChainBuilder.make();
		expect(createdChain).toBe(null);
	});
});
