import { CensoDadosBaseHandler } from './abstract/censoDadosBase.handler';
import { CensoDadosChainBuilder } from './abstract/censoDadosChain.builder';

export class CensoDadosChainBuilderImpl implements CensoDadosChainBuilder {
	private sequence: CensoDadosBaseHandler[] = [];

	public setNext(
		censoDadosBaseHandler: CensoDadosBaseHandler,
	): CensoDadosChainBuilder {
		this.sequence.push(censoDadosBaseHandler);
		return this;
	}
	public make(): CensoDadosBaseHandler {
		this.sequence.forEach((handler, index) => {
			if (index + 1 > this.sequence.length - 1) {
				return;
			}
			handler.setNext(this.sequence[index + 1]);
		});
		return this.sequence.length > 0 ? this.sequence[0] : null;
	}
}
