import { FindCensoDadosService } from './findCensoDados.service';
import { CensoDadosListagem } from '../../dto/CensoDadosListagem';
import { CensoDadosChainFactory } from './chain/factory/censoDadosChain.factory';
import { CensoDados } from 'src/core/domain/CensoDados';
import { DependentDto } from './chain/dto/dependent.dto';
import { ChangeCensoStatusService } from '../censo/changeCensoStatus.service';
import { ErrorOnCensoDadosProcessmentException } from 'src/shared/exceptions/rule/ErrorOnCensoDadosProcessment.exception';

export namespace ProcessCensoDadosService {
	export class ProcessCensoDadosService {
		constructor(
			private readonly findCensoDadosService: FindCensoDadosService.FindCensoDadosService,
			private readonly censoDadosChain: CensoDadosChainFactory,
			private readonly changeCensoStatus: ChangeCensoStatusService.ChangeCensoStatusService,
		) {}

		public async processCensoDados(censoId: number): Promise<void> {
			try {
				let page = 1;
				let total = 0;
				let totalQuantity = 0;
				do {
					const list = await this.findPaginatedCensoDados(censoId, page);
					page = list.page + 1;
					totalQuantity = list.totalQuantity;
					total = total + list.censoData.length;
					await this.executeRegisters(list.censoData);
				} while (total != totalQuantity);
				await this.changeCensoStatus.changeStatus(
					censoId.toString(),
					'concluido',
				);
			} catch (ex) {
				console.error(ex);
				throw new ErrorOnCensoDadosProcessmentException(
					500,
					'Um erro ocorreu ao enviar os dados do censo por favor contate o suporte',
				);
			}
		}

		private async findPaginatedCensoDados(
			censoId: number,
			page: number,
		): Promise<CensoDadosListagem> {
			return await this.findCensoDadosService.findListagem(
				censoId,
				{
					onlyError: false,
					onlyWarning: false,
					noRowsWithWarnings: true,
				},
				{
					limit: Number(process.env.GET_CENSO_DADOS_TO_SEND_LIMIT),
					page: page,
				},
			);
		}

		private async executeRegisters(censoDados: CensoDados[]): Promise<void> {
			if (censoDados.length === 0) {
				return;
			}
			const chain = this.censoDadosChain.createChain(censoDados[0].companyId);
			for (const data of censoDados) {
				await chain.handle(data, new DependentDto());
			}
		}
	}
}
