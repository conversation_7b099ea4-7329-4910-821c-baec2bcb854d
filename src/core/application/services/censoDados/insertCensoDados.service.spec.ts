import { CensoDados } from 'src/core/domain/CensoDados';
import { InsertCensoDadosGateway } from '../../gateway/censoDados/insertCensoDados.gateway';
import { InsertCensoDadosService } from './insertCensoDados.service';
import { TipoConflito } from 'src/core/domain/TipoConflito';
import { InsereCensoDados } from '../../dto/InsereCensoDados';
describe('InsertCensoDadosService', () => {
	let insertCensoDadosGateway: jest.Mocked<InsertCensoDadosGateway>;
	let insertCensoDadosService: InsertCensoDadosService.InsertCensoDadosService;
	let censoDadosMock: CensoDados;
	let insereCensoDados: InsereCensoDados;
	beforeEach(() => {
		insertCensoDadosGateway = {
			insert: jest.fn(),
		};
		censoDadosMock = new CensoDados(
			1,
			100,
			200,
			1,
			new Date('2023-01-01'),
			1,
			new Date('2023-02-01'),
			'São Paulo',
			'Hospital X',
			'Controle 123',
			new Date('1990-01-01'),
			new Date('2023-01-10'),
			new Date('2023-01-15'),
			'Alta Médica',
			'Diagnóstico Principal',
			'Diagnóstico Secundário',
			new Date('2023-01-20'),
			'Urgente',
			'Clínica',
			'GUIA123',
			'Ativo',
			'José da Silva',
			"'1234567890",
			'São Paulo',
			'SP',
			false,
			'Tipo A',
			0,
			'SP-Regional',
			'Controle Tipo 1',
			10,
			'HOSP123',
			'PLANO123',
			'Plano de Saúde XYZ',
			'EMPRESA123',
			'Empresa Teste',
			'Ativo',
			new Date('2023-01-01'),
			null,
		);
		insereCensoDados = new InsereCensoDados(censoDadosMock, [
			new TipoConflito(1, 'conflito'),
		]);
		insertCensoDadosService =
			new InsertCensoDadosService.InsertCensoDadosService(
				insertCensoDadosGateway,
			);
	});

	it('deve inserir um censo dados com conflito  1', () => {
		insertCensoDadosService.insert(insereCensoDados);
		expect(insereCensoDados.censoDados.conflito).toBe(1);
		expect(insertCensoDadosGateway.insert).toHaveBeenCalledWith(
			insereCensoDados,
		);
	});

	it('deve inserir um censo dados com conflito 0', () => {
		insereCensoDados.tiposConflito.pop();
		insertCensoDadosService.insert(insereCensoDados);
		expect(insereCensoDados.censoDados.conflito).toBe(0);
		expect(insertCensoDadosGateway.insert).toHaveBeenCalledWith(
			insereCensoDados,
		);
	});

	it('deve inserir um censo dados com conflito 0 se o tipoConflito for igual a null', () => {
		insereCensoDados = new InsereCensoDados(censoDadosMock, null);
		insertCensoDadosService.insert(insereCensoDados);
		expect(insereCensoDados.censoDados.conflito).toBe(0);
		expect(insertCensoDadosGateway.insert).toHaveBeenCalledWith(
			insereCensoDados,
		);
	});
});
