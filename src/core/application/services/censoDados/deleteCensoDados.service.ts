import { Injectable } from '@nestjs/common';
import { DeleteCensoDadosGateway } from '../../gateway/censoDados/deleteCensoDados.gateway';

@Injectable()
export class DeleteCensoDadosService {
	constructor(
		private readonly deleteCensoDadosGateway: DeleteCensoDadosGateway,
	) {}

	public async deleteBatch(censoDataIds: number[]): Promise<void> {
		await this.deleteCensoDadosGateway.deleteBatch(censoDataIds);
	}
}
