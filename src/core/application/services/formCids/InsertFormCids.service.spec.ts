import { InsertFormCidsGateway } from '../../gateway/formCids/insertFormCids.gateway';
import { FormCids } from 'src/core/domain/FormCids';
import { InsertFormCidsService } from './InsertFormCids.service';

describe('InsertFormCidsService', () => {
	let insertFormCidsService: InsertFormCidsService;
	let insertFormCidsGatewayMock: jest.Mocked<InsertFormCidsGateway>;

	beforeEach(() => {
		insertFormCidsGatewayMock = {
			insertFormCids: jest.fn(),
		} as unknown as jest.Mocked<InsertFormCidsGateway>;

		insertFormCidsService = new InsertFormCidsService(
			insertFormCidsGatewayMock,
		);
	});

	it('deve chamar InsertFormCidsGateway.insertFormCids com o objeto FormCids fornecido e retornar o resultado', async () => {
		const dummyFormCids = new FormCids(
			null,
			new Date('2023-01-01T00:00:00Z'),
			1,
			1,
			42,
			true,
			true,
			{ id: 200 },
		);
		const returnedFormCids = new FormCids(
			100,
			new Date('2023-01-01T00:00:00Z'),
			1,
			1,
			42,
			true,
			true,
			{ id: 200 },
		);

		insertFormCidsGatewayMock.insertFormCids.mockResolvedValue(
			returnedFormCids,
		);

		const result = await insertFormCidsService.insertFormCids(dummyFormCids);

		expect(insertFormCidsGatewayMock.insertFormCids).toHaveBeenCalledTimes(1);
		expect(insertFormCidsGatewayMock.insertFormCids).toHaveBeenCalledWith(
			dummyFormCids,
		);
		expect(result).toEqual(returnedFormCids);
	});
});
