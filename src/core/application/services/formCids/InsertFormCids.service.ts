import { FormCids } from 'src/core/domain/FormCids';
import { InsertFormCidsGateway } from '../../gateway/formCids/insertFormCids.gateway';

export class InsertFormCidsService {
	constructor(private InsertFormCidsGateway: InsertFormCidsGateway) {}

	public async insertFormCids(formCids: FormCids): Promise<FormCids> {
		const result = await this.InsertFormCidsGateway.insertFormCids(formCids);
		return result;
	}
}
