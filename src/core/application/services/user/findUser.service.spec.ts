import { FindUserService } from './findUser.service';
import { FindUserGateway } from '../../gateway/user/findUser.gateway';
import { User } from '../../../domain/User';
import { FindCompanyConfigService } from '../companyConfig/findCompanyConfig.service';
import { FindPermissionsService } from '../permissions/findPermissions.service';
import { CompanyConfig } from 'src/core/domain/CompanyConfig';
import { Permissions } from 'src/core/domain/Permissions';
import { UserPermissions } from 'src/core/domain/UserPermissions';

describe('FindUserService', () => {
	let findUserService: FindUserService.FindUserService;
	let findUserGateway: FindUserGateway;
	let findCompanyConfigService: FindCompanyConfigService;
	let findPermissionsService: FindPermissionsService;

	const mockCompanyConfigs: CompanyConfig = {
		['teste']: [{ id: 1, value: '1', enabled: true, name: '1' }],
	};

	const mockPermissions: Permissions = {
		['teste']: { view: true, insert: true, edit: true, delete: true },
	};

	const userPermissions = new UserPermissions(
		mockCompanyConfigs,
		mockPermissions,
	);

	beforeEach(() => {
		findUserGateway = {
			findById: jest.fn(),
			findOneOrFail: jest.fn(),
		};

		findCompanyConfigService = {
			findCompanyConfigByCompanyId: jest.fn(),
		} as unknown as FindCompanyConfigService;

		findPermissionsService = {
			findPermissionsByUserId: jest.fn(),
		} as unknown as FindPermissionsService;

		findUserService = new FindUserService.FindUserService(
			findUserGateway,
			findCompanyConfigService,
			findPermissionsService,
		);
	});

	it('should call findById on the gateway and return the result', async () => {
		const mockUsers: User = new User(2, 'Jane Doe', '<EMAIL>', '3');

		(findUserGateway.findById as jest.Mock).mockResolvedValue(mockUsers);

		const result = await findUserService.findUserById(0);

		expect(findUserGateway.findById).toHaveBeenCalled();
		expect(result).toEqual(mockUsers);
	});

	it('should return an empty result when no user are found', async () => {
		(findUserGateway.findById as jest.Mock).mockResolvedValue([]);

		const result = await findUserService.findUserById(0);

		expect(findUserGateway.findById).toHaveBeenCalled();
		expect(result).toEqual([]);
	});

	it('should handle errors thrown by the gateway', async () => {
		const errorMessage = 'Failed to fetch users';
		(findUserGateway.findById as jest.Mock).mockRejectedValue(
			new Error(errorMessage),
		);

		await expect(findUserService.findUserById(0)).rejects.toThrow(errorMessage);
		expect(findUserGateway.findById).toHaveBeenCalled();
	});

	it('should call findOneOrFail on the gateway and return the result', async () => {
		const mockUser: User = new User(3, 'John Doe', '<EMAIL>', '3');

		(findUserGateway.findOneOrFail as jest.Mock).mockResolvedValue(mockUser);

		const result = await findUserService.findOneOrFail({
			where: { email: mockUser.email },
		});

		expect(findUserGateway.findOneOrFail).toHaveBeenCalledWith({
			where: { email: mockUser.email },
		});
		expect(result).toEqual(mockUser);
	});

	it('should handle errors thrown by the gateway on findOneOrFail', async () => {
		const errorMessage = 'User not found';
		(findUserGateway.findOneOrFail as jest.Mock).mockRejectedValue(
			new Error(errorMessage),
		);

		await expect(
			findUserService.findOneOrFail({ where: { email: '<EMAIL>' } }),
		).rejects.toThrow(errorMessage);
		expect(findUserGateway.findOneOrFail).toHaveBeenCalledWith({
			where: { email: '<EMAIL>' },
		});
	});

	it('should get user with permissions', async () => {
		const userId = 1;

		(
			findPermissionsService.findPermissionsByUserId as jest.Mock
		).mockResolvedValue(mockPermissions);

		(
			findCompanyConfigService.findCompanyConfigByCompanyId as jest.Mock
		).mockResolvedValue(mockCompanyConfigs);

		const result = await findUserService.findUserPermissions(userId, 3);

		expect(findPermissionsService.findPermissionsByUserId).toHaveBeenCalledWith(
			userId,
		);
		expect(result).toEqual(userPermissions);
	});
});
