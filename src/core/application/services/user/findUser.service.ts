import { FindOneOptions } from 'typeorm';
import { User } from '../../../domain/User';
import { FindUserGateway } from '../../gateway/user/findUser.gateway';
import { UserPermissions } from 'src/core/domain/UserPermissions';
import { FindCompanyConfigService } from '../companyConfig/findCompanyConfig.service';
import { FindPermissionsService } from '../permissions/findPermissions.service';
export namespace FindUserService {
	export class FindUserService {
		constructor(
			private readonly findUserGateway: FindUserGateway,
			private readonly findCompanyConfigService: FindCompanyConfigService,
			private readonly findPermissionsGateway: FindPermissionsService,
		) {}

		public async findUserById(id: number): Promise<User> {
			return this.findUserGateway.findById(id);
		}

		public async findOneOrFail(options: FindOneOptions<User>): Promise<User> {
			return this.findUserGateway.findOneOrFail(options);
		}

		public async findUserPermissions(
			userId: number,
			companyId: number,
		): Promise<UserPermissions> {
			const companyConfig =
				this.findCompanyConfigService.findCompanyConfigByCompanyId(companyId);

			const permissions =
				this.findPermissionsGateway.findPermissionsByUserId(userId);

			return new UserPermissions(await companyConfig, await permissions);
		}
	}
}
