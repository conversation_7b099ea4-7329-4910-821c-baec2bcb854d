import { Tags } from 'src/core/domain/Tags';
import { FindTagsGateway } from '../../gateway/tags/findTags.gateway';

export class FindTagsService {
	constructor(private readonly findTagsGateway: FindTagsGateway) {}

	async searchTags(
		name: string,
		companyId: number,
		modulo: string,
		limit: number,
	): Promise<Tags[]> {
		return await this.findTagsGateway.searchTags(
			name,
			companyId,
			modulo,
			limit,
		);
	}
}
