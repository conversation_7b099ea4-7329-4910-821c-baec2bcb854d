import { FindTagsService } from './findTags.service';
import { FindTagsGateway } from '../../gateway/tags/findTags.gateway';
import { Tags } from 'src/core/domain/Tags';

describe('FindTagsService', () => {
	let findTagsService: FindTagsService;
	let findTagsGatewayMock: jest.Mocked<FindTagsGateway>;
	let tagsMock: Tags[];

	beforeEach(() => {
		findTagsGatewayMock = {
			searchTags: jest.fn(),
		} as unknown as jest.Mocked<FindTagsGateway>;

		findTagsService = new FindTagsService(findTagsGatewayMock);

		tagsMock = [
			{
				id: 1,
				tag: 'Test Tag',
				modulo: 'test',
				habilitado: true,
			},
		];
	});

	describe('searchTags', () => {
		it('deve chamar o gateway e retornar uma lista de tags', async () => {
			const name = 'Test';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			findTagsGatewayMock.searchTags.mockResolvedValue(tagsMock);

			const result = await findTagsService.searchTags(
				name,
				companyId,
				modulo,
				limit,
			);

			expect(findTagsGatewayMock.searchTags).toHaveBeenCalledWith(
				name,
				companyId,
				modulo,
				limit,
			);
			expect(result).toEqual(tagsMock);
		});

		it('deve retornar um array vazio quando nenhuma tag é encontrada', async () => {
			const name = 'NonExistent';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			findTagsGatewayMock.searchTags.mockResolvedValue([]);

			const result = await findTagsService.searchTags(
				name,
				companyId,
				modulo,
				limit,
			);

			expect(result).toEqual([]);
			expect(findTagsGatewayMock.searchTags).toHaveBeenCalledWith(
				name,
				companyId,
				modulo,
				limit,
			);
		});

		it('deve propagar erro quando gateway falhar', async () => {
			const name = 'Test';
			const companyId = 1;
			const modulo = 'test';
			const limit = 10;

			const errorMessage = 'Erro ao buscar tags';
			findTagsGatewayMock.searchTags.mockRejectedValue(new Error(errorMessage));

			await expect(
				findTagsService.searchTags(name, companyId, modulo, limit),
			).rejects.toThrow(errorMessage);

			expect(findTagsGatewayMock.searchTags).toHaveBeenCalledWith(
				name,
				companyId,
				modulo,
				limit,
			);
		});
	});
});
