import { FindEmpresaGateway } from '../../gateway/empresa/findEmpresa.gateway';
import { Empresa } from '../../../domain/Empresa';

export class FindEmpresaService {
	constructor(private readonly findEmpresaGateway: FindEmpresaGateway) {}

	public async searchEmpresa(
		nome: string,
		companyId: number,
		limit: number,
	): Promise<Empresa[]> {
		return await this.findEmpresaGateway.searchEmpresa(nome, companyId, limit);
	}
}
