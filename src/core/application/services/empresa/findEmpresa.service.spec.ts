import { FindEmpresaService } from './findEmpresa.service';
import { FindEmpresaGateway } from '../../gateway/empresa/findEmpresa.gateway';
import { Empresa } from '../../../domain/Empresa';

describe('FindEmpresaService', () => {
	let findEmpresaService: FindEmpresaService;
	let findEmpresaGateway: FindEmpresaGateway;

	beforeEach(async () => {
		findEmpresaGateway = {
			searchEmpresa: jest.fn(),
		};

		findEmpresaService = new FindEmpresaService(findEmpresaGateway);
	});

	describe('searchEmpresa', () => {
		it('should call searchEmpresa on the FindEmpresaGateway with the correct parameters and return the result', async () => {
			const nome = 'Test Company';
			const companyId = 123;
			const limit = 10;

			const mockEmpresas: Empresa[] = [
				new Empresa(1, 'Test Company 1', 'TC1', '12345678901234'),
				new Empresa(2, 'Test Company 2', 'TC2', '56789012345678'),
			];

			jest
				.spyOn(findEmpresaGateway, 'searchEmpresa')
				.mockResolvedValue(mockEmpresas);

			const result = await findEmpresaService.searchEmpresa(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockEmpresas);
			expect(findEmpresaGateway.searchEmpresa).toHaveBeenCalledWith(
				nome,
				companyId,
				limit,
			);
		});

		it('should call searchEmpresa on the FindEmpresaGateway with the nome null parameter and return the result', async () => {
			const nome: string = null;
			const companyId = 123;
			const limit = 10;

			const mockEmpresas: Empresa[] = [
				new Empresa(1, 'Test Company 1', 'TC1', '12345678901234'),
				new Empresa(2, 'Test Company 2', 'TC2', '56789012345678'),
			];

			jest
				.spyOn(findEmpresaGateway, 'searchEmpresa')
				.mockResolvedValue(mockEmpresas);

			const result = await findEmpresaService.searchEmpresa(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockEmpresas);
			expect(findEmpresaGateway.searchEmpresa).toHaveBeenCalledWith(
				nome,
				companyId,
				limit,
			);
		});
	});
});
