import { InsertGuideStatusGateway } from '../../gateway/guideStatus/insertGuideStatus.gateway';
import { GuideStatus } from 'src/core/domain/GuideStatus';
import { InsertGuideStatusService } from './insertGuideStatus.service';

describe('InsertGuideStatusService', () => {
	let insertGuideStatusService: InsertGuideStatusService;
	let insertGuideStatusGatewayMock: jest.Mocked<InsertGuideStatusGateway>;

	beforeEach(() => {
		insertGuideStatusGatewayMock = {
			insertGuideStatus: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideStatusGateway>;

		insertGuideStatusService = new InsertGuideStatusService(
			insertGuideStatusGatewayMock,
		);
	});

	it('deve chamar insertGuideStatusGateway.insertGuideStatus com o GuideStatus fornecido e retornar o resultado', async () => {
		const dummyGuideStatus = new GuideStatus(
			null,
			{ id: 1 },
			undefined,
			0, // status: 0 - aberto (valor default)
			undefined,
			'Observação de teste',
			new Date('2023-01-01T00:00:00Z'),
			new Date('2023-01-02T00:00:00Z'),
			1,
			123,
		);

		const returnedGuideStatus = new GuideStatus(
			789,
			{ id: 1 },
			undefined,
			0,
			undefined,
			'Observação de teste',
			new Date('2023-01-01T00:00:00Z'),
			new Date('2023-01-02T00:00:00Z'),
			1,
			123,
		);

		insertGuideStatusGatewayMock.insertGuideStatus.mockResolvedValue(
			returnedGuideStatus,
		);

		const result =
			await insertGuideStatusService.insertGuideService(dummyGuideStatus);

		expect(
			insertGuideStatusGatewayMock.insertGuideStatus,
		).toHaveBeenCalledTimes(1);
		expect(insertGuideStatusGatewayMock.insertGuideStatus).toHaveBeenCalledWith(
			dummyGuideStatus,
		);
		expect(result).toEqual(returnedGuideStatus);
	});
});
