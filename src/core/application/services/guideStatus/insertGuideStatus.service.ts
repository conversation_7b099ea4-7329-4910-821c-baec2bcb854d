import { GuideStatus } from 'src/core/domain/GuideStatus';
import { InsertGuideStatusGateway } from '../../gateway/guideStatus/insertGuideStatus.gateway';

export class InsertGuideStatusService {
	constructor(
		private readonly insertGuideStatusGateway: InsertGuideStatusGateway,
	) {}

	public async insertGuideService(
		guideStatus: GuideStatus,
	): Promise<GuideStatus> {
		return await this.insertGuideStatusGateway.insertGuideStatus(guideStatus);
	}
}
