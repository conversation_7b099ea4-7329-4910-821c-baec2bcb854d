import { CriptografiaService } from './criptografia.service';
import { CriptografiaGateway } from '../../gateway/criptografia/criptografia.gateway';

describe('CriptografiaService', () => {
	let criptografiaService: CriptografiaService;
	let criptografiaGateway: CriptografiaGateway;

	beforeEach(async () => {
		criptografiaGateway = {
			encrypt: jest.fn(),
			decrypt: jest.fn(),
		};

		criptografiaService = new CriptografiaService(criptografiaGateway);
	});

	describe('encrypt', () => {
		it('should call encrypt on the CriptografiaGateway with the correct parameters and return the result', () => {
			const plainText = 'text to encrypt';
			const encryptedText = 'encrypted text';

			jest.spyOn(criptografiaGateway, 'encrypt').mockReturnValue(encryptedText);

			const result = criptografiaService.encrypt(plainText);

			expect(result).toEqual(encryptedText);
			expect(criptografiaGateway.encrypt).toHaveBeenCalledWith(plainText);
		});
	});

	describe('decrypt', () => {
		it('should call decrypt on the CriptografiaGateway with the correct parameters and return the result', () => {
			const encryptedText = 'encrypted text';
			const decryptedText = 'decrypted text';

			jest.spyOn(criptografiaGateway, 'decrypt').mockReturnValue(decryptedText);

			const result = criptografiaService.decrypt(encryptedText);

			expect(result).toEqual(decryptedText);
			expect(criptografiaGateway.decrypt).toHaveBeenCalledWith(encryptedText);
		});
	});
});
