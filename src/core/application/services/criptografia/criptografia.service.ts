import { Injectable } from '@nestjs/common';
import { CriptografiaGateway } from '../../gateway/criptografia/criptografia.gateway';

@Injectable()
export class CriptografiaService {
	constructor(private readonly criptografiaGateway: CriptografiaGateway) {}

	public encrypt(text: string): string {
		return this.criptografiaGateway.encrypt(text);
	}
	public decrypt(encryptedText: string): string {
		return this.criptografiaGateway.decrypt(encryptedText);
	}
}
