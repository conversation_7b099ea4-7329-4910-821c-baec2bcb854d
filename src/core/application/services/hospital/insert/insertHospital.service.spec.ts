import { FindHospitalCompanyGateway } from 'src/core/application/gateway/hospital/findHospitalCompany.gateway';
import { InsertHospitalGateway } from 'src/core/application/gateway/hospital/insertHospitalGateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { Hospital } from 'src/core/domain/Hospital';
import { InsertHospitalService } from './insertHospital.service';
import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';
import { FindHospitalGateway } from '../../../gateway/hospital/findHospital.gateway';

describe('InsertHospitalService', () => {
	let insertHospitalService: InsertHospitalService;
	let findHospitalCompanyGateway: jest.Mocked<FindHospitalCompanyGateway>;
	let insertHospitalGateway: jest.Mocked<InsertHospitalGateway>;
	let findHospitalGateway: jest.Mocked<FindHospitalGateway>;
	let censoDados: CensoDados;

	beforeEach(() => {
		findHospitalCompanyGateway = {
			findHospitalCompanyByName: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalCompanyGateway>;

		insertHospitalGateway = {
			insertHospitalCompany: jest.fn(),
		} as unknown as jest.Mocked<InsertHospitalGateway>;

		findHospitalGateway = {
			findOneByName: jest.fn(),
		} as unknown as jest.Mocked<FindHospitalGateway>;

		insertHospitalService = new InsertHospitalService(
			findHospitalCompanyGateway,
			insertHospitalGateway,
			findHospitalGateway,
		);

		censoDados = {
			hospitalCredenciado: 'Hospital XYZ',
			companyId: 10,
			codigoEmpresa: 'Hosp456',
			codigoHospital: 'Hosp456',
		} as CensoDados;
	});

	it('deve retornar dados do hospital existente se encontrado', async () => {
		const fakeHospitalCompany = {
			id: 200,
			hospital: { id: 100 },
		} as HospitalsCompany;

		findHospitalCompanyGateway.findHospitalCompanyByName.mockResolvedValue(
			fakeHospitalCompany,
		);

		const result = await insertHospitalService.insertHospital(censoDados);

		expect(
			findHospitalCompanyGateway.findHospitalCompanyByName,
		).toHaveBeenCalledWith(
			censoDados.hospitalCredenciado,
			censoDados.companyId,
		);
		expect(result).toEqual({ hospitalId: 100, hospitalCompanyId: 200 });
		expect(insertHospitalGateway.insertHospitalCompany).not.toHaveBeenCalled();
	});

	it('deve inserir um novo hospital se não for encontrado e retornar os ids inseridos', async () => {
		findHospitalCompanyGateway.findHospitalCompanyByName.mockResolvedValue(
			null,
		);

		findHospitalGateway.findOneByName.mockResolvedValue(null);

		const fakeInserted = { hospitalId: 300, hospitalCompanyId: 400 };
		insertHospitalGateway.insertHospitalCompany.mockResolvedValue(fakeInserted);

		const result = await insertHospitalService.insertHospital(censoDados);

		expect(insertHospitalGateway.insertHospitalCompany).toHaveBeenCalled();
		const hospitalArg = (
			insertHospitalGateway.insertHospitalCompany as jest.Mock
		).mock.calls[0][0];
		expect(hospitalArg).toBeInstanceOf(Hospital);
		expect(hospitalArg.id).toBeNull();
		expect(hospitalArg.coUnidade).toBe(censoDados.codigoEmpresa);
		expect(hospitalArg.coCnes).toBe(censoDados.codigoHospital);

		expect(result).toEqual(fakeInserted);
	});
});
