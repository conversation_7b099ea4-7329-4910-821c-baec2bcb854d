import { Hospital } from 'src/core/domain/Hospital';
import { FindHospitalCompanyGateway } from '../../../gateway/hospital/findHospitalCompany.gateway';
import { InsertHospitalGateway } from '../../../gateway/hospital/insertHospitalGateway';
import { CensoDados } from 'src/core/domain/CensoDados';
import { FindHospitalGateway } from '../../../gateway/hospital/findHospital.gateway';

export class InsertHospitalService {
	constructor(
		private findHospitalCompanyGateway: FindHospitalCompanyGateway,
		private insertHospitalGateway: InsertHospitalGateway,
		private findHospitalGateway: FindHospitalGateway,
	) {}

	public async insertHospital(
		censoDados: CensoDados,
	): Promise<{ hospitalId: number; hospitalCompanyId: number }> {
		const hospitalsCompanies =
			await this.findHospitalCompanyGateway.findHospitalCompanyByName(
				censoDados.hospitalCredenciado,
				censoDados.companyId,
			);
		if (hospitalsCompanies) {
			return {
				hospitalId: hospitalsCompanies.hospital.id,
				hospitalCompanyId: hospitalsCompanies.id,
			};
		}

		let hospital = await this.findHospitalGateway.findOneByName(
			censoDados.hospitalCredenciado,
		);

		if (!hospital) {
			hospital = new Hospital(
				null,
				censoDados.codigoHospital,
				censoDados.codigoHospital,
				null,
				null,
				null,
				censoDados.hospitalCredenciado,
				censoDados.hospitalCredenciado,
			);
		}

		const insertedId = await this.insertHospitalGateway.insertHospitalCompany(
			hospital,
			censoDados.companyId,
		);
		return {
			hospitalId: insertedId.hospitalId,
			hospitalCompanyId: insertedId.hospitalCompanyId,
		};
	}
}
