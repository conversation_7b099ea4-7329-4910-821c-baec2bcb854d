import { FindHospitalService } from './findHospital.service';
import { FindHospitalCompanyGateway } from '../../../gateway/hospital/findHospitalCompany.gateway';
import { HospitalsCompany } from '../../../../domain/HospitalsCompany';

describe('FindHospitalService', () => {
	let findHospitalService: FindHospitalService;
	let findHospitalCompanyGateway: FindHospitalCompanyGateway;

	beforeEach(async () => {
		findHospitalCompanyGateway = {
			findHospitalCompanyByName: jest.fn(),
			searchHospitalCompany: jest.fn(),
			searchUnimed: jest.fn(),
		};

		findHospitalService = new FindHospitalService(findHospitalCompanyGateway);
	});

	describe('searchHospitalCompany', () => {
		it('should call searchHospitalCompany on the FindHospitalCompanyGateway with the correct parameters and return the result', async () => {
			const nome = 'Test Hospital';
			const companyId = 123;
			const limit = 10;

			const mockHospitals: HospitalsCompany[] = [
				{ id: 1, name: 'Test Hospital 1' } as HospitalsCompany,
				{ id: 2, name: 'Test Hospital 2' } as HospitalsCompany,
			];

			jest
				.spyOn(findHospitalCompanyGateway, 'searchHospitalCompany')
				.mockResolvedValue(mockHospitals);

			const result = await findHospitalService.searchHospitalCompany(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockHospitals);
			expect(
				findHospitalCompanyGateway.searchHospitalCompany,
			).toHaveBeenCalledWith(nome, companyId, limit);
		});
	});

	describe('searchUnimed', () => {
		it('should call searchUnimed on the FindHospitalCompanyGateway with the correct parameters and return the result', async () => {
			const nome = 'Test Unimed';
			const companyId = 123;
			const limit = 10;

			const mockHospitals: HospitalsCompany[] = [
				{ id: 1, name: 'Test Unimed 1' } as HospitalsCompany,
				{ id: 2, name: 'Test Unimed 2' } as HospitalsCompany,
			];

			jest
				.spyOn(findHospitalCompanyGateway, 'searchUnimed')
				.mockResolvedValue(mockHospitals);

			const result = await findHospitalService.searchUnimed(
				nome,
				companyId,
				limit,
			);

			// Assert
			expect(result).toEqual(mockHospitals);
			expect(findHospitalCompanyGateway.searchUnimed).toHaveBeenCalledWith(
				nome,
				companyId,
				limit,
			);
		});
	});
});
