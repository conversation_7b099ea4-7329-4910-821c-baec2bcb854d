import { FindHospitalCompanyGateway } from '../../../gateway/hospital/findHospitalCompany.gateway';
import { HospitalsCompany } from '../../../../domain/HospitalsCompany';

export class FindHospitalService {
	constructor(private findHospitalCompanyGateway: FindHospitalCompanyGateway) {}

	public async searchHospitalCompany(
		nome: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]> {
		return this.findHospitalCompanyGateway.searchHospitalCompany(
			nome,
			companyId,
			limit,
		);
	}

	public async searchUnimed(
		nome: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]> {
		return this.findHospitalCompanyGateway.searchUnimed(nome, companyId, limit);
	}
}
