import { FindPermissionsService } from './findPermissions.service';
import { FindPermissionsGateway } from '../../gateway/permissions/findPermissions.gateway';
import { Permissions } from 'src/core/domain/Permissions';

describe('FindPermissionsService', () => {
	let findPermissionsService: FindPermissionsService;
	let findPermissionsGateway: FindPermissionsGateway;

	const mockPermissions: Permissions = {
		['teste']: { view: true, insert: true, edit: true, delete: true },
	};

	beforeEach(() => {
		findPermissionsGateway = {
			findPermissionsByUserId: jest.fn(),
		};

		findPermissionsService = new FindPermissionsService(findPermissionsGateway);
	});

	describe('findPermissionsByUserId', () => {
		it('should call findPermissionsByUserId on the gateway with the correct parameters and return the result', async () => {
			const userId = 123;

			(
				findPermissionsGateway.findPermissionsByUserId as jest.Mock
			).mockResolvedValue(mockPermissions);

			const result =
				await findPermissionsService.findPermissionsByUserId(userId);

			expect(
				findPermissionsGateway.findPermissionsByUserId,
			).toHaveBeenCalledWith(userId);
			expect(result).toEqual(mockPermissions);
		});

		it('should throw an error when the gateway throws an error', async () => {
			const userId = 123;
			const errorMessage = 'Failed to fetch permissions';

			(
				findPermissionsGateway.findPermissionsByUserId as jest.Mock
			).mockRejectedValue(new Error(errorMessage));

			await expect(
				findPermissionsService.findPermissionsByUserId(userId),
			).rejects.toThrow(errorMessage);

			expect(
				findPermissionsGateway.findPermissionsByUserId,
			).toHaveBeenCalledWith(userId);
		});

		it('should return empty permissions when user id is not provided', async () => {
			const userId: number = null;
			const emptyPermissions = {};

			(
				findPermissionsGateway.findPermissionsByUserId as jest.Mock
			).mockResolvedValue(emptyPermissions);

			const result =
				await findPermissionsService.findPermissionsByUserId(userId);

			expect(
				findPermissionsGateway.findPermissionsByUserId,
			).toHaveBeenCalledWith(userId);
			expect(result).toEqual(emptyPermissions);
		});
	});
});
