import { Permissions } from 'src/core/domain/Permissions';
import { FindPermissionsGateway } from '../../gateway/permissions/findPermissions.gateway';

export class FindPermissionsService {
	constructor(
		private readonly findPermissionsGateway: FindPermissionsGateway,
	) {}
	public async findPermissionsByUserId(userId: number): Promise<Permissions> {
		return this.findPermissionsGateway.findPermissionsByUserId(userId);
	}
}
