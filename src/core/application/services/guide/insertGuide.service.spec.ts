import { InsertGuideGateway } from '../../gateway/guide/insertGuide.gateway';
import { Guide } from 'src/core/domain/Guide';
import { InsertGuideService } from './insertGuide.service';

describe('InsertGuideService', () => {
	let insertGuideService: InsertGuideService;
	let insertGuideGatewayMock: jest.Mocked<InsertGuideGateway>;

	beforeEach(() => {
		insertGuideGatewayMock = {
			insertGuide: jest.fn(),
		} as unknown as jest.Mocked<InsertGuideGateway>;

		insertGuideService = new InsertGuideService(insertGuideGatewayMock);
	});

	it('deve chamar insertGuideGateway.insertGuide com o guide fornecido e retornar o guide resultante', async () => {
		const guide = new Guide(null, { id: 1 }, { id: 2 }, { id: 3 });
		const returnedGuide = new Guide(123, { id: 1 }, { id: 2 }, { id: 3 });

		insertGuideGatewayMock.insertGuide.mockResolvedValue(returnedGuide);

		const result = await insertGuideService.insertGuide(guide);

		expect(insertGuideGatewayMock.insertGuide).toHaveBeenCalledTimes(1);
		expect(insertGuideGatewayMock.insertGuide).toHaveBeenCalledWith(guide);
		expect(result).toEqual(returnedGuide);
	});
});
