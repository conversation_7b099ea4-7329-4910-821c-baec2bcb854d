import { Censo } from 'src/core/domain/Censo';
import { CensoListagem } from 'src/core/application/dto/CensoListagem';
import { Paginacao } from 'src/core/application/dto/Paginacao';
import { OrdenacaoCensoListagem } from '../../dto/OrdenacaoCensoListagem';

export interface FindCensoGateway {
	findByCompanyId(companyId: number): Promise<Censo[]>;
	findListagem(
		companyId: number,
		query: Paginacao & OrdenacaoCensoListagem,
	): Promise<CensoListagem>;
	findByFileHash(hash: string): Promise<Censo>;
	findById(id: number): Promise<Censo>;
	findModalStateByUser(userId: number): Promise<Censo>;
}
