import { CensoDados } from 'src/core/domain/CensoDados';
import { CensoDadosListagem } from '../../dto/CensoDadosListagem';
import { Paginacao } from '../../dto/Paginacao';
import { CensoDadosFilters } from '../../dto/CensoDadosFilters';

export interface FindCensoDadosGateway {
	findByCensoId(censoId: number): Promise<CensoDados[]>;
	findListagem(
		censoId: number,
		filters: CensoDadosFilters,
		{ page, limit, search }: Paginacao,
	): Promise<CensoDadosListagem>;
}
