import { Patient } from 'src/core/domain/Patient';

export interface FindPatientGateway {
	findByNameBirthAndNotCodBeneficiary(
		name: string,
		birth: Date,
		companyId: number,
		codBeneficiary: string,
	): Promise<Patient>;
	findByCodBeneficiary(
		codBeneficiary: string,
		companyId: number,
	): Promise<Patient>;
	findByCodBeneficiaryAndNotName(
		name: string,
		codBeneficiario: string,
		companyId: number,
	): Promise<Patient>;
}
