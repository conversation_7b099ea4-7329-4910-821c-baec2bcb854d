import { expectedHeadersMinhaOperadora } from 'src/helpers/headers/expectedHeaderMinhaOperadora.helper';
import { GenerateModelCensoImpl } from './generateModel.factory.impl';
import { expectedHeadersSSU } from 'src/helpers/headers/expectedHeaderSSU.helper';
import { expectedHeadersUVX } from 'src/helpers/headers/expectedHeaderUVX.helper';
import { expectedHeadersSFO } from 'src/helpers/headers/expectedHeaderSFO.helper';

describe('ConflictValidationFactoryGatewayImpl', () => {
	let generateModelCensoImpl: GenerateModelCensoImpl;
	beforeEach(() => {
		generateModelCensoImpl = new GenerateModelCensoImpl();
	});

	const operadoras = [
		{ id: 3, expectedHeaders: expectedHeadersMinhaOperadora },
		{ id: 10, expectedHeaders: expectedHeadersSSU },
		{ id: 22, expectedHeaders: expectedHeadersUVX },
		{ id: 2, expectedHeaders: expectedHeadersSFO },
		{ id: 100, expectedHeaders: expectedHeadersMinhaOperadora },
	];

	it.each(operadoras)(
		'deve retornar um ConflictValidationGateway para a estratégia válida %s',
		(operadora) => {
			const result = generateModelCensoImpl.generateTemplateToFile(
				operadora.id,
			);
			expect(result).toEqual(operadora.expectedHeaders);
		},
	);
});
