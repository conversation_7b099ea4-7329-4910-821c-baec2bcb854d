import { expectedHeadersMinhaOperadora } from 'src/helpers/headers/expectedHeaderMinhaOperadora.helper';
import { ModelTemplateFactory } from './abstract/modelTemplate.factory';
import Operadoras from 'src/shared/operadoras.enum';
import { expectedHeadersSSU } from 'src/helpers/headers/expectedHeaderSSU.helper';
import { expectedHeadersUVX } from 'src/helpers/headers/expectedHeaderUVX.helper';
import { expectedHeadersSFO } from 'src/helpers/headers/expectedHeaderSFO.helper';

export class GenerateModelCensoImpl implements ModelTemplateFactory {
	generateTemplateToFile(companyId: number): string[] {
		switch (Number(companyId)) {
			case Operadoras.MINHA_OPERADORA:
				return expectedHeadersMinhaOperadora;
			case Operadoras.SEGUROS_UNIMED:
				return expectedHeadersSSU;
			case Operadoras.UNIMED_VITORIA:
				return expectedHeadersUVX;
			case Operadoras.SAO_FRANCISCO:
				return expectedHeadersSFO;
			default:
				return expectedHeadersMinhaOperadora;
		}
	}
}
