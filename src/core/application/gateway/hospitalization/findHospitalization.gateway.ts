import { Hospitalization } from 'src/core/domain/Hospitalization';
import { CensoDados } from '../../../domain/CensoDados';

export interface FindHospitalizationGateway {
	findByCodBeneficiario(censoDados: CensoDados): Promise<Hospitalization>;

	findByNomeNascimento(censoDados: CensoDados): Promise<Hospitalization>;

	findByCodigoGuia(
		companyId: number,
		codigoGuia: string,
	): Promise<Hospitalization>;

	findBeetweenPeriods(
		dataEntrada: Date,
		dataAlta: Date,
		patientId: number,
		companyId: number,
	): Promise<Hospitalization>;

	findActiveHospitalizationByPatient(
		patientId: number,
		companyId: number,
	): Promise<Hospitalization[]>;

	findByAdmissionInAndPatient(
		admissionIn: Date,
		patientId: number,
	): Promise<Hospitalization>;
}
