import { HospitalsCompany } from 'src/core/domain/HospitalsCompany';

export interface FindHospitalCompanyGateway {
	findHospitalCompanyByName(
		name: string,
		companyId: number,
	): Promise<HospitalsCompany>;

	searchHospitalCompany(
		name: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]>;

	searchUnimed(
		name: string,
		companyId: number,
		limit: number,
	): Promise<HospitalsCompany[]>;
}
