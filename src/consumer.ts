import { NestFactory } from '@nestjs/core';
import { OpenApiConfig } from './configuration/openapi.config';
import { HttpExceptionFilter } from './configuration/httpException.config.filter';
import { consumerModule } from './configuration/module/consumer.module';

async function bootstrap(): Promise<void> {
	const app = await NestFactory.create(consumerModule);
	OpenApiConfig.executeConfig(app);
	app.useGlobalFilters(new HttpExceptionFilter());

	const consumerMqttConfigImpl = app.get('ConsumerMqttConfig');
	consumerMqttConfigImpl.bindConsumers();

	await app.startAllMicroservices();
	await app.listen(5000);
	return;
}
bootstrap();
