import * as fs from 'fs';
export async function detectDelimiter(filePath: string): Promise<string> {
	return new Promise((resolve, reject) => {
		const readStream = fs.createReadStream(filePath, { encoding: 'utf-8' });
		let firstLine = '';

		readStream
			.on('data', (chunk: string) => {
				firstLine += chunk;
				readStream.close();
			})
			.on('close', () => {
				const delimiter = firstLine.includes(';') ? ';' : ',';
				resolve(delimiter);
			})
			.on('error', (err) => reject(err));
	});
}
