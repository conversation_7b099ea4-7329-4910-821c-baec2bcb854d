import * as fs from 'fs';
import * as crypto from 'crypto';
import { BadRequestException } from '@nestjs/common';

export async function encryptFile(path: string): Promise<string> {
	const hash = crypto.createHash('md5');

	const fileStream = fs.createReadStream(path);

	fileStream.on('data', (chunk) => {
		hash.update(chunk);
	});

	return new Promise<string>((resolve, reject) => {
		fileStream.on('end', () => {
			const md5Hash = hash.digest('hex');
			resolve(md5Hash);
		});

		fileStream.on('error', (err) => {
			reject(new BadRequestException(`Erro ao abrir arquivo: ${err.message}`));
		});
	});
}
