services:
    carefy-censo-backend:
        image: node:20.17-alpine
        container_name: carefy-censo-backend
        working_dir: /opt/src/app
        ports:
            - '5000:5000'
        volumes:
            - .:/opt/src/app
            - ./node_modules:/opt/src/app/node_modules
        environment:
            - NODE_ENV=dev
        command: sh -c "npm install -g pnpm && pnpm install --silent && pnpm run start:dev"
        networks:
            - carefy-network
        depends_on: 
            carefy-censo-rabbitmq:
                condition: service_healthy
                restart: true
    carefy-grafana:
        image: grafana/grafana-enterprise
        user: "root"
        container_name: carefy-grafana
        ports:
        - '5010:3000'
        volumes:
        - grafana-storage:/var/lib/grafana
        networks:
            - carefy-network
    carefy-prometheus:
        image: prom/prometheus
        volumes:
        - "./prometheus.yml:/etc/prometheus/prometheus.yml"
        ports:
        - 5020:9090
        networks:
            - carefy-network
    carefy-censo-rabbitmq:
        image: rabbitmq:3.9-management
        container_name: carefy-censo-rabbitmq
        ports:
            - '5674:5672'
            - '15674:15672'
        networks:
            - carefy-network    
        volumes:
        - ./rabbitmq/rabbitmq.conf:/etc/rabbitmq/rabbitmq.conf:ro
        - ./rabbitmq/definitions.json:/etc/rabbitmq/definitions.json:ro
        healthcheck:
            test: rabbitmq-diagnostics -q ping
            interval: 10s
            retries: 5
            start_period: 30s
            timeout: 10s
    censo-front:
        image: node:20.17-alpine
        container_name: carefy-censo-frontend
        working_dir: /app
        ports:
        - 5030:3000
        volumes:
        - ../carefy-censo-front:/app
        command: sh -c "npm install && npm run dev"
        environment:
        - NODE_ENV=development
        networks:
            - carefy-network
    carefy-censo-consumer:
        image: node:20.17-alpine
        container_name: carefy-censo-consumer
        working_dir: /opt/src/app
        volumes:
            - .:/opt/src/app
            - ./node_modules:/opt/src/app/node_modules
        environment:
            - NODE_ENV=dev
        command: sh -c "npm install -g pnpm && pnpm install --silent && pnpm run start:consumer:dev"
        ports:
          - 5040:5000
        networks:
            - carefy-network
        depends_on: 
            carefy-censo-rabbitmq:
                condition: service_healthy
                restart: true
    carefy-php-web:
        image: carefy-web:php7.4-2021.01.19
        container_name: carefy-php-web
        ports:
            - '8081:80'
        networks:
            - carefy-network
        volumes:
            - ../carefy/src/:/var/www/html
            - ../carefy/keys/:/keys
            - ../carefy/sa-logging/:/sa-logging
            - ../carefy/deploy/misc/config/upload.ini:/usr/local/etc/php/conf.d/upload.ini

networks:
    carefy-network:
        driver: bridge

volumes:
    grafana-storage:
