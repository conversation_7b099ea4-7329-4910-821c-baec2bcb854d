CREATE TABLE
    tipos_conflitos (
        id int (11) auto_increment NOT NULL,
        descricao varchar(255) NULL,
        CONSTRAINT tipos_conflitos_pk PRIMARY KEY (id)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_general_ci;

CREATE TABLE
    censo_dados (
        id int (11) unsigned auto_increment NOT NULL PRIMARY KEY,
        censo_id int (11) unsigned,
        data_expiracao TIMESTAMP,
        data_criacao TIMESTAMP,
        data_edicao TIMESTAMP,
        data_exclusao TIMESTAMP,
        user_id int (11) unsigned,
        company_id int (11) unsigned,
        conflito BOOLEAN,
        data TIMESTAMP,
        municipio VARCHAR(255),
        hospital_credenciado VARCHAR(255),
        controle VARCHAR(255),
        dt_nascimento TIMESTAMP,
        data_internacao TIMESTAMP,
        data_alta TIMESTAMP,
        acomodacao VARCHAR(255),
        motivo_alta VARCHAR(255),
        diagnostico VARCHAR(255),
        diagnostico_secundario VARCHAR(255),
        previsao_alta TIMESTAMP,
        carater_internacao VARCHAR(50),
        tipo_internacao VARCHAR(50),
        uti_na_internacao BOOLEAN,
        codigo_guia VARCHAR(255),
        alto_custo_status VARCHAR(50),
        nome_beneficiario VARCHAR(255),
        cod_beneficiario VARCHAR(255),
        cidade_beneficiario VARCHAR(255),
        estado_beneficiario CHAR(2),
        recem_nascido BOOLEAN,
        sexo CHAR(1),
        tipo_cliente VARCHAR(50),
        valor_diaria DECIMAL(10, 2),
        regional_beneficiario VARCHAR(255),
        tipo_controle VARCHAR(50),
        diarias_autorizadas INT,
        codigo_hospital VARCHAR(255),
        codigo_plano VARCHAR(255),
        nome_plano VARCHAR(255),
        codigo_empresa VARCHAR(255),
        nome_empresa VARCHAR(255),
        status_plano VARCHAR(50),
        data_plano_desde TIMESTAMP
    );

ALTER TABLE censo_dados ADD CONSTRAINT censos_dados_users_FK FOREIGN KEY (user_id) REFERENCES users (id);

ALTER TABLE censo_dados ADD CONSTRAINT censos_dados_company_FK FOREIGN KEY (company_id) REFERENCES company (id);

ALTER TABLE censo_dados ADD CONSTRAINT censos_dados_censo_rework_FK FOREIGN KEY (censo_id) REFERENCES censo_rework (id);

ALTER TABLE censo_dados MODIFY COLUMN data_edicao timestamp on update CURRENT_TIMESTAMP NULL;

ALTER TABLE censo_dados MODIFY COLUMN data_criacao timestamp DEFAULT CURRENT_TIMESTAMP NULL;

CREATE TABLE
    linhas_conflitadas_censo (
        id int (11) unsigned auto_increment NOT NULL,
        censo_dados_id int (11) unsigned NULL,
        tipo_conflito_id int (11) NULL,
        CONSTRAINT linhas_conflitadas_censo_pk PRIMARY KEY (id),
        CONSTRAINT linhas_conflitadas_censo_censo_dados_FK FOREIGN KEY (censo_dados_id) REFERENCES censo_dados (id),
        CONSTRAINT linhas_conflitadas_censo_tipos_conflitos_FK FOREIGN KEY (tipo_conflito_id) REFERENCES tipos_conflitos (id)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_general_ci;

ALTER TABLE censo_rework ADD nome_arquivo varchar(255) NULL;

INSERT INTO
    tipos_conflitos (id,descricao)
VALUES
    (
        1,
        'O código já existe para este beneficiário. Verifique também o status de habilitação do beneficiário.'
    ),
    (
        2,
        'A data de internação não pode ser superior à data de alta.'
    ),
    (
        3,
        'A data de nascimento do paciente está no futuro. Corrija essa informação.'
    ),
    (
        4,
        'Pacientes com o mesmo nome e data de nascimento, mas códigos diferentes. Verifique se há duplicidade no cadastro.'
    ),
    (
        5,
        'Já existe uma internação com esse código de guia.'
    ),
    (
        6
        'A data de entrada na internação não pode ser anterior à data de nascimento do paciente.'
    ),
    (
        7,
        'O período de internação (data de entrada e saída) conflita com uma internação já cadastrada para o mesmo paciente.'
    ),
    (
        8,
        'Para pacientes ainda internados (sem data de alta registrada), não é possível cadastrar uma internação futura. A data de entrada não pode ser maior do que a internação ainda em andamento.'
    );