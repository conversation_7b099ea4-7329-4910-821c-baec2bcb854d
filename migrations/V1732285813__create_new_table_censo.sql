CREATE TABLE
    censo_status (
        id varchar(100) NOT NULL,
        descricao varchar(255) NULL,
        cor varchar(100) NULL,
        design_status varchar(100) NULL,
        CONSTRAINT censo_status_pk PRIMARY KEY (id)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_general_ci;

CREATE TABLE
    censo_rework (
        id int (11) unsigned auto_increment NOT NULL,
        data_criacao TIMESTAMP DEFAULT CURRENT_TIMESTAMP NOT NULL,
        data_exclusao TIMESTAMP on update CURRENT_TIMESTAMP NULL,
        company_id int (11) unsigned NOT NULL,
        total_linhas INT NULL,
        user_id int (11) unsigned NULL,
        censo_status_id varchar(100) NULL,
        diretorio_salvo varchar(100) NULL,
        CONSTRAINT censo_rework_pk PRIMARY KEY (id),
        CONSTRAINT censo_rework_censo_status_FK FOREIGN KEY (censo_status_id) REFERENCES censo_status (id)
    ) ENGINE = InnoDB DEFAULT CHARSET = utf8 COLLATE = utf8_general_ci;

INSERT INTO
    censo_status (id, descricao, cor, design_status)
VALUES
    ('concluido', 'CONCLUÍDO', '#50F2B9', 'preenchido'),
    ('verificar', 'VERIFICAR', '#FF8383', 'preenchido'),
    ('processando', 'PROCESSANDO', '', ''),
    ('aguardando', 'AGUARDANDO', '', '');

ALTER TABLE censo_rework ADD CONSTRAINT censo_rework_users_FK FOREIGN KEY (user_id) REFERENCES users(id);
ALTER TABLE censo_rework ADD COLUMN hash_arquivo  VARCHAR(50) NULL;

ALTER TABLE censo_rework ADD data_edicao TIMESTAMP on update CURRENT_TIMESTAMP NULL;
ALTER TABLE censo_rework CHANGE data_edicao data_edicao TIMESTAMP on update CURRENT_TIMESTAMP NULL AFTER data_criacao;
