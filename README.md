## Setup do projeto

```bash
$ pnpm install
```

## run do projeto

```bash
# development
$ pnpm run start

# watch mode
$ pnpm run start:dev

# production mode
$ pnpm run start:prod
```

## testes

```bash
# unit tests
$ pnpm run test

# e2e tests
$ pnpm run test:e2e

# test coverage
$ pnpm run test:cov
```

## coverage 

### neste projeto o commit não é permitido com menos de 70% de cobertura de testes


## prometheus e grafana 

login default grafana: admin 
senha default grafana: admin
biblioteca node js do prometheus utilizada: https://github.com/willsoto/nestjs-prometheus
