{"rabbit_version": "3.9", "users": [{"name": "consumer", "password": "consumer", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": ""}, {"name": "producer", "password": "producer", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": ""}, {"name": "guest", "password": "guest", "hashing_algorithm": "rabbit_password_hashing_sha256", "tags": "administrator"}], "vhosts": [{"name": "/"}], "permissions": [{"user": "consumer", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}, {"user": "producer", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}, {"user": "guest", "vhost": "/", "configure": ".*", "write": ".*", "read": ".*"}], "parameters": [], "policies": [], "queues": [], "exchanges": [], "bindings": []}