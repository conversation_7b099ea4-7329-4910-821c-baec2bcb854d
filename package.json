{"name": "carefy-censo-back", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest --maxWorkers=4", "test:watch": "jest --watch --maxWorkers=4", "test:cov": "jest --coverage --maxWorkers=4", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "NODE_ENV=test jest --config ./test/jest-e2e.json", "pre-commit": "pnpm lint-staged", "prettier": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "prepare": "husky", "start:consumer": "nest start --entryFile consumer.js", "start:consumer:dev": "nest start --entryFile consumer.js --watch"}, "dependencies": {"@google-cloud/storage": "^7.14.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/microservices": "^10.4.12", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.4.13", "@nestjs/swagger": "^7.4.2", "@nestjs/typeorm": "^10.0.2", "@nestjs/websockets": "^10.4.13", "@types/amqplib": "^0.10.6", "@willsoto/nestjs-prometheus": "^6.0.1", "amqp-connection-manager": "^4.1.14", "amqplib": "^0.10.5", "bcrypt": "^5.1.1", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.4.5", "fast-csv": "^5.0.2", "multer": "1.4.5-lts.1", "mysql2": "^3.11.3", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "prom-client": "^15.1.3", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "typeorm": "^0.3.20", "uuid": "^11.0.3"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@swc/cli": "0.4.1-nightly.20240914", "@swc/core": "^1.7.26", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.13", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/passport-local": "^1.0.38", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.2.1", "husky": "^9.1.6", "jest": "^29.5.0", "lint-staged": "^15.2.10", "mockdate": "^3.0.5", "prettier": "^3.3.3", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node", "coverageThreshold": {"global": {"branches": 70, "functions": 70, "lines": 70, "statements": 70}}, "coveragePathIgnorePatterns": ["/node_modules/", "src/main.ts", "src/consumer.ts", "src/(.+?)\\.module\\.(t|j)s", "src/(.+?)\\.providers\\.(t|j)s", "src/(.+?)\\.entity\\.(t|j)s", "src/helpers/", "src/configuration/apis", "src/configuration/amqp", "src/configuration/socket", "src/configuration/providers", "src/shared/exceptions/", "src/(.+?)\\.dto\\.(t|j)s"], "moduleNameMapper": {"^src/(.*)$": "<rootDir>/$1"}}, "lint-staged": {"src/**/*.ts": ["prettier --write"], "*.{ts,js}": ["pnpm run lint"]}, "husky": {"hooks": {"pre-commit": "pnpm run pre-commit"}}, "packageManager": "pnpm@9.15.4+sha512.b2dc20e2fc72b3e18848459b37359a32064663e5627a51e4c74b2c29dd8e8e0491483c3abb40789cfd578bf362fb6ba8261b05f0387d76792ed6e23ea3b1b6a0"}