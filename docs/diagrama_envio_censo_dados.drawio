<mxfile host="app.diagrams.net" agent="Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" version="26.0.16">
  <diagram id="0WN17LLgHYXZRPudMleL" name="Page-1">
    <mxGraphModel dx="1368" dy="794" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
      <root>
        <mxCell id="0" />
        <mxCell id="1" parent="0" />
        <mxCell id="3" style="edgeStyle=none;html=1;" parent="1" source="2" target="4" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="380" y="130" as="targetPoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="2" value="Rogerinho" style="shape=umlActor;verticalLabelPosition=bottom;verticalAlign=top;html=1;" parent="1" vertex="1">
          <mxGeometry x="80" y="100" width="30" height="60" as="geometry" />
        </mxCell>
        <mxCell id="9" style="edgeStyle=none;html=1;" parent="1" source="4" target="7" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="23" style="edgeStyle=none;html=1;" parent="1" source="4" target="22" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="4" value="/v1/Send" style="shape=folder;fontStyle=1;spacingTop=10;tabWidth=40;tabHeight=14;tabPosition=left;html=1;" parent="1" vertex="1">
          <mxGeometry x="345" y="105" width="70" height="50" as="geometry" />
        </mxCell>
        <mxCell id="6" value="censo_id" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="195" y="105" width="70" height="30" as="geometry" />
        </mxCell>
        <mxCell id="7" value="carefy" style="shape=datastore;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="680" y="100" width="60" height="60" as="geometry" />
        </mxCell>
        <mxCell id="10" value="get paginado" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="515" y="105" width="90" height="30" as="geometry" />
        </mxCell>
        <mxCell id="13" value="fluxo de cadastro" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="410" y="210" width="110" height="30" as="geometry" />
        </mxCell>
        <mxCell id="19" style="edgeStyle=none;html=1;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="565" y="360" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="20" style="edgeStyle=none;html=1;entryX=1;entryY=0.5;entryDx=0;entryDy=0;" parent="1" edge="1">
          <mxGeometry relative="1" as="geometry">
            <mxPoint x="440" y="455" as="targetPoint" />
            <mxPoint x="510" y="455" as="sourcePoint" />
          </mxGeometry>
        </mxCell>
        <mxCell id="22" value="" style="rounded=0;whiteSpace=wrap;html=1;" parent="1" vertex="1">
          <mxGeometry x="45" y="390" width="670" height="300" as="geometry" />
        </mxCell>
        <mxCell id="27" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="24" target="26" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="24" value="«interface»&lt;br&gt;&lt;b&gt;RegisterPaciente&lt;br&gt;&lt;/b&gt;(patients)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="130" y="410" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="25" value="Cadeias de Registro" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="175" y="330" width="130" height="30" as="geometry" />
        </mxCell>
        <mxCell id="29" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="26" target="28" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="26" value="«interface»&lt;br&gt;&lt;b&gt;RegisterInternacao&lt;br&gt;&lt;/b&gt;(patient_way)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="325" y="410" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="31" value="" style="edgeStyle=none;html=1;" parent="1" source="28" target="30" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="28" value="«interface»&lt;br&gt;&lt;b&gt;RegisterLeito&lt;br&gt;&lt;/b&gt;(bed_hospital)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="515" y="410" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="34" value="" style="edgeStyle=none;html=1;" parent="1" source="30" target="33" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="30" value="«interface»&lt;br&gt;&lt;b&gt;RegisterEvolucao&lt;br&gt;&lt;/b&gt;(form_clinical)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="515" y="500" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="36" value="" style="edgeStyle=none;html=1;" parent="1" source="33" target="35" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="33" value="«interface»&lt;br&gt;&lt;b&gt;RegisterCid&lt;br&gt;&lt;/b&gt;(form_cids)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="325" y="500" width="110" height="50" as="geometry" />
        </mxCell>
        <mxCell id="40" value="" style="edgeStyle=none;html=1;" parent="1" source="35" target="39" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="35" value="«interface»&lt;br&gt;&lt;b&gt;RegisterProrrogacao&lt;br&gt;&lt;/b&gt;(guide)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="120" y="500" width="125" height="50" as="geometry" />
        </mxCell>
        <mxCell id="42" value="" style="edgeStyle=none;html=1;" parent="1" source="39" target="41" edge="1">
          <mxGeometry relative="1" as="geometry" />
        </mxCell>
        <mxCell id="39" value="«interface»&lt;br&gt;&lt;b&gt;RegisterDiariaProrrogacao&lt;br&gt;&lt;/b&gt;(guide_daily)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="105" y="610" width="160" height="50" as="geometry" />
        </mxCell>
        <mxCell id="41" value="«interface»&lt;br&gt;&lt;b&gt;RegisterDiariaProrrogacaoStatus&lt;br&gt;&lt;/b&gt;(guide_status)" style="html=1;" parent="1" vertex="1">
          <mxGeometry x="345" y="610" width="215" height="50" as="geometry" />
        </mxCell>
        <mxCell id="43" value="Uma company tem uma cadeia de registro" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" parent="1" vertex="1">
          <mxGeometry x="415" y="340" width="250" height="30" as="geometry" />
        </mxCell>
      </root>
    </mxGraphModel>
  </diagram>
</mxfile>
