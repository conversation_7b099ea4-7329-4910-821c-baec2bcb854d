<mxfile host="65bd71144e">
    <diagram id="IW3lgZdCHQUSAW0JUA9e" name="Page-1">
        <mxGraphModel dx="1255" dy="585" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="850" pageHeight="1100" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="26" value="Gateway" style="shape=folder;fontStyle=1;spacingTop=10;tabWidth=40;tabHeight=14;tabPosition=left;html=1;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="720" y="66" width="510" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="Core" style="shape=folder;fontStyle=1;spacingTop=10;tabWidth=40;tabHeight=14;tabPosition=left;html=1;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="250" y="60" width="395" height="210" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="" style="edgeStyle=none;html=1;" edge="1" parent="1" source="2" target="3">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="«interface»&lt;br&gt;&lt;b&gt;Controller&lt;/b&gt;" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="100" y="100" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="9" style="edgeStyle=none;html=1;exitX=0.5;exitY=1;exitDx=0;exitDy=0;" edge="1" parent="1" source="3" target="5">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="«interface»&lt;br&gt;&lt;b&gt;Servico&lt;/b&gt;" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="290" y="100" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="11" style="edgeStyle=none;html=1;entryX=0.5;entryY=1;entryDx=0;entryDy=0;" edge="1" parent="1" source="5" target="10">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="5" value="ServicoImpl" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="290" y="206" width="160" height="34" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="5">
                    <mxGeometry y="26" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="13" style="edgeStyle=none;html=1;entryX=-0.015;entryY=0.929;entryDx=0;entryDy=0;entryPerimeter=0;" edge="1" parent="1" source="10" target="15">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="730" y="170" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="10" value="«interface»&lt;br&gt;&lt;b&gt;Gateway&lt;/b&gt;" style="html=1;whiteSpace=wrap;" vertex="1" parent="1">
                    <mxGeometry x="480" y="100" width="110" height="50" as="geometry"/>
                </mxCell>
                <mxCell id="18" style="edgeStyle=none;html=1;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" edge="1" parent="1" source="14" target="21">
                    <mxGeometry relative="1" as="geometry">
                        <mxPoint x="935" y="164.0526315789474" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="14" value="GatewayImpl" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="780" y="128" width="160" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="+ field: type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="14">
                    <mxGeometry y="26" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="14">
                    <mxGeometry y="52" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="14">
                    <mxGeometry y="60" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="Repository" style="swimlane;fontStyle=1;align=center;verticalAlign=top;childLayout=stackLayout;horizontal=1;startSize=26;horizontalStack=0;resizeParent=1;resizeParentMax=0;resizeLast=0;collapsible=1;marginBottom=0;whiteSpace=wrap;html=1;" vertex="1" parent="1">
                    <mxGeometry x="1040" y="128" width="160" height="86" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="+ field: type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="20">
                    <mxGeometry y="26" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="22" value="" style="line;strokeWidth=1;fillColor=none;align=left;verticalAlign=middle;spacingTop=-1;spacingLeft=3;spacingRight=3;rotatable=0;labelPosition=right;points=[];portConstraint=eastwest;strokeColor=inherit;" vertex="1" parent="20">
                    <mxGeometry y="52" width="160" height="8" as="geometry"/>
                </mxCell>
                <mxCell id="23" value="+ method(type): type" style="text;strokeColor=none;fillColor=none;align=left;verticalAlign=top;spacingLeft=4;spacingRight=4;overflow=hidden;rotatable=0;points=[[0,0.5],[1,0.5]];portConstraint=eastwest;whiteSpace=wrap;html=1;" vertex="1" parent="20">
                    <mxGeometry y="60" width="160" height="26" as="geometry"/>
                </mxCell>
                <mxCell id="24" value="Adapter" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;" vertex="1" parent="1">
                    <mxGeometry x="645" y="128" width="70" height="30" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>