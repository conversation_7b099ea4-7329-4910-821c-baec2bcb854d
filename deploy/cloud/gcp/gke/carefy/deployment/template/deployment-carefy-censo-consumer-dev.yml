apiVersion: apps/v1
kind: Deployment
metadata:
  name: carefy-censo-consumer-dev
  namespace: default
  labels:
    app: carefy-censo-consumer-dev
spec:
  replicas: 1
  selector:
    matchLabels:
      app: carefy-censo-consumer-dev
  strategy:
    rollingUpdate:
      maxSurge: 2
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: carefy-censo-consumer-dev
    spec:
      containers:
        - image: $CAREFY_GCP_REGION-docker.pkg.dev/$CAREFY_GCP_PROJECT/$CAREFY_GCP_REPOSITORY/carefy-censo-consumer:$TAG
          imagePullPolicy: Always
          name: carefy-censo-consumer-dev
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          env:
            - name: DATABASE_HOST
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.db.host
            - name: DATABASE_USERNAME
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.db.user
            - name: DATABASE_PASSWORD
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.db.pass
            - name: DATABASE_NAME
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.db.name.shared
            - name: SECRET_JWT
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.censo.secret    
            - name: CAREFY_GCP_PROJECT
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.censo.project
            - name: CAREFY_GCP_BUCKET_NAME
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.censo.bucket
            - name: CAREFY_RABBIT_MQ_PRODUCER_USER
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key: carefy.rabbit.mq.producer.user
            - name: CAREFY_RABBIT_MQ_PRODUCER_PASS
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key:  carefy.rabbit.mq.producer.pass
            - name: CAREFY_RABBIT_MQ_HOST
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.rabbitmq.host
            - name: CAREFY_RABBIT_MQ_PORT
              valueFrom:
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.rabbitmq.port
            - name: CAREFY_RABBIT_MQ_CONSUMER_USER
              valueFrom: 
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.rabbit.mq.consumer.user
            - name: CAREFY_RABBIT_MQ_CONSUMER_PASS
              valueFrom: 
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.rabbit.mq.consumer.pass
            - name: CENSO_CONSUMER_RATE
              valueFrom: 
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.censo.amqp.rate
            - name: CENSO_CONSUMER_WORKERS
              valueFrom: 
                secretKeyRef:
                  name: carefy-all-dev
                  key:   carefy.censo.amqp.workers
            - name: GET_CENSO_DADOS_TO_SEND_LIMIT
              valueFrom: 
                secretKeyRef:
                  name: carefy-all-dev
                  key:  carefy.censo.dados.send.limit
          workingDir: /app
          ports:
            - containerPort: 5000
              protocol: TCP
          resources:
            requests:
              memory: "50Mi"
              cpu: "100m"
            limits:
              memory: "128Mi"
              cpu: "500m"
          livenessProbe:
            httpGet:
              path: /status
              port: 5000
            initialDelaySeconds: 300
            periodSeconds: 30
            timeoutSeconds: 30
            failureThreshold: 3
            successThreshold: 1
      volumes:
            - name: keys
              secret:
                secretName: carefy-sa-censo-ci-dev
