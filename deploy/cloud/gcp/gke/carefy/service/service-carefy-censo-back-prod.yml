apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-back-prod"}'
  labels:
    app: carefy-censo-back-prod
  name: carefy-censo-back-prod
  namespace: default
spec:
  selector:
    app: carefy-censo-back-prod
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
