apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-consumer-staging"}'
  labels:
    app: carefy-censo-consumer-staging
  name: carefy-censo-consumer-staging
  namespace: default
spec:
  selector:
    app: carefy-censo-consumer-staging
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
