apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-back-staging"}'
  labels:
    app: carefy-censo-back-staging
  name: carefy-censo-back-staging
  namespace: default
spec:
  selector:
    app: carefy-censo-back-staging
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
