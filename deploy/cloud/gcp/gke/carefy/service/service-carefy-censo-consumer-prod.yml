apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-consumer-prod"}'
  labels:
    app: carefy-censo-consumer-prod
  name: carefy-censo-consumer-prod
  namespace: default
spec:
  selector:
    app: carefy-censo-consumer-prod
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
