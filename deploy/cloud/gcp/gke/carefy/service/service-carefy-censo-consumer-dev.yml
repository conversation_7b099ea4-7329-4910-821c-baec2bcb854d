apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-consumer-dev"}'
  labels:
    app: carefy-censo-consumer-dev
  name: carefy-censo-consumer-dev
  namespace: default
spec:
  selector:
    app: carefy-censo-consumer-dev
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
