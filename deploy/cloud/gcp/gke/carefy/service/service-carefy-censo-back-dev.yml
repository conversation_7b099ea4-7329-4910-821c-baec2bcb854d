apiVersion: v1
kind: Service
metadata:
  annotations:
    cloud.google.com/neg: '{"ingress": true}'
    cloud.google.com/backend-config: '{"default": "carefy-censo-back-dev"}'
  labels:
    app: carefy-censo-back-dev
  name: carefy-censo-back-dev
  namespace: default
spec:
  selector:
    app: carefy-censo-back-dev
  ports:
    - protocol: TCP
      port: 5000
      targetPort: 5000
  sessionAffinity: None
  type: NodePort
